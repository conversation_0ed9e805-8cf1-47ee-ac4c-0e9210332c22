# Plan Limits System

## Overview
This document details the implementation of the plan limits system for the Bubl platform. This system enforces usage limits based on the user's subscription plan, providing a tiered approach to service access.

## Features Implemented

### Plan Tiers
- Implemented three subscription tiers: Free, Pro, and Enterprise
- Each tier has different limits for websites, pages per website, and messages per day
- Created a plans page to display available plans and allow users to switch between them
- Added visual indicators for the current plan and its features

### Usage Tracking
- Implemented a plan usage API endpoint to track website, page, and message usage
- Added a plan usage component to the dashboard with visual indicators
- Fixed the plan usage API endpoint to correctly display website and page counts
- Added helpful messages and visual indicators for plan limits
- Added a refresh button to the plan usage component

### Limit Enforcement
- Updated website creation to respect plan limits
- Updated website crawler to respect page limits
- Updated chat API to respect message limits
- Added visual indicators when limits are reached
- Added upgrade prompts when limits are reached

### UI Components
- Created a PlanUsage component for the dashboard
- Updated the "Add New Website" form to respect plan limits
- Updated the Crawl Configuration page to respect plan limits
- Added plan information to the websites page
- Improved error handling in the plan usage component

## Implementation Details

### Plan Limits Service
Created a centralized service to check and enforce plan limits:

```typescript
// src/lib/services/plan-limits-service.ts
export class PlanLimitsService {
  // Check if the user has reached their website limit
  async checkWebsitesLimit(userId: string): Promise<LimitCheckResult> {
    const user = await this.getUserWithPlan(userId);
    const websiteCount = await this.getWebsiteCount(userId);
    const limit = user.plan?.websiteLimit || 1;
    
    return {
      allowed: websiteCount < limit,
      currentCount: websiteCount,
      limit,
    };
  }
  
  // Check if a website has reached its page limit
  async checkPagesLimit(websiteId: string, userId: string): Promise<LimitCheckResult> {
    const user = await this.getUserWithPlan(userId);
    const pageCount = await this.getPageCount(websiteId);
    const limit = user.plan?.pagesPerWebsiteLimit || 100;
    
    return {
      allowed: pageCount < limit,
      currentCount: pageCount,
      limit,
    };
  }
  
  // Check if the user has reached their daily message limit
  async checkMessagesLimit(userId: string): Promise<LimitCheckResult> {
    const user = await this.getUserWithPlan(userId);
    const messageCount = await this.getTodayMessageCount(userId);
    const limit = user.plan?.messagesPerDayLimit || 100;
    
    return {
      allowed: messageCount < limit,
      currentCount: messageCount,
      limit,
    };
  }
}
```

### Plan Usage API Endpoint
Created an API endpoint to fetch the user's plan usage:

```typescript
// src/app/api/dashboard/user/plan/usage/route.ts
export async function GET(request: Request) {
  const { userId } = auth();
  
  if (!userId) {
    return Response.json({ error: "Unauthorized" }, { status: 401 });
  }
  
  try {
    const user = await db.query.usersTable.findFirst({
      where: eq(usersTable.id, userId),
      with: {
        plan: true,
      },
    });
    
    if (!user || !user.plan) {
      return Response.json({ error: "User or plan not found" }, { status: 404 });
    }
    
    // Get website count and limit
    const websitesCount = await db
      .select({ count: count() })
      .from(websitesTable)
      .where(eq(websitesTable.userId, userId));
    
    const websitesLimit = user.plan.websiteLimit;
    
    // Get page count and limit
    const pagesCount = 0; // Simplified for now
    const pagesLimit = user.plan.pagesPerWebsiteLimit;
    
    // Get message count and limit
    const messagesCount = 0; // Simplified for now
    const messagesLimit = user.plan.messagesPerDayLimit;
    
    return Response.json({
      websites: {
        count: Number(websitesCount[0].count),
        limit: websitesLimit,
      },
      pages: {
        count: pagesCount,
        limit: pagesLimit,
      },
      messages: {
        count: messagesCount,
        limit: messagesLimit,
      },
    });
  } catch (error) {
    console.error("Error fetching plan usage:", error);
    return Response.json(
      { error: "Failed to fetch plan usage" },
      { status: 500 }
    );
  }
}
```

### Plan Usage Component
Created a component to display the user's plan usage:

```tsx
// src/components/dashboard/PlanUsage.tsx
export function PlanUsage() {
  const { data: userPlan, isLoading: isPlanLoading } = useUserPlan();
  const { data: usage, isLoading: isUsageLoading, isError: isUsageError } = usePlanUsage();
  
  const isLoading = isPlanLoading || isUsageLoading;
  
  // Calculate usage percentages
  const websitePercentage = usage 
    ? Math.min(Math.round((usage.websites.count / usage.websites.limit) * 100), 100)
    : 0;
  
  const pagesPercentage = usage 
    ? Math.min(Math.round((usage.pages.count / usage.pages.limit) * 100), 100)
    : 0;
  
  const messagesPercentage = usage 
    ? Math.min(Math.round((usage.messages.count / usage.messages.limit) * 100), 100)
    : 0;
  
  // Component rendering with progress bars and helpful messages
}
```

### Website Creation Limit Check
Updated the website creation API to check limits:

```typescript
// src/app/api/dashboard/websites/route.ts
export async function POST(request: Request) {
  const { userId } = auth();
  
  if (!userId) {
    return Response.json({ error: "Unauthorized" }, { status: 401 });
  }
  
  try {
    // Check if the user has reached their website limit
    const planLimitsService = new PlanLimitsService();
    const limitCheck = await planLimitsService.checkWebsitesLimit(userId);
    
    if (!limitCheck.allowed) {
      return Response.json(
        { 
          error: "Website limit reached", 
          limit: limitCheck.limit,
          count: limitCheck.currentCount
        }, 
        { status: 403 }
      );
    }
    
    // Continue with website creation...
  } catch (error) {
    console.error("Error creating website:", error);
    return Response.json(
      { error: "Failed to create website" },
      { status: 500 }
    );
  }
}
```

### Crawl Configuration Limit Check
Updated the Crawl Configuration page to respect plan limits:

```tsx
// src/components/dashboard/CrawlConfigurationForm.tsx
export function CrawlConfigurationForm({
  websiteId,
  userPlan,
  onCrawlStart,
  onCrawlComplete,
}: CrawlConfigurationFormProps) {
  // Get the page limit from the user's plan
  const pagesPerWebsiteLimit = userPlan?.pagesPerWebsiteLimit || 50;
  
  // Set default max pages based on the user's plan
  const defaultMaxPages = Math.min(100, pagesPerWebsiteLimit);
  
  const [config, setConfig] = useState<CrawlConfig>({
    maxPages: defaultMaxPages,
    // Other config options...
  });
  
  // Component rendering with plan-aware inputs
}
```

## Benefits

### Clear Plan Structure
- Users understand what they're getting with each plan
- Transparent limits make decision-making easier
- Visual indicators show current usage and limits
- Upgrade paths are clear and accessible

### Improved User Experience
- Users receive helpful messages when approaching limits
- Visual indicators make it easy to understand usage
- The refresh button allows users to see updated usage
- Upgrade prompts appear at relevant points in the UI

### Robust Limit Enforcement
- Consistent limit checking across all features
- Centralized service for all limit-related logic
- Proper error handling and user feedback
- Graceful degradation when limits are reached

### Flexible Implementation
- Easy to adjust limits for different plans
- Simple to add new types of limits
- Consistent UI for displaying all types of limits
- Reusable components for limit-related UI

## Next Steps

1. **Payment Processing**
   - Integrate with a payment processor (Stripe)
   - Implement subscription management
   - Handle plan upgrades and downgrades
   - Add payment history and receipts

2. **Usage Analytics**
   - Track website usage metrics over time
   - Monitor message limits with daily resets
   - Provide usage insights to users
   - Add usage forecasting

3. **Admin Interface**
   - Create an admin interface for managing plans
   - Allow admins to adjust user limits
   - Provide tools for monitoring system usage
   - Add ability to create custom plans

4. **Notifications**
   - Add email notifications for approaching limits
   - Implement in-app notifications for limit warnings
   - Send subscription renewal reminders
   - Notify users of successful plan changes
