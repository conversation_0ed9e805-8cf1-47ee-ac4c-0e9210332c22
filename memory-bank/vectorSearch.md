# Vector Search Implementation

## Overview
This document outlines the implementation plan for the Website Content Ingestion feature (P0 priority in the PRD) and the vector search capabilities required for the Bubl platform. This feature enables the chatbot to understand and answer questions about website content.

## Database Schema Implementation

### WebsitePages Table
```typescript
export const websitePagesTable = pgTable("website_pages", {
  id,                                                  // UUID primary key
  websiteId: uuid("website_id").references(() => websitesTable.id).notNull(),
  url: varchar("url", { length: 2048 }).notNull(),
  title: varchar("title", { length: 512 }).notNull(),
  content: text("content").notNull(),
  createdAt,                                           // Timestamp
  updatedAt,                                           // Timestamp
  lastCrawledAt: timestamp("last_crawled_at", { withTimezone: true }).notNull(),
});
```

### Embeddings Tables
```typescript
// Custom pgvector type for Drizzle ORM
export const pgVector = customType<{
  data: number[];
  driverData: string;
  config: { dimensions: number };
}>({
  dataType(config) {
    return `vector(${config?.dimensions ?? 1024})`;
  },
  toDriver(value: number[]): string {
    return `[${value.join(",")}]`;
  },
  fromDriver(value: string): number[] {
    return value
      .substring(1, value.length - 1)
      .split(",")
      .map(Number);
  },
});

// Embeddings table
export const embeddingsTable = pgTable("embeddings", {
  id,                                                  // UUID primary key
  pageId: uuid("page_id").references(() => websitePagesTable.id).notNull(),
  chunk: text("chunk").notNull(),
  embedding: pgVector("embedding", { dimensions: 1024 }),
  createdAt,                                           // Timestamp
  updatedAt,                                           // Timestamp
});

// Website-specific embeddings table
export const websiteEmbeddingsTable = pgTable("website_embeddings", {
  id,                                                  // UUID primary key
  websiteId: uuid("website_id").references(() => websitesTable.id).notNull(),
  pageId: uuid("page_id").references(() => websitePagesTable.id).notNull(),
  chunkText: text("chunk_text").notNull(),
  embedding: pgVector("embedding", { dimensions: 1024 }),
  metadata: jsonb("metadata").default({}).notNull(),
  createdAt,                                           // Timestamp
  updatedAt,                                           // Timestamp
});
```

## Implementation Components

### 1. Website Crawler
- **Purpose**: Extract content from client websites
- **Implementation**:
  - Headless browser (Puppeteer) for JavaScript-rendered content
  - Browserless integration for Docker/server environments
  - Configurable crawl depth and frequency
  - Support for inclusion/exclusion patterns
  - Rate limiting to avoid overloading client websites
- **Status**: Implemented
  - Created WebsiteCrawler class with Puppeteer integration
  - Added Browserless support for Docker environments
  - Implemented configurable crawl depth and page limits
  - Added inclusion/exclusion pattern support
  - Implemented error handling and status updates

### 2. Content Processor
- **Purpose**: Extract and clean relevant content from crawled pages
- **Implementation**:
  - HTML parsing and cleaning with Cheerio
  - Main content extraction with DOM manipulation
  - Text chunking for optimal embedding generation
  - Metadata extraction (title, URL, etc.)
- **Status**: Implemented
  - Created content extraction methods in WebsiteCrawler
  - Implemented HTML cleaning to remove scripts, styles, and navigation
  - Added text normalization for consistent content
  - Integrated with database storage for processed content

### 3. Embedding Generator
- **Purpose**: Generate vector embeddings for website content
- **Implementation**:
  - Integration with MastrAI embedding models
  - Batch processing for efficiency
  - Error handling and retry mechanisms
  - Storage in pgvector-enabled PostgreSQL
- **Status**: Implemented
  - Using MastrAI's RAG capabilities for document processing and embedding generation
  - Implemented pgvector extension for PostgreSQL
  - Created custom vector type for Drizzle ORM
  - Set up IVFFLAT indexes for efficient similarity search

### 4. Vector Search API
- **Purpose**: Enable semantic search of website content
- **Implementation**:
  - Similarity search using pgvector
  - Relevance scoring and ranking
  - Context window optimization for chat responses
  - Caching for frequently accessed content
- **Status**: Implemented
  - Created WebsiteRagService for vector similarity search
  - Implemented cosine similarity search with pgvector
  - Added relevance scoring with similarity threshold
  - Example implementation:
  ```typescript
  // Search for similar content
  const results = await db.execute(sql`
    SELECT
      we.chunk_text as text,
      we.page_id as page_id,
      wp.url as url,
      wp.title as title,
      1 - (we.embedding <-> ${sql.raw(`'[${embedding.join(",")}]'::vector`)}) as similarity
    FROM
      website_embeddings we
    JOIN
      website_pages wp ON we.page_id = wp.id
    WHERE
      we.website_id = ${websiteId}
    ORDER BY
      we.embedding <-> ${sql.raw(`'[${embedding.join(",")}]'::vector`)}
    LIMIT ${limit}
  `);
  ```

### 5. Enhanced Website Context Tool
- **Purpose**: Provide relevant website content to the MastrAI agent
- **Implementation**:
  - Update the existing website context tool to use vector search
  - Implement query preprocessing for better matches
  - Add relevance filtering
  - Support for hybrid search (keyword + semantic)
- **Status**: Implemented
  - Updated websiteContextTool to use WebsiteRagService
  - Integrated with MastrAI's RuntimeContext for dynamic values
  - Added support for passing user's message as the query
  - Example implementation:
  ```typescript
  // In the website context tool
  const ragService = new WebsiteRagService();
  const similarContent = await ragService.searchSimilarContent(
    websiteId,
    query,
    5 // Limit to 5 results
  );

  // Format the content for the output
  content = similarContent.map((item) => ({
    title: item.title,
    text: item.text,
    url: item.url,
    similarity: item.similarity,
  }));
  ```

## Implementation Phases

### Phase 1: Database Schema and Basic Crawler (Week 1-2)
- Implement WebsitePages and Embeddings tables
- Create basic crawler with URL discovery
- Implement content extraction and cleaning
- Set up basic admin interface for crawler configuration

### Phase 2: Embedding Generation and Storage (Week 3-4)
- Implement embedding generation pipeline
- Set up pgvector extension and configuration
- Create batch processing system for embeddings
- Implement storage and indexing

### Phase 3: Vector Search and Integration (Week 5-6)
- Implement vector search API
- Enhance website context tool
- Integrate with chat system
- Add caching and performance optimizations

### Phase 4: Advanced Features and Testing (Week 7-8)
- Implement inclusion/exclusion patterns
- Add crawl scheduling and frequency configuration
- Create monitoring and error reporting
- Comprehensive testing with real websites

## pgvector Setup Process

We've implemented a streamlined process for setting up pgvector:

1. **Docker Compose Configuration**:
   - Updated to use the pgvector/pgvector:pg16 image
   - Ensures pgvector extension is available in the database

2. **Extension Enablement**:
   - Created a script to enable the pgvector extension
   - Added as `db:pgvector` npm script

3. **Vector Tables Creation**:
   - Created a script to set up vector tables with proper indexes
   - Added as `db:vector-tables` npm script

4. **Combined Setup**:
   - Added `db:setup` script that runs all necessary steps
   - Ensures proper order of operations

5. **Custom Vector Type**:
   - Implemented a custom pgVector type for Drizzle ORM
   - Handles conversion between JavaScript arrays and pgvector format

6. **Vector Indexes**:
   - Created IVFFLAT indexes for efficient similarity search
   - Added standard indexes for foreign key lookups

## Technical Considerations

### Performance Optimization
- Implement efficient chunking strategies for optimal retrieval
- Use appropriate vector dimensions and distance metrics
- Create indexes for frequently accessed data
- Implement caching for common queries

### Scalability
- Design for horizontal scaling of crawler components
- Implement queue-based processing for embedding generation
- Use batch operations for database interactions
- Consider sharding strategies for large websites

### Security
- Implement proper authentication for crawler requests
- Respect robots.txt and site policies
- Store credentials securely
- Implement rate limiting to avoid site overload

### Browserless Integration
- Use Browserless for headless browser capabilities in Docker environments
- Configure environment variables for flexible deployment options
- Implement fallback to local browser for development environments
- Add detailed logging for troubleshooting
- Support both self-hosted and cloud-based Browserless options

## Integration with Existing Components

### Chat API Integration
- Update the chat API to include relevant website context in responses
- Implement context window management for large websites
- Add source attribution for information used in responses

### Dashboard Integration
- Add website crawling status and metrics to dashboard
- Implement manual crawl triggering
- Provide content preview and search testing
- Add configuration options for crawl settings

## Success Criteria
- Successfully crawl and index websites with up to 10,000 pages
- Generate accurate embeddings for all content
- Retrieve relevant context for at least 90% of test queries
- Maintain crawl completion within 10 minutes for average sites
- Ensure vector search response time under 200ms
