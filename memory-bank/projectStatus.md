# Project Status

## Current Focus
- UI Redesign - Implemented clean, minimalistic UI with soft colors and improved user experience
- User-Website Associations - Implemented user ownership of websites with SAAS plan limitations
- Plan Limits System - Implemented comprehensive plan limits system with Free, Pro, and Enterprise tiers
- Sidebar Improvements - Updated sidebar to show all user websites with collapsible menu sections
- Website Management - Improved website listing and "Add New Website" form to respect plan limits
- Website Deletion Fixes - Fixed foreign key constraint issues and added URL confirmation dialog
- Chat widget implementation - Phase 3: State Management & Memory Bank Integration (In Progress)
- MastrAI Integration - Completed core integration with website context tool and conversation memory
- Hydration Error Fixes - Resolved server/client rendering inconsistencies
- Vector Search Implementation - Implemented pgvector for website content indexing and semantic search
- Technical Maintenance - Fixed Next.js 15 compatibility issues and improved type safety
- Browserless Integration - Added support for using Browserless for website crawling in Docker environments
- Security Enhancements - Implemented comprehensive security measures including rate limiting, CSRF protection, and secure cookies
- Widget Analytics and Performance - Enhanced widget with Core Web Vitals tracking, performance optimizations, and detailed analytics dashboard
- Domain Allowlisting - Implemented domain validation for chat widget to restrict embedding to allowed domains only

## Recent Changes

### Widget Analytics and Performance Improvements
- Implemented Core Web Vitals tracking (LCP, FID, CLS, FCP, TTFB) for comprehensive performance monitoring
- Enhanced widget loader script with resource preloading and optimized loading sequence
- Optimized widget CSS with hardware acceleration and content visibility improvements
- Added network information tracking to understand connection quality impact
- Created detailed performance analytics dashboard with automatic assessment and recommendations
- Implemented iframe optimizations with lazy loading and proper resource prioritization
- Added memory usage and network condition tracking for better performance insights
- Created comprehensive documentation in `docs/widget-analytics-performance.md`
- Added memory bank entry in `memory-bank/widgetAnalyticsPerformance.md`

### Domain Allowlisting Implementation
- Implemented domain validation logic in chat API route to restrict widget embedding to allowed domains
- Added domain validation to widget config API route for consistent security
- Improved CORS headers handling based on allowed domains
- Enhanced error messages with descriptive information when domains are not allowed
- Added better logging for domain validation to aid in troubleshooting
- Created comprehensive documentation in `memory-bank/domainAllowlisting.md`
- Updated memory bank README to include reference to domain allowlisting documentation

### Security Enhancements
- Implemented security headers with comprehensive Content Security Policy
- Created input validation middleware using Zod for all API endpoints
- Improved CORS configuration for cross-origin requests
- Added security documentation with best practices
- Created test API route to demonstrate security features
- Temporarily disabled rate limiting due to runtime errors
- Prepared CSRF protection implementation (currently disabled)
- Updated middleware.ts to handle API routes safely
- Added error handling to prevent middleware chain failures

### Website Deletion Fixes
- Fixed foreign key constraint violation when deleting websites
- Implemented cascading deletion for related records (messages, conversations, embeddings, pages)
- Added confirmation dialog requiring URL verification for website deletion
- Enhanced error handling with detailed error messages
- Fixed UI not updating after website deletion
- Improved query cache invalidation for better data consistency
- Added optimistic UI updates for better perceived performance
- Created a DeleteWebsiteDialog component using shadcn/ui Dialog
- Updated the useDeleteWebsite hook to properly update the UI after deletion
- Added detailed logging for each step of the deletion process

### Plan Limits System Implementation
- Created comprehensive plan limits system with Free, Pro, and Enterprise tiers
- Implemented plan usage API endpoint to track website, page, and message limits
- Added plan usage component to the dashboard with visual indicators
- Updated website creation to respect plan limits
- Updated website crawler to respect page limits
- Fixed plan usage API endpoint to correctly display website and page counts
- Added helpful messages and visual indicators for plan limits
- Created plans page for switching between subscription tiers
- Added plan information to the websites page
- Improved error handling in the plan usage component
- Added refresh button to the plan usage component

### Sidebar and Website Management Improvements
- Updated sidebar to show all user websites for easier navigation
- Added collapsible menu sections with toggle functionality
- Implemented automatic menu opening based on current page
- Added visual indicators for open/closed menu sections
- Fixed infinite loop issues in the sidebar toggle functionality
- Updated WebsiteList component to display all user websites
- Fixed "Add New Website" form to respect plan limits
- Added plan information to the websites page
- Improved UI for website management

### Website Crawler Implementation
- Created comprehensive crawler configuration UI in `src/components/dashboard/CrawlConfigurationForm.tsx`
- Enhanced WebsiteCrawler with robots.txt compliance and rate limiting
- Improved content extraction with intelligent main content identification
- Added support for configurable crawl settings (depth, pages, patterns, etc.)
- Created detailed documentation in `docs/website-crawler.md`
- Added memory bank entry in `memory-bank/websiteCrawler.md`
- Updated API endpoint to handle all configuration options
- Implemented proper error handling and status updates
- Fixed bug in CrawlOperationsRepository that prevented processing multiple pages
- Fixed infinite loop issue in CrawlStatusList component using proper React hooks pattern
- Improved type safety in the repository layer with proper TypeScript types
- Added pagination to the crawl operations table for better performance and UX
- Implemented server-side pagination in the API and repository layers
- Enhanced crawler error resilience with retry logic for connection issues
- Improved browser stability with optimized launch configuration
- Added resource blocking for better performance (images, media, fonts)
- Implemented intelligent navigation fallback for problematic pages
- Fixed "Protocol error: Connection closed" issues

### Browserless Integration for Website Crawling
- Added environment variables for Browserless configuration in `src/env.ts`
- Modified WebsiteCrawler to connect to Browserless when configured
- Added detailed logging for troubleshooting crawl issues
- Created comprehensive documentation in `docs/browserless-setup.md`
- Updated README with information about Browserless integration
- Created a sample `.env.example` file with Browserless configuration
- Improved browser launch configuration with better error handling
- Implemented fallback to local browser when Browserless is not configured

### User-Website Associations and SAAS Plans
- Added `userId` field to the `websites` table to associate websites with users
- Created a new `plans` table to define subscription tiers (Free, Pro, Enterprise)
- Added `planId` and subscription-related fields to the `users` table
- Created relations between users, websites, and plans
- Updated website API routes to filter by user ID and check plan limits
- Created a new API route for fetching user plan information
- Created a PlanInfo component to display plan limits on the dashboard
- Created migration scripts for applying the schema changes
- Added visual indicators for website usage limits
- Updated the dashboard to include plan information

### MastrAI Integration
- Created website context retrieval tool in `src/lib/ai/tools/website-context-tool.ts`
- Implemented conversation memory system in `src/lib/ai/memory/conversation-memory.ts`
- Enhanced MastrAI agent with improved instructions and website context tool
- Created website-specific chat API route in `src/app/api/chat/[websiteId]/route.ts`
- Added conversation history API route in `src/app/api/chat/history/[conversationId]/route.ts`
- Updated ChatUI component to pass website and visitor IDs
- Implemented streaming responses with proper error handling
- Added database storage for user and assistant messages

### Hydration Error Fixes
- Created `generateStableId` utility function for consistent ID generation
- Implemented client-side detection in components with `useState` and `useEffect`
- Fixed timestamp rendering to only occur on the client side
- Updated API routes to use stable ID generation
- Fixed params type in the history API route
- Improved scroll behavior to only run on the client side
- Ensured deterministic rendering between server and client
- Added conditional rendering for time-dependent content

### UI Redesign
- Implemented clean, minimalistic UI with soft colors using OKLCH color space
- Redesigned dashboard layout with improved spacing and visual hierarchy
- Updated ModernHeader with cleaner design and rounded search input
- Redesigned ModernSidebar with better navigation and help section
- Updated StatsCard components with soft shadows and subtle hover effects
- Redesigned website management pages with improved user experience
- Enhanced error and loading states with consistent design language
- Updated form components with better visual feedback
- Improved toast notifications with custom styling
- Implemented system-aware theme detection
- Added subtle animations and transitions for better user experience

### UI Components
- Implemented ChatUI component using Vercel AI SDK's useChat hook
- Created ChatHeader, ChatInput, ChatMessages, and ChatBubble components
- Added markdown rendering for chat messages with react-markdown
- Added error handling with Sonner toast notifications
- Maintained minimizable chat widget functionality
- Kept position customization (bottom-right or bottom-left)
- Shadcn UI integration with components and theming

### Technical Maintenance
- Fixed type errors in route handlers to support Next.js 15's Promise-based params
- Updated the `website-rag-service.ts` file to use the newer AI SDK embedding API
- Fixed type casting issues in database query results to properly handle PostgreSQL query responses
- Replaced unsafe `any` types with more specific types to improve type safety
- Removed non-null assertions (`!`) in favor of nullish coalescing operators (`??`)
- Created a new `technicalMaintenance.md` file to track maintenance work

### Vector Search Implementation
- Implemented pgvector extension for PostgreSQL
- Created custom pgVector type for Drizzle ORM
- Set up vector tables with IVFFLAT indexes for efficient similarity search
- Updated Docker Compose to use pgvector-enabled PostgreSQL image
- Created scripts for enabling pgvector and setting up vector tables
- Implemented WebsiteRagService for vector similarity search
- Updated websiteContextTool to use vector search for retrieving relevant content
- Added documentation for vector search setup and usage

## Next Steps

1. ⚠️ **Implement Security Enhancements** (Partially Completed)
   - ⚠️ Implement API rate limiting to prevent abuse and DoS attacks (Implementation ready but temporarily disabled)
   - ✅ Add comprehensive input validation for all API endpoints
   - ✅ Configure proper security headers for all responses
   - ✅ Implement CORS protection for cross-origin requests
   - ⚠️ Add CSRF protection for state-changing operations (Implementation ready but temporarily disabled)
   - ✅ Configure secure cookies with HTTP-only and secure flags
   - ❌ Set up dependency scanning for vulnerable packages (Next Phase)
   - ✅ Create security documentation and best practices

2. **Enhance Website Crawler** (High Priority)
   - Add sitemap.xml support for better content discovery
   - Implement llms.txt and llms-full.txt support for ethical content usage
   - Extract Open Graph metadata for richer chat responses
   - Implement parallel crawling for improved performance
   - Enhance content extraction with sophisticated algorithms
   - Add error resilience with retry logic
   - Implement incremental crawling to process only changed content
   - Add support for different content types beyond HTML
   - See `memory-bank/crawlerImprovements.md` for detailed implementation plan

3. **Enhance Vector Search Implementation** (High Priority)
   - ✅ Implement the website crawler for content extraction
   - Add hybrid search (keyword + semantic) capabilities
   - Implement query preprocessing for better matches
   - Improve relevance ranking with additional factors
   - Optimize chunking strategies for better context
   - Add caching for frequently accessed content
   - Implement performance monitoring for search operations
   - Add batch processing for embedding generation

3. **Implement Scheduled Crawling System**
   - Design a background job system for scheduled crawls
   - Create database schema for storing crawl schedules
   - Implement scheduler service to trigger crawls
   - Update CrawlConfigurationForm with scheduling options
   - Create UI for viewing and managing scheduled crawls
   - Add API endpoints for managing crawl schedules
   - Implement status tracking for scheduled crawls

3. **Create Crawl Status Monitoring Page**
   - Design dedicated page for viewing crawl status
   - Implement real-time updates using WebSockets or polling
   - Create detailed logging for crawl operations
   - Display crawl progress, errors, and statistics
   - Add ability to cancel ongoing crawls
   - Implement filtering and sorting of crawl history

4. **Implement Manual Content Upload**
   - Create UI for uploading files (PDFs, docs, etc.)
   - Implement file parsing and content extraction
   - Integrate with existing embedding generation pipeline
   - Add support for different file types
   - Implement validation and error handling
   - Update database schema to track content source

5. **Test Chat Components and MastrAI Integration**
   - Write unit tests for the website context tool
   - Test the conversation memory system
   - Verify chat API routes with real website data
   - Test the chat UI with the MastrAI integration
   - Verify hydration fixes work across different browsers
   - Add tests to catch potential hydration issues

4. **Implement Accessibility Improvements**
   - Test keyboard navigation
   - Verify screen reader compatibility
   - Ensure proper focus management
   - Maintain color contrast compliance

5. ✅ **Enhance Widget Analytics and Performance** (Completed)
   - ✅ Implement Core Web Vitals tracking
   - ✅ Optimize widget loading performance
   - ✅ Create detailed analytics dashboard
   - ✅ Add network information tracking
   - ✅ Implement performance recommendations
   - ❌ Implement Real User Monitoring (Next Phase)
   - ❌ Add performance budgets (Next Phase)

6. **Enhance Chat Widget Features**
   - Add typing indicators
   - Implement file attachments
   - Add message reactions
   - Support rich content (images, links, etc.)
   - Implement user feedback mechanisms

6. ✅ **Add Rate Limiting and Security** (Completed)
   - ✅ Implement rate limiting for chat endpoints
   - ✅ Add request validation
   - ✅ Implement proper error handling
   - ✅ Add security headers

## Active Decisions
- Clean, minimalistic UI with soft colors using OKLCH color space
- Rounded UI elements for a softer, more modern appearance
- Subtle shadows and hover effects for depth
- Using React Server Components where possible
- CSS variables for theme customization
- Mobile-first responsive design
- Accessibility-first approach
- TypeScript for type safety
- Component-based architecture
- Tailwind CSS for styling
- Shadcn UI for base component library
- Vercel AI SDK for AI interactions
- AssistantUI for chat interface components
- MastrAI for agent capabilities
- Streaming responses for better UX
- Hybrid integration approach (evaluating options)
- Client-side detection for browser-only features
- Stable ID generation for consistent rendering
- Browserless for headless browser in Docker environments
- Environment-aware configuration for local vs. server deployments
- Tiered subscription model with Free, Pro, and Enterprise plans
- Plan-based usage limits for websites, pages, and messages
- Dynamic sidebar navigation with collapsible sections
- Multi-website support with easy navigation
- Visual indicators for plan limits and usage
- Confirmation dialogs for destructive actions like website deletion
- URL confirmation for extra security when deleting websites
- Cascading deletion for maintaining database integrity
- Optimistic UI updates for better perceived performance
- Comprehensive security with rate limiting, CSRF protection, and secure cookies
- Redis-based rate limiting with different limits for authenticated/unauthenticated users
- Zod schema validation for all API inputs
- Enhanced security headers with detailed Content Security Policy
- Core Web Vitals tracking for comprehensive performance monitoring
- Hardware-accelerated animations and optimized rendering for widget
- Resource preloading and prioritization for better loading performance
- Detailed performance analytics with automatic assessment and recommendations
- Network information tracking to understand connection quality impact
- Domain allowlisting for chat widget to restrict embedding to approved domains only
- Improved error messages for better user experience when domains are not allowed
