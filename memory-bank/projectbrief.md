# Bubl Project Brief

## Project Overview
Bubl is a multi-tenant chat application that enables organizations to add AI-powered chat capabilities to their websites.

## Core Features
- Organization-based multi-tenancy using Clerk
- Website management per organization
- AI-powered chat interface
- Dashboard for analytics and management

## Authentication & Authorization
- Clerk-based authentication
- Organization membership management
- Role-based access control

## Type Safety
- Strong TypeScript implementation
- Custom type definitions for Clerk integration
- Type-safe API routes and components

## Key Requirements
1. Organizations must be created before accessing dashboard
2. Each organization must have at least one website
3. Type safety across all features
4. Secure authentication and authorization
5. Real-time chat capabilities

## Recent Improvements
- Enhanced type safety for Clerk organization memberships
- Improved error handling in API routes
- Streamlined setup flow for new users
- Build process optimization
- Implemented robust API rate limiting for chat endpoint

## Next Steps
1. Continue improving type coverage
2. Enhance error handling
3. Optimize build performance
4. Expand test coverage
5. Implement input validation for API endpoints (next security priority)

## Core Requirements
1. Website content ingestion and indexing
2. Embeddable chat widget with customization options
3. Management dashboard for configuration and analytics
4. Conversation analytics and reporting
5. Training and customization tools

## Project Goals
- Improve website user engagement
- Reduce support request volume
- Provide 24/7 automated website assistance
- Enable data-driven insights through analytics

## Target Users
- Primary: Website owners (businesses, marketing managers)
- Secondary: Website visitors (end users seeking information)

## Success Metrics
- User engagement with chat widget
- Customer satisfaction ratings
- Support ticket reduction
- Conversion rates 