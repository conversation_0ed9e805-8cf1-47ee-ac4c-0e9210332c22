# Technical Context

## Tech Stack
- Next.js 15 with App Router
- TypeScript
- Clerk Authentication
- PostgreSQL with <PERSON><PERSON>zle<PERSON><PERSON>
- Tailwind CSS
- Shadcn UI Components

## Environment Setup
- PNPM for package management
- Biome for linting and formatting
- <PERSON><PERSON> for git hooks
- <PERSON><PERSON><PERSON><PERSON><PERSON> for database migrations

## Type System
### Clerk Integration Types
```typescript
interface ClerkOrganization {
  id: string;
  name: string;
}

interface ClerkOrganizationMembership {
  organization: ClerkOrganization;
  role: string;
}

interface ClerkUserWithMemberships {
  organizationMemberships: ClerkOrganizationMembership[];
}
```

### Database Schema
- Organizations table with Clerk organization ID mapping
- Websites table linked to organizations
- Chat history and analytics tables

## API Routes
- `/api/user/memberships` - Organization membership management
- `/api/user/setup-status` - User onboarding flow
- `/api/chat` - Chat functionality
- `/api/dashboard/*` - Dashboard features

## Authentication Flow
1. Clerk handles user authentication
2. Organization membership verification
3. Database organization mapping
4. Website access control

## Build Process
- Type checking with TypeScript
- Code quality with Biome
- Next.js production build
- Automatic deployment via Vercel

## Development Practices
- Strong type safety enforcement
- API route error handling
- Environment variable validation
- Consistent code formatting
- Upstash Redis-backed API rate limiting integrated with Next.js middleware (Edge-compatible)
- Clerk userId or IP-based identification for multi-tenant safety (API handlers)
- Middleware checks for CSRF header presence on state-changing requests; full CSRF validation in API handlers
- Zod-based input validation for all API endpoints
- Standardized error responses and reusable validateRequest utility

## Recent Technical Decisions
1. Added custom type definitions for Clerk
2. Improved type safety in API routes
3. Enhanced error handling
4. Optimized build configuration
5. Adopted Upstash Redis-backed API rate limiting for scalable, multi-tenant protection in middleware
6. Middleware now checks for CSRF header presence; full validation in API handlers
7. Adopted Zod-based input validation and standardized error handling for API endpoints 