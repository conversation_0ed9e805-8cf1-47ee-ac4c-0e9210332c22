# Website Crawler Improvements

## Overview
This document outlines a comprehensive plan for enhancing the Bubl platform's website crawler with advanced features to improve content discovery, extraction quality, and overall performance.

## Planned Improvements

### 1. Sitemap.xml Support
- **Priority**: High
- **Status**: Planned
- **Description**: Add support for parsing sitemap.xml files to discover pages more efficiently
- **Implementation Details**:
  - Create a `fetchSitemap()` method to retrieve and parse sitemap.xml
  - Extract URLs from the sitemap and add them to the crawl queue
  - Support for sitemap index files that link to multiple sitemaps
  - Handle different sitemap formats (XML, gzipped, etc.)
- **Benefits**:
  - More comprehensive content discovery
  - Respect for website owner's content priorities
  - Better crawl efficiency by focusing on important pages first

### 2. llms.txt and llms-full.txt Support
- **Priority**: Medium
- **Status**: Planned
- **Description**: Implement support for the emerging llms.txt standard that specifies what content can be used for AI training and retrieval
- **Implementation Details**:
  - Create a `fetchLlmsTxt()` method to retrieve and parse llms.txt or llms-full.txt
  - Implement an `isAllowedByLlmsTxt()` method to check if a URL is allowed
  - Respect both Allow and Disallow directives
  - Integrate with the existing crawl process
- **Benefits**:
  - Ethical content usage in compliance with website owner preferences
  - Reduced legal risks
  - Better alignment with emerging AI content standards

### 3. Open Graph Metadata Extraction
- **Priority**: Medium
- **Status**: Planned
- **Description**: Extract Open Graph metadata to provide richer context for chat responses
- **Implementation Details**:
  - Create an `extractOpenGraphMetadata()` method to parse og: meta tags
  - Store metadata in the database alongside page content
  - Update the database schema to include a metadata field
  - Integrate with the chat system to display rich content
- **Benefits**:
  - Enhanced chat responses with images, descriptions, and titles
  - Better user experience when asking about products or blog posts
  - More accurate content representation

### 4. Parallel Crawling
- **Priority**: Medium
- **Status**: Planned
- **Description**: Implement parallel processing to improve crawling speed
- **Implementation Details**:
  - Create a `processPagesBatch()` method to handle multiple pages concurrently
  - Implement configurable batch sizes based on server capacity
  - Add proper concurrency controls to avoid overloading websites
  - Update the main crawl loop to use batching
- **Benefits**:
  - Faster crawling of large websites
  - More efficient use of system resources
  - Reduced overall crawl time

### 5. Smarter Content Extraction
- **Priority**: High
- **Status**: Planned
- **Description**: Improve content extraction with more sophisticated algorithms
- **Implementation Details**:
  - Enhance the `extractMainContent()` method with content density scoring
  - Calculate text-to-HTML ratio, link density, heading density, and paragraph density
  - Score elements based on these metrics to identify main content
  - Implement fallback mechanisms for different page structures
- **Benefits**:
  - Higher quality content extraction
  - Better handling of diverse website layouts
  - More accurate information for the chat system

### 6. Scheduled Crawling
- **Priority**: High
- **Status**: Planned
- **Description**: Add a scheduled crawling system to keep content fresh
- **Implementation Details**:
  - Create a `CrawlScheduler` class to manage scheduled crawls
  - Implement cron-based scheduling for different frequencies
  - Add database support for storing crawl schedules
  - Create UI components for managing schedules
- **Benefits**:
  - Automated content freshness
  - Reduced manual intervention
  - More up-to-date information for users

### 7. Crawl Status Monitoring
- **Priority**: Medium
- **Status**: Implemented and Fixed
- **Description**: Create a system to track and monitor crawl status
- **Implementation Details**:
  - Created a database schema for crawl operations (`crawlOperationsTable`)
  - Created a repository for crawl operations (`CrawlOperationsRepository`)
  - Updated the `WebsiteCrawler` to track crawl progress
  - Added methods to track pages processed, succeeded, and failed
  - Created an API endpoint for fetching crawl operations
  - Built a UI component (`CrawlStatusList`) for displaying crawl status
  - Integrated the crawl status UI into the website detail page
  - Fixed bug in `incrementField` method that prevented processing multiple pages
  - Improved type safety in the repository layer with proper TypeScript types
  - Fixed infinite loop issue in the `CrawlStatusList` component by using proper React hooks pattern
- **Benefits**:
  - Better visibility into crawl operations
  - Easier troubleshooting of crawl issues
  - Improved user experience for website owners
  - Real-time tracking of crawl progress
  - Stable and reliable crawling of multiple pages

### 8. Error Resilience
- **Priority**: High
- **Status**: Planned
- **Description**: Make the crawler more resilient to errors
- **Implementation Details**:
  - Implement retry logic with exponential backoff
  - Add better error handling and logging
  - Create a `processPageWithRetries()` method
  - Implement graceful degradation for partial failures
- **Benefits**:
  - More reliable crawling
  - Better handling of temporary network issues
  - Improved overall system stability

### 9. Incremental Crawling
- **Priority**: Medium
- **Status**: Implemented
- **Description**: Add support for incremental crawling to only process new or changed content
- **Implementation Details**:
  - Created a `hasPageChanged()` method to check for modifications
  - Implemented support for HTTP headers (Last-Modified, ETag) to detect changes
  - Enhanced the method with conditional GET requests using If-None-Match and If-Modified-Since headers
  - Updated the crawl process to skip unchanged pages
  - Added support for storing ETag values in page metadata
  - Added configuration option to enable/disable incremental crawling
- **Benefits**:
  - More efficient use of system resources
  - Faster crawl completion for large websites
  - Reduced load on client websites
  - Improved crawl performance by only processing changed content

### 10. Persistent Crawl Configuration Storage
- **Priority**: Medium
- **Status**: Implemented
- **Description**: Store crawl configurations for each website to remember settings between sessions
- **Implementation Details**:
  - Updated the database schema to include a `crawlConfiguration` field in the websites table
  - Added repository methods to save and retrieve crawl configurations
  - Created an API endpoint to get the saved configuration
  - Updated the CrawlConfigurationForm to load the saved configuration
  - Enhanced the crawl API endpoint to save the configuration when a crawl is started
- **Benefits**:
  - Improved user experience by remembering preferred settings
  - Consistency in recurring crawls
  - Reduced configuration errors
  - Time savings for users who frequently crawl the same websites

### 11. Content Type Support
- **Priority**: Low
- **Status**: Planned
- **Description**: Add support for different content types beyond HTML
- **Implementation Details**:
  - Create a `processContentByType()` method to handle different MIME types
  - Implement handlers for PDF, DOC, and other document formats
  - Add content extraction for different file types
  - Update the database schema to store content type information
- **Benefits**:
  - More comprehensive content coverage
  - Support for document-heavy websites
  - Better handling of diverse content sources

## Implementation Timeline

### Phase 1 (1-2 weeks)
- Implement Sitemap.xml Support
- Enhance Content Extraction
- Add Error Resilience

### Phase 2 (2-3 weeks)
- Implement Scheduled Crawling
- Add Crawl Status Monitoring
- Implement Open Graph Metadata Extraction

### Phase 3 (3-4 weeks)
- Implement Parallel Crawling
- Add llms.txt Support
- Implement Incremental Crawling

### Phase 4 (4-5 weeks)
- Add Content Type Support
- Final integration and testing
- Documentation updates

## Success Metrics

- **Crawl Completeness**: Increase in the percentage of website content successfully crawled
- **Content Quality**: Improvement in the relevance and accuracy of extracted content
- **Crawl Efficiency**: Reduction in crawl time and resource usage
- **Error Rate**: Decrease in crawl failures and errors
- **User Satisfaction**: Improvement in chat response quality based on better content

## Implementation Progress

### Completed
1. Implemented Crawl Status Monitoring
   - Created database schema for crawl operations
   - Developed repository and service for managing crawl operations
   - Updated WebsiteCrawler to track crawl progress
   - Built UI for displaying crawl status
2. Implemented Incremental Crawling
   - Created hasPageChanged method with ETag and Last-Modified support
   - Enhanced with conditional GET requests
   - Updated processPage method to skip unchanged pages
   - Added configuration option in the UI
3. Implemented Persistent Crawl Configuration Storage
   - Added crawlConfiguration field to websites table
   - Created repository methods for saving and retrieving configurations
   - Built API endpoint for getting saved configurations
   - Updated UI to load and use saved configurations

### Next Steps
1. Enhance Vector Search Implementation (High Priority)
   - Implement hybrid search combining vector search with keyword search
   - Add query preprocessing for better matches
   - Improve relevance ranking with additional factors
   - Optimize chunking strategies for better context preservation
   - Add caching for frequently accessed content
2. Implement Scheduled Crawling
   - Create database schema for crawl schedules
   - Develop scheduler service to trigger crawls at specified intervals
   - Build UI for managing crawl schedules
   - Create background job system for executing scheduled crawls
3. Create Comprehensive Crawl Status Monitoring Page
   - Design dedicated status page with detailed metrics
   - Implement real-time updates
   - Add visualizations for crawl history and performance
4. Implement Content Type Support
   - Add handlers for different file types (PDF, DOC, etc.)
   - Update database schema to store content type information
   - Enhance content extraction for different file formats
5. Continue enhancing existing features
   - Further improve sitemap.xml support
   - Enhance content extraction algorithms
   - Implement additional error resilience improvements
