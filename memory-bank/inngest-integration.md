# Inngest Integration for Background Jobs

## Overview

We've implemented Inngest for handling background jobs and scheduled tasks in Bubl, with a primary focus on the web crawler functionality. Inngest provides a durable execution engine that ensures reliable job processing, scheduling, and workflow management.

## Implementation Details

### Directory Structure

```
src/
  inngest/
    client.ts             # Inngest client instance
    functions/
      crawler.ts          # Website crawler functions
      index.ts            # Exports all functions
    README.md             # Documentation
```

### Key Components

1. **Inngest Client**: Created in `src/inngest/client.ts` to send and receive events.
2. **API Route**: Set up in `src/app/api/inngest/route.ts` to handle Inngest requests.
3. **Crawler Functions**:
   - `crawlWebsite`: Triggered by the `crawler/website.crawl` event to crawl a website.
   - `scheduledCrawl`: Scheduled function that runs daily to check for websites that need to be crawled based on their crawl frequency.
4. **WebsiteCrawler Modifications**: Updated to work with Inngest by adding support for the crawlOperationId.
5. **Development Tools**: Added scripts for running the Inngest dev server.
6. **Documentation**: Created comprehensive documentation in `docs/inngest-integration.md` and `src/inngest/README.md`.

### Crawler Functions

#### crawlWebsite

This function is triggered by the `crawler/website.crawl` event and handles the entire crawling process:

1. Gets the website from the database
2. Creates or gets a crawl operation
3. Updates the website status to CRAWLING
4. Performs the actual crawl using the WebsiteCrawler
5. Updates the website and operation status based on the result

#### scheduledCrawl

This function runs daily at midnight and checks for websites that need to be crawled based on their crawl frequency:

- **DAILY**: Crawled every day
- **WEEKLY**: Crawled on Sundays
- **MONTHLY**: Crawled on the 1st of each month
- **MANUAL**: Only crawled when manually triggered

The function includes enhanced features:
- **Retries**: Configured with 3 retries for reliability
- **Concurrency Limits**: Limited to 1 concurrent execution to prevent system overload
- **Detailed Logging**: Comprehensive logging of all operations
- **Status Tracking**: Tracks triggered, skipped, and failed crawls
- **Error Handling**: Robust error handling for each website crawl attempt
- **Skip Already Crawling**: Skips websites that are already being crawled

### API Route Updates

The `/api/dashboard/websites/[id]/crawl` API route was updated to trigger the Inngest event instead of running the crawler directly:

```typescript
// Trigger the crawl process using Inngest
await inngest.send({
  name: "crawler/website.crawl",
  data: {
    websiteId: id,
    configuration: completeConfig,
  },
});
```

## Benefits

1. **Reliability**: Ensures crawls are executed reliably, even during failures or outages.
2. **Scalability**: Handles multiple concurrent crawls and scales as needed.
3. **Observability**: Provides a dashboard for monitoring and troubleshooting.
4. **Scheduling**: Supports cron-based scheduling for recurring crawls.
5. **Step-based Workflows**: Enables complex workflows with multiple steps.
6. **Error Handling**: Provides built-in retry mechanisms and error tracking.

## Usage

### Development

1. Start the Inngest dev server:
   ```bash
   pnpm inngest:dev
   ```

2. Start the Next.js application:
   ```bash
   pnpm dev
   ```

3. Access the Inngest dev UI at http://localhost:8288

### Triggering a Crawl

```typescript
import { inngest } from "@/inngest/client";

await inngest.send({
  name: "crawler/website.crawl",
  data: {
    websiteId: "website-id",
    configuration: {
      maxPages: 100,
      maxDepth: 3,
      // other crawl options...
    },
  },
});
```

### Manual Trigger Function

We've also implemented a `triggerScheduledCrawl` function that allows manually triggering the scheduled crawl check:

```typescript
export const triggerScheduledCrawl = inngest.createFunction(
  { id: "trigger-scheduled-crawl" },
  { event: "crawler/schedule.trigger" },
  async ({ event, step, logger }) => {
    // Instead of trying to trigger the scheduled function directly,
    // we implement the same logic as the scheduled crawl function

    // Get websites by crawl frequency
    const dailyWebsites = await websitesRepository.getWebsitesByCrawlFrequency("DAILY");
    const weeklyWebsites = await websitesRepository.getWebsitesByCrawlFrequency("WEEKLY");
    const monthlyWebsites = await websitesRepository.getWebsitesByCrawlFrequency("MONTHLY");

    // Combine all websites to crawl
    const websitesToCrawl = [...dailyWebsites, ...weeklyWebsites, ...monthlyWebsites];

    // Trigger crawl for each website
    const results = [];
    for (const website of websitesToCrawl) {
      // Send event to trigger crawl
      await inngest.send({
        name: "crawler/website.crawl",
        data: {
          websiteId: website.id,
          configuration: website.crawlConfiguration || { /* default config */ },
        },
      });
    }

    return {
      success: true,
      crawlsTriggered: results.length,
      websites: results,
    };
  }
);
```

This function can be triggered by sending a `crawler/schedule.trigger` event to Inngest, which is useful for testing and for manually running the scheduled crawl outside of its normal schedule. Unlike the previous approach, this function directly implements the crawl scheduling logic rather than trying to trigger the cron job.

## Next Steps

1. **Production Deployment**:
   - Set up Inngest for production deployment
   - Configure environment variables for production
   - Implement proper authentication for Inngest webhooks

2. **Enhanced Error Handling**:
   - ✅ Added basic retry logic and error handling
   - Implement custom retry strategies for different types of failures
   - Add detailed error logging and reporting

3. **Monitoring and Alerting**:
   - ✅ Added basic logging for monitoring
   - Implement alerting for critical issues
   - Create dashboards for tracking crawl performance

4. **Performance Optimization**:
   - Optimize the crawler for better performance
   - Implement batching for large crawls
   - Add support for distributed crawling

5. **Additional Background Jobs**:
   - Implement other background jobs using Inngest
   - Add support for data processing and analytics
   - Implement scheduled report generation

6. **User Interface Enhancements**:
   - Add a UI for viewing crawl status and history
   - Implement controls for managing scheduled crawls
   - Add visualizations for crawl performance metrics
