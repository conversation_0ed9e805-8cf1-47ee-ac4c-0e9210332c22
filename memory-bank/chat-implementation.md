# Chat Implementation

## Implementation History

### Initial Chat Implementation
- Implemented a basic chat UI using Vercel AI SDK and custom components
- Created ChatUI, ChatHeader, ChatInput, ChatMessages, and ChatBubble components
- Used useChat hook from Vercel AI SDK for message handling
- Implemented server-side API routes for chat functionality

### AssistantUI Implementation Attempt
- Attempted to implement AssistantUI modal for chat
- Installed @assistant-ui/react and @assistant-ui/react-ai-sdk packages
- Created assistant-ui components (assistant-modal.tsx, thread.tsx, tooltip-icon-button.tsx)
- Created AssistantChatProvider component to manage chat state
- Encountered issues with the implementation:
  - "Unknown event handler property `onReceiveMessage`" error
  - "Status 400: {"error":"websiteId is required"}" error
  - Integration issues with existing codebase

### Rollback to Previous Implementation
- Rolled back to the previous chat implementation due to issues with AssistantUI
- Created SimpleChatWidget component that uses the existing ChatUI component
- Created ChatToggleButton component for toggling the chat widget
- Updated references in playground and assistant-demo pages
- Removed AssistantUI components and dependencies
- Fixed build errors related to missing modules

### Vector Search Implementation
- Created vector-search.ts module to implement searchSimilarContent function
- Integrated with WebsiteRagService for vector similarity search
- Fixed type errors in API route handlers (params as Promise)
- Ensured compatibility with Next.js 15 route handler conventions

## Current Implementation

### Chat Components
- **ChatUI**: Main chat interface component
- **ChatHeader**: Header component with title and status
- **ChatInput**: Input component for user messages
- **ChatMessages**: Component to display chat messages
- **ChatBubble**: Component to display individual messages
- **ChatToggleButton**: Button to toggle the chat widget
- **SimpleChatWidget**: Wrapper component for ChatUI with client-side rendering

### API Routes
- **/api/chat/[websiteId]/route.ts**: Main chat API route with MastrAI integration
- **/api/chat-simple/[websiteId]/route.ts**: Simplified chat API route with vector search

### Vector Search
- **vector-search.ts**: Module for searching similar content using vector embeddings
- Integrated with WebsiteRagService for semantic search capabilities
- Used in chat-simple API route for retrieving relevant content

## Technical Details

### Client-Side Implementation
```tsx
// SimpleChatWidget.tsx
"use client"

import { useEffect, useState } from "react"
import { ChatUI } from "./ChatUI"

export function SimpleChatWidget({
  websiteId,
  position = "bottom-right",
  title = "Chat Assistant",
  primaryColor = "#4F46E5",
  secondaryColor = "#FFFFFF",
  welcomeMessage = "Hi there! How can I help you today?",
  initiallyOpen = false,
}: SimpleChatWidgetProps) {
  const [mounted, setMounted] = useState(false)

  // Set mounted to true after the component mounts to avoid hydration issues
  useEffect(() => {
    setMounted(true)
  }, [])

  // Only render the content after the component has mounted on the client
  if (!mounted) {
    return null
  }

  return (
    <ChatUI
      websiteId={websiteId}
      position={position}
      title={title}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      initiallyOpen={initiallyOpen}
      initialMessages={
        welcomeMessage
          ? [
              {
                role: "assistant",
                content: welcomeMessage,
              },
            ]
          : []
      }
    />
  )
}
```

### Server-Side Implementation
```typescript
// vector-search.ts
import { WebsiteRagService } from "@/lib/services/rag/website-rag-service";

export async function searchSimilarContent(
  websiteId: string,
  query: string,
  limit = 5
): Promise<Array<{
  content: string;
  url: string;
  title: string;
  similarity: number;
}>> {
  try {
    // Create an instance of the WebsiteRagService
    const ragService = new WebsiteRagService();

    // Search for similar content
    const results = await ragService.searchSimilarContent(
      websiteId,
      query,
      limit
    );

    // Map the results to the expected format
    return results.map(result => ({
      content: result.text,
      url: result.url,
      title: result.title,
      similarity: result.similarity
    }));
  } catch (error) {
    console.error("Error in searchSimilarContent:", error);
    // Return an empty array instead of throwing to avoid breaking the API
    return [];
  }
}
```

## Analytics Implementation

### Chat Analytics
- **Event Tracking**: Implemented tracking for various chat events:
  - `conversation_started`: When a chat session begins
  - `message_sent`: When a user sends a message
  - `message_received`: When the assistant responds
  - `chat_opened`: When the chat widget is opened
  - `chat_minimized`: When the chat widget is minimized
  - `error`: When an error occurs during chat
  - `file_attached`: When a file is attached to the chat
  - `file_removed`: When a file is removed from the chat
  - `widget_loaded`: When the embedded widget is loaded

- **Performance Metrics**: Implemented tracking for chat performance:
  - Response time measurement
  - RAG usage tracking
  - Result count tracking
  - Source tracking (embedded vs. direct)

- **Implementation Details**:
  - Added tracking code to the ChatUI component
  - Used fetch API to send analytics data to the server
  - Implemented proper error handling for analytics tracking
  - Added conversation ID generation for tracking conversations
  - Added isWidget flag to differentiate between embedded and direct chat
  - Enhanced metadata with source information for all events
  - Added referrer tracking for embedded widgets
  - Fixed widget analytics tracking to follow React's Rules of Hooks
  - Added proper error handling for widget analytics tracking
  - Ensured widget tracking works even without authentication

### Analytics API Routes
- **/api/analytics/track-event**: API route for tracking chat events
- **/api/analytics/track-performance**: API route for tracking performance metrics

## Next Steps
- Enhance the chat UI with additional features
- Improve vector search performance and relevance
- Add support for more advanced chat capabilities
- Enhance analytics with more detailed metrics
- Implement analytics dashboard visualizations
- Optimize for mobile devices
