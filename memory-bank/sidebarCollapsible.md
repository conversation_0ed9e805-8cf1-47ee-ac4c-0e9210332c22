# Collapsible Sidebar Implementation

## Overview

Implemented a collapsible sidebar for the dashboard that can be toggled on and off, especially useful for smaller devices. The sidebar automatically collapses on mobile devices and can be toggled with a button in the header.

## Features

- Responsive sidebar that collapses on mobile devices
- Toggle button in the header to show/hide the sidebar
- Smooth transitions for opening and closing
- Overlay that appears behind the sidebar on mobile
- Automatic detection of screen size to set initial state
- Context provider for managing sidebar state across components
- Auto-close sidebar on mobile when a link is clicked
- Improved mobile UX with better screen space utilization
- Keyboard accessibility for closing the sidebar (Escape key)
- Screen reader support with proper ARIA attributes

## Implementation Details

### 1. Sidebar Context

Created a context provider to manage the sidebar state globally:

```tsx
// src/contexts/SidebarContext.tsx
"use client";

import { createContext, useContext, useEffect, useState } from "react";

interface SidebarContextType {
  isOpen: boolean;
  toggle: () => void;
  close: () => void;
  open: () => void;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export function SidebarProvider({ children }: { children: React.ReactNode }) {
  // Default to open on larger screens, closed on mobile
  const [isOpen, setIsOpen] = useState(true);

  // Check screen size on mount and when window resizes
  useEffect(() => {
    const checkScreenSize = () => {
      if (typeof window !== "undefined") {
        setIsOpen(window.innerWidth >= 768); // 768px is the md breakpoint in Tailwind
      }
    };

    // Set initial state
    checkScreenSize();

    // Add event listener for window resize
    window.addEventListener("resize", checkScreenSize);

    // Clean up
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  const toggle = () => setIsOpen((prev) => !prev);
  const close = () => setIsOpen(false);
  const open = () => setIsOpen(true);

  return (
    <SidebarContext.Provider value={{ isOpen, toggle, close, open }}>
      {children}
    </SidebarContext.Provider>
  );
}

export function useSidebar() {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error("useSidebar must be used within a SidebarProvider");
  }
  return context;
}
```

### 2. Header Toggle Button

Added a toggle button to the ModernHeader component:

```tsx
// In ModernHeader.tsx
const { isOpen, toggle } = useSidebar();

// Added to the header
<Button
  variant="ghost"
  size="icon"
  className="mr-2 h-9 w-9 rounded-full md:hidden"
  onClick={toggle}
  aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
>
  <Menu className="h-5 w-5" />
</Button>
```

### 3. Responsive Sidebar Components

Created client components for the sidebar and content area:

```tsx
// src/components/dashboard/DashboardClientComponents.tsx
export const DashboardSidebar = () => {
  const { isOpen, close } = useSidebar();

  return (
    <>
      {/* Overlay for mobile - only visible when sidebar is open */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-background/80 backdrop-blur-sm z-20 md:hidden"
          onClick={close}
          aria-hidden="true"
        />
      )}
      <ModernSidebar
        className={cn(
          "w-64 border-r border-border/40 fixed h-full shadow-sm z-30 transition-all duration-300 ease-in-out",
          isOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
        )}
      />
    </>
  );
};

export const DashboardContent = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { isOpen } = useSidebar();

  return (
    <div
      className={cn(
        "flex-1 transition-all duration-300 ease-in-out",
        isOpen ? "md:ml-64" : "ml-0"
      )}
    >
      <ModernHeader className="sticky top-0 z-20 backdrop-blur-md bg-background/80 border-b border-border/40" />
      <main className="p-4 md:p-8 max-w-7xl mx-auto">{children}</main>
    </div>
  );
};
```

### 4. Updated Dashboard Layout

Updated the dashboard layout to use the SidebarProvider and client components:

```tsx
// src/app/(main)/dashboard/layout.tsx
export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Auth and website checks...

  return (
    <SidebarProvider>
      <div className="flex min-h-screen bg-background">
        <DashboardSidebar />
        <DashboardContent>{children}</DashboardContent>
      </div>
    </SidebarProvider>
  );
}
```

## Benefits

1. **Improved Mobile Experience**: Users on mobile devices now have more screen space for content
2. **Better Usability**: The sidebar can be toggled on and off as needed
3. **Responsive Design**: Automatically adapts to different screen sizes
4. **Smooth Transitions**: Uses CSS transitions for smooth opening and closing
5. **Accessibility**: Includes proper ARIA labels and keyboard support

### 5. Auto-Close on Link Click

Added functionality to automatically close the sidebar when a link is clicked on mobile devices:

```tsx
// In ModernSidebar.tsx
export function ModernSidebar({ className }: SidebarProps) {
  const pathname = usePathname();
  const { data: websites, isLoading: isLoadingWebsites } = useWebsites();
  const { close } = useSidebar();

  // Function to handle link clicks - closes sidebar on mobile
  const handleLinkClick = () => {
    // Only close the sidebar on mobile devices
    if (typeof window !== "undefined" && window.innerWidth < 768) {
      close();
    }
  };

  // ... rest of the component

  return (
    // ... component JSX
    <Link
      href={item.href}
      onClick={handleLinkClick}
    >
      {/* Link content */}
    </Link>
    // ... more component JSX
  );
}
```

This ensures that when a user clicks on a navigation link on mobile, the sidebar automatically closes to show the content, providing a better user experience.

### 6. Keyboard Accessibility

Added keyboard accessibility to ensure users can navigate and close the sidebar using only a keyboard:

```tsx
// In DashboardClientComponents.tsx
{isOpen && (
  <div
    className="fixed inset-0 bg-background/80 backdrop-blur-sm z-20 md:hidden"
    onClick={close}
    onKeyDown={(e) => {
      // Close on Escape key
      if (e.key === "Escape") {
        close();
      }
    }}
    tabIndex={0} // Make div focusable for keyboard navigation
    role="button"
    aria-label="Close sidebar"
  />
)}
```

This implementation allows users to:
- Close the sidebar by pressing the Escape key
- Navigate to the overlay using Tab key (tabIndex={0})
- Understand the purpose of the element with screen readers (aria-label)

## Future Improvements

- Add keyboard shortcuts for toggling the sidebar (e.g., Esc to close)
- Remember user preference for sidebar state in localStorage
- Add more animation effects for a polished feel
- Consider adding a mini sidebar mode that shows only icons
