# Domain Allowlisting for Chat Widget

## Overview

The domain allowlisting feature restricts which websites can embed the chat widget. This security measure ensures that the chat widget can only be used on domains that have been explicitly approved by the website owner.

## Implementation Details

### Database Schema

The `chat_configurations` table includes an `allowedDomains` field, which is an array of strings representing the domains that are allowed to embed the chat widget.

```sql
allowedDomains text[] DEFAULT '{}'
```

### Domain Validation Logic

The domain validation logic is implemented in two main places:

1. **Chat API Route** (`src/app/api/chat/[websiteId]/route.ts`):
   - Checks if the origin domain is in the allowed domains list
   - Returns a 403 error with a descriptive message if the domain is not allowed
   - Allows all domains if no allowed domains are configured

2. **Widget Config API Route** (`src/app/api/widget/config/[websiteId]/route.ts`):
   - Checks if the origin domain is in the allowed domains list
   - Returns a 403 error with a descriptive message if the domain is not allowed
   - Allows all domains if no allowed domains are configured

### Domain Matching Rules

The domain validation logic follows these rules:

1. If no allowed domains are configured, all domains are allowed
2. If allowed domains are configured, the origin domain must match one of the allowed domains
3. A domain matches if it's exactly the same as an allowed domain or if it's a subdomain of an allowed domain
4. The domain comparison is done on the hostname part of the URL (e.g., `example.com` from `https://example.com`)

### CORS Headers

The CORS headers are set based on the allowed domains:

1. If the domain is allowed, the `Access-Control-Allow-Origin` header is set to the specific origin
2. If no allowed domains are configured, the `Access-Control-Allow-Origin` header is set to `*` (allowing all origins)

## User Interface

The domain allowlisting feature is managed through the chat configuration UI in the dashboard. Users can:

1. Add domains to the allowed list
2. Remove domains from the allowed list
3. View the current list of allowed domains

## Error Handling

When a domain is not allowed, the API returns a 403 error with a descriptive message:

```json
{
  "error": "Domain not allowed",
  "message": "The domain example.com is not in the allowed domains list for this chat widget. Please contact the widget owner to add this domain."
}
```

## Testing

To test the domain allowlisting feature:

1. Add a domain to the allowed list through the chat configuration UI
2. Embed the chat widget on a website with that domain - it should work
3. Embed the chat widget on a website with a different domain - it should show an error message

## Future Improvements

Potential improvements to the domain allowlisting feature:

1. **Wildcard Domain Support**: Add support for wildcard domains (e.g., `*.example.com`) to allow all subdomains of a specific domain
2. **Domain Testing Tool**: Create a tool that allows users to test if a domain is allowed before embedding the chat widget
3. **Bulk Domain Management**: Add the ability to import/export allowed domains in bulk
4. **Domain Verification**: Add a verification step to ensure that the user owns the domain they're adding

## Troubleshooting

Common issues with domain allowlisting:

1. **Domain Format**: Make sure domains are added in the correct format (e.g., `https://example.com`)
2. **Subdomains**: If you want to allow a subdomain, you need to add it specifically or add the parent domain
3. **Local Development**: For testing locally, add `http://localhost` or `http://localhost:3000` to the allowed domains list
