# Vector Search Implementation

## Overview

The vector search functionality enables semantic search of website content using embeddings. This allows the chat assistant to find and retrieve relevant content from the website based on the user's query, providing more accurate and contextually relevant responses.

## Components

### 1. Vector Search Module
- **File**: `src/lib/vector-search.ts`
- **Purpose**: Provides a simple interface for searching similar content
- **Main Function**: `searchSimilarContent`
- **Integration**: Uses WebsiteRagService for the actual search implementation

```typescript
// src/lib/vector-search.ts
import { WebsiteRagService } from "@/lib/services/rag/website-rag-service";

export async function searchSimilarContent(
  websiteId: string,
  query: string,
  limit = 5
): Promise<Array<{
  content: string;
  url: string;
  title: string;
  similarity: number;
}>> {
  try {
    // Create an instance of the WebsiteRagService
    const ragService = new WebsiteRagService();

    // Search for similar content
    const results = await ragService.searchSimilarContent(
      websiteId,
      query,
      limit
    );

    // Map the results to the expected format
    return results.map(result => ({
      content: result.text,
      url: result.url,
      title: result.title,
      similarity: result.similarity
    }));
  } catch (error) {
    console.error("Error in searchSimilarContent:", error);
    // Return an empty array instead of throwing to avoid breaking the API
    return [];
  }
}
```

### 2. WebsiteRagService
- **File**: `src/lib/services/rag/website-rag-service.ts`
- **Purpose**: Provides RAG (Retrieval Augmented Generation) capabilities
- **Main Methods**: 
  - `processPage`: Process a website page and store its embeddings
  - `processWebsite`: Process all pages for a website
  - `searchSimilarContent`: Search for similar content using vector similarity

### 3. Database Schema
- **Tables**:
  - `website_embeddings`: Stores embeddings for website content
  - `website_pages`: Stores website pages content
- **Vector Type**: Using pgvector for PostgreSQL vector operations

## Implementation Details

### Embedding Generation
1. Website content is chunked into smaller pieces
2. Embeddings are generated for each chunk using Mistral's embedding model
3. Embeddings are stored in the database with metadata

### Vector Search Process
1. Generate an embedding for the user's query
2. Perform a similarity search using cosine similarity
3. Retrieve the most similar content chunks
4. Return the results with similarity scores

### API Integration
- The chat-simple API route uses the vector search functionality to find relevant content
- The search results are used to generate a response to the user's query

```typescript
// Example usage in API route
const searchResults = await searchSimilarContent(
  websiteId,
  lastUserMessage.content
);

if (searchResults && searchResults.length > 0) {
  // Use the search results to generate a response
  const context = searchResults
    .map((result) => result.content)
    .join("\n\n");

  responseContent = `Based on the information from ${website.name}, I can tell you that:\n\n${context}\n\nIs there anything else you'd like to know?`;
}
```

## Testing

### Test Scripts
- `test-vector-search-direct.ts`: Test vector search functionality directly
- `search-similar-content.ts`: Search for similar content in a website

### Usage
```bash
# Test vector search
pnpm test:vector-search:direct --websiteId=<websiteId> --query="<search query>"

# Search for similar content
pnpm search:similar --websiteId=<websiteId> --query="<search query>"
```

## Performance Considerations

### Optimization Techniques
1. **Chunking Strategy**: Using recursive chunking with overlap for better context preservation
2. **Embedding Dimensions**: Using 1024-dimensional embeddings for better semantic representation
3. **Similarity Threshold**: Filtering results below a certain similarity threshold
4. **Result Limit**: Limiting the number of results to improve response time
5. **Batch Processing**: Processing embeddings in batches for better performance

### Scaling Considerations
1. **Database Indexing**: Using pgvector's indexing capabilities for faster searches
2. **Caching**: Implementing caching for frequently accessed content
3. **Asynchronous Processing**: Processing embeddings asynchronously to avoid blocking
4. **Incremental Updates**: Only updating embeddings for changed content

## Future Improvements

1. **Hybrid Search**: Combine vector search with keyword search for better results
2. **Query Preprocessing**: Implement query preprocessing for better matches
3. **Relevance Ranking**: Improve relevance ranking with additional factors
4. **Multi-modal Embeddings**: Support for embeddings of different content types
5. **Embedding Model Updates**: Keep embedding models up-to-date with latest versions
6. **Chunking Improvements**: Implement more sophisticated chunking strategies
7. **Performance Monitoring**: Add metrics for search performance and relevance
