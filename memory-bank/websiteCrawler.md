# Website Crawler Implementation

## Overview
This document details the implementation of the Website Crawler feature for the Bubl platform. The crawler is responsible for extracting content from client websites for use in the chatbot knowledge base.

## Architecture

### Core Components
1. **WebsiteCrawler Class**: Main class responsible for crawling websites
2. **CrawlConfigurationForm**: UI component for configuring crawler settings
3. **API Endpoint**: `/api/dashboard/websites/[id]/crawl` for triggering crawls
4. **Database Schema**: Tables for storing website pages and embeddings

### Technology Stack
- **Puppeteer**: Headless browser for JavaScript-rendered content
- **Cheerio**: HTML parsing and content extraction
- **Browserless**: Remote browser service for Docker environments
- **robots-parser**: Library for parsing and respecting robots.txt rules

## Implementation Status

### ✅ Core Crawler: Implemented and Enhanced
- `WebsiteCrawler` class in `src/lib/services/crawler/website-crawler.ts` implemented
- Support for configurable crawl depth and page limits
- Basic content extraction using Cheerio
- Integration with Puppeteer for JavaScript-rendered content
- Support for Browserless in Docker environments
- Basic URL filtering with include/exclude patterns
- Robots.txt compliance
- Rate limiting to avoid overloading websites
- Enhanced error handling with retry logic for connection issues
- Improved browser stability with optimized launch configuration
- Resource blocking for better performance (images, media, fonts)
- Intelligent navigation fallback for problematic pages

### ✅ Crawler Configuration UI: Implemented
- `CrawlConfigurationForm` component in `src/components/dashboard/CrawlConfigurationForm.tsx` implemented
- Fields for max pages, max depth, include/exclude patterns, and scheduling
- Support for robots.txt compliance and rate limiting
- Integration with the website detail page

### ✅ API Endpoint: Implemented
- `/api/dashboard/websites/[id]/crawl` endpoint for triggering crawls
- Support for all configuration options
- Background processing for long-running crawls
- Status updates for tracking crawl progress

### ✅ Content Extraction: Implemented
- Enhanced content extraction logic to better identify main content
- Support for different content types and structures
- Better text cleaning and normalization
- Removal of boilerplate text

### ✅ Browserless Integration: Implemented
- Environment variable configuration for Browserless
- WebsiteCrawler modifications to connect to Browserless
- Detailed documentation in `docs/browserless-setup.md`
- Error handling and logging
- Fallback to local browser when Browserless is not available

### ✅ Documentation: Implemented
- Comprehensive documentation in `docs/website-crawler.md`
- API documentation for the crawler endpoint
- UI documentation for the crawler configuration form

## Features

1. **Configurable Crawl Settings**
   - Maximum pages to crawl
   - Maximum link depth
   - Include/exclude patterns for URLs
   - Crawl frequency (daily, weekly, monthly, manual)

2. **Content Extraction**
   - Intelligent main content identification
   - Removal of non-content elements
   - Text normalization and cleaning
   - Metadata extraction

3. **Robots.txt Compliance**
   - Fetching and parsing robots.txt files
   - Respecting disallow rules
   - User-agent identification

4. **Rate Limiting**
   - Configurable delay between requests
   - Avoids overloading client websites

5. **Browserless Integration**
   - Support for using Browserless in Docker environments
   - Fallback to local browser when Browserless is not available

6. **Vector Search Integration**
   - Automatic generation of embeddings for search
   - Integration with MastrAI's embedding models

## Implementation Details

### WebsiteCrawler Class
The `WebsiteCrawler` class is the core component responsible for crawling websites. It uses Puppeteer to navigate to pages, extract content, and follow links.

Key methods:
- `crawl()`: Main method to start the crawling process
- `processPage()`: Process a single page, extract content, and store in the database
- `extractMainContent()`: Extract the main content from a page
- `extractLinks()`: Extract links from a page for further crawling
- `fetchRobotsTxt()`: Fetch and parse the robots.txt file
- `isAllowedByRobotsTxt()`: Check if a URL is allowed by robots.txt

### CrawlConfigurationForm Component
The `CrawlConfigurationForm` component provides a user interface for configuring the crawler. It allows users to set various options like max pages, max depth, include/exclude patterns, and more.

### API Endpoint
The `/api/dashboard/websites/[id]/crawl` endpoint is used to trigger crawls. It accepts a JSON payload with configuration options and starts the crawling process in the background.

### Browserless Integration

#### Environment Configuration
Added environment variables to support Browserless configuration:

```typescript
// In src/env.ts
BROWSERLESS_URL: z.string().url().optional(),
BROWSERLESS_API_KEY: z.string().optional(),
USE_BROWSERLESS: z.enum(["true", "false"]).optional().default("false"),
```

### Error Resilience Improvements

#### Retry Logic for Connection Issues
Implemented retry logic in the `processPage` method to handle connection issues:

```typescript
// Maximum number of retries
const MAX_RETRIES = 3;

// Check if we should retry
if (
  retryCount < MAX_RETRIES &&
  (errorMessage.includes("Protocol error") ||
   errorMessage.includes("Connection closed") ||
   errorMessage.includes("Browser disconnected") ||
   errorMessage.includes("Navigation timeout"))
) {
  console.log(`Retrying page ${url} (attempt ${retryCount + 1} of ${MAX_RETRIES})`);

  // Wait before retrying
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Retry processing the page
  return this.processPage(browser, url, depth, retryCount + 1);
}
```

#### Enhanced Browser Configuration
Improved browser stability with optimized launch configuration:

```typescript
browser = await puppeteer.launch({
  headless: true,
  args: [
    "--no-sandbox",
    "--disable-setuid-sandbox",
    "--disable-dev-shm-usage",
    "--disable-accelerated-2d-canvas",
    "--disable-gpu",
    "--disable-extensions",
    "--disable-component-extensions-with-background-pages",
    "--disable-default-apps",
    "--mute-audio",
    "--no-default-browser-check",
    "--no-first-run",
    "--disable-background-timer-throttling",
    "--disable-backgrounding-occluded-windows",
    "--disable-renderer-backgrounding",
    "--disable-background-networking",
    "--disable-breakpad",
    "--disable-sync",
    "--disable-translate",
    "--metrics-recording-only",
    "--disable-hang-monitor",
    "--disable-features=site-per-process,TranslateUI",
    "--disable-ipc-flooding-protection",
    "--enable-features=NetworkService,NetworkServiceInProcess",
  ],
  ignoreDefaultArgs: false,
  timeout: 60000 // 60 second timeout
});
```

#### Resource Blocking for Performance
Implemented resource blocking to improve performance:

```typescript
// Block unnecessary resource types to improve performance
await page.setRequestInterception(true);
page.on('request', (request) => {
  const resourceType = request.resourceType();
  if (resourceType === 'image' || resourceType === 'media' || resourceType === 'font') {
    request.abort();
  } else {
    request.continue();
  }
});
```

#### Navigation Fallback
Added fallback navigation strategy for problematic pages:

```typescript
// Navigate to the URL with retry logic for navigation
try {
  await page.goto(url, {
    waitUntil: "networkidle2",
    timeout: 60000 // 60 second timeout
  });
} catch (navError) {
  console.warn(`Navigation error for ${url}, trying with domcontentloaded: ${navError}`);
  // Try with a less strict wait condition
  await page.goto(url, {
    waitUntil: "domcontentloaded",
    timeout: 60000
  });
}
```

#### WebsiteCrawler Modifications
Updated the WebsiteCrawler class to connect to Browserless when configured:

```typescript
// Check if we should use Browserless
const useBrowserless = env.USE_BROWSERLESS === "true" && env.BROWSERLESS_URL;

console.log(`Crawler configuration: Using Browserless: ${useBrowserless ? 'Yes' : 'No'}`);

let browser;

if (useBrowserless) {
  // Connect to Browserless service
  console.log(`Connecting to Browserless at: ${env.BROWSERLESS_URL}`);

  const browserlessUrl = env.BROWSERLESS_API_KEY
    ? `${env.BROWSERLESS_URL}?token=${env.BROWSERLESS_API_KEY}`
    : env.BROWSERLESS_URL;

  try {
    browser = await puppeteer.connect({
      browserWSEndpoint: browserlessUrl,
    });
    console.log("Successfully connected to Browserless");
  } catch (error) {
    console.error("Failed to connect to Browserless:", error);
    throw new Error("Failed to connect to Browserless service");
  }
} else {
  // Use local browser
  browser = await puppeteer.launch({
    headless: true,
    args: [
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-dev-shm-usage",
      "--disable-accelerated-2d-canvas",
    ],
  });
}
```

#### Deployment Options

1. **Self-hosted Browserless**
   Run a Browserless instance using Docker:
   ```bash
   docker run -p 3000:3000 browserless/chrome
   ```

2. **Browserless.io Cloud Service**
   Use the [Browserless.io](https://browserless.io) cloud service, which offers a free tier and paid plans for higher usage.

## Next Steps

1. **Scheduled Crawling**
   - Implement a background job system for scheduled crawls
   - Create a UI for viewing scheduled crawls
   - Add support for canceling scheduled crawls

2. **Manual Content Upload**
   - Add support for manually uploading content (PDFs, docs)
   - Implement content extraction for different file types
   - Integrate with the existing embedding generation pipeline

3. **Crawl Status Page**
   - Create a dedicated page for viewing crawl status
   - Add real-time updates using WebSockets
   - Implement detailed logging for troubleshooting

4. **Advanced Content Extraction**
   - Implement more sophisticated content extraction algorithms
   - Add support for extracting structured data
   - Improve metadata extraction

5. **Performance Optimization**
   - Implement parallel crawling for faster processing
   - Add caching for frequently accessed content
   - Optimize database operations for large websites

6. **Browserless Enhancements**
   - Implement automated testing for Browserless integration
   - Add connection pooling for multiple concurrent crawls
   - Implement monitoring and alerting for Browserless service
   - Optimize crawl performance with Browserless-specific settings
   - Add support for additional Browserless features (screenshots, PDF generation)

7. **Error Resilience Improvements: Implemented**
   - Added retry logic for handling connection issues
   - Implemented intelligent fallback for navigation errors
   - Enhanced browser configuration for better stability
   - Added resource blocking for improved performance
   - Implemented proper error handling and recovery
   - Fixed "Protocol error: Connection closed" issues
   - Added better browser connection management
