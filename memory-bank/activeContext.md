# Active Context

## Current Work Focus
- Chat widget analytics instrumentation and event tracking
- Dashboard analytics integration for widget and chat metrics
- Troubleshooting missing analytics data in dashboard (Avg. Response Time, RAG Usage, Widget Engagements)
- Security enhancements for API endpoints (in progress)

## Recent Changes
- Instrumented chat widget for load time, engagement, error, FID, and TTI events
- Updated backend analytics aggregation to include widget and chat metrics
- Updated dashboard UI to display new analytics cards (Avg. Response Time, RAG Usage, Widget Engagements, etc.)
- Investigated and fixed chat widget remount issue (ErrorBoundary placement)
- Implemented robust API rate limiting using Upstash Redis in Next.js middleware (Edge-compatible)
- Removed all legacy ioredis-based rate limiting and server-only CSRF middleware from the codebase
- Clerk userId or IP-based identification for multi-tenant safety (API handlers)
- Zod-based input validation for all major API endpoints
- Standardized error responses for validation errors
- Added reusable validateRequest utility for DRY validation logic
- Middleware now checks for CSRF header presence on state-changing requests; full CSRF validation is performed in API handlers
- Chat API route is now fully cleaned up and relies only on middleware for rate limiting

## Next Steps
- Confirm analytics events are being sent from the widget (network tab)
- Query database to ensure analytics events and metrics are present
- Test with real chat interactions to generate analytics data
- (If needed) Add debug logging to backend aggregation for further troubleshooting
- Implement security headers for all API endpoints (next security priority)
- Expand E2E testing coverage for the entire app (Playwright milestone)
- Set up CORS protection, CSRF protection, secure cookies, and dependency scanning
- Continue with other security improvements as outlined

## Active Decisions & Considerations
- Analytics event structure: all events include isWidget/source metadata
- Widget engagement and performance metrics tracked via dedicated events
- Rate limiting granularity: Upstash Redis in middleware (IP-based), Clerk userId for API handlers
- Middleware pattern for easy extension to other routes
- Type safety and error handling prioritized
- Zod schemas colocated in src/lib/validation, types inferred for downstream safety
- Standard error response: { error: string, details: ZodError.flatten() } 