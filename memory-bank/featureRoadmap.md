# Feature Roadmap

## Overview
This document outlines the feature roadmap for the Bubl platform, aligned with the phases defined in the PRD. It tracks progress against the MVP scope and provides a timeline for feature implementation.

## <PERSON> Scope (Phase 1)

### 1. Basic Website Crawling and Content Indexing
- **Priority**: P0
- **Status**: Partially Implemented
- **Components**:
  - ✅ Website crawler implementation
  - ✅ Content extraction and processing
  - ✅ Basic embedding generation
  - ✅ Simple vector search
- **Timeline**: Weeks 1-4
- **Dependencies**: PostgreSQL with pgvector, MastrAI embedding models
- **Notes**: See `vectorSearch.md` and `websiteCrawler.md` for implementation details

### 2. Simple, Customizable Chat Widget
- **Priority**: P0
- **Status**: Implemented
- **Components**:
  - ✅ Basic chat UI with Shadcn components
  - ✅ Customizable position (bottom-right, bottom-left)
  - ✅ Minimizable interface
  - ✅ Message history within session
  - ⚠️ Basic customization options (in progress)
- **Timeline**: Weeks 1-3
- **Dependencies**: Next.js, Shadcn UI, Tailwind CSS
- **Notes**: See `uiImplementation.md` and `uiComponents.md` for details

### 3. Core Dashboard Functionality
- **Priority**: P0
- **Status**: Partially Implemented
- **Components**:
  - ✅ Authentication with Clerk
  - ✅ Basic dashboard structure
  - ✅ Website management API endpoints
  - ⚠️ Website management UI (in progress)
  - ❌ Chat configuration UI (planned)
  - ❌ Embed code generator (planned)
- **Timeline**: Weeks 2-5
- **Dependencies**: Next.js, Clerk Auth, Shadcn UI
- **Notes**: See `analyticsDashboard.md` for implementation details

### 4. MastrAI Integration
- **Priority**: P0
- **Status**: Implemented
- **Components**:
  - ✅ MastrAI agent configuration
  - ✅ Chat API with streaming responses
  - ✅ Conversation memory system
  - ✅ Basic context retrieval
  - ❌ Vector search integration (planned)
- **Timeline**: Weeks 1-3
- **Dependencies**: MastrAI SDK, Vercel AI SDK
- **Notes**: See `mastraAI.md` for implementation details

### 5. Basic Analytics
- **Priority**: P0
- **Status**: Implemented
- **Components**:
  - ✅ Event tracking setup
  - ✅ Database schema for analytics
  - ✅ Data aggregation and processing
  - ✅ Basic visualization dashboard
  - ✅ Performance metrics tracking
  - ✅ Core Web Vitals monitoring
  - ✅ Network information tracking
- **Timeline**: Weeks 3-6
- **Dependencies**: PostgreSQL, Drizzle ORM
- **Notes**: See `analyticsDashboard.md` and `widgetAnalyticsPerformance.md` for implementation details

## Phase 2 Features

### 1. Enhanced Analytics
- **Priority**: P1
- **Status**: Partially Implemented
- **Components**:
  - ✅ Advanced performance metrics and visualization
  - ✅ Core Web Vitals monitoring
  - ✅ Network quality tracking
  - ✅ Performance recommendations
  - ❌ User satisfaction tracking (planned)
  - ❌ Conversation analysis (planned)
  - ❌ Exportable reports (planned)
  - ❌ Real User Monitoring (planned)
  - ❌ Performance budgets (planned)
- **Timeline**: Months 3-4
- **Dependencies**: Phase 1 completion
- **Notes**: See `widgetAnalyticsPerformance.md` for implementation details

### 2. Advanced Customization
- **Priority**: P1
- **Status**: Planned
- **Components**:
  - ❌ Advanced theme customization
  - ❌ Custom CSS options
  - ❌ Widget behavior configuration
  - ❌ Mobile-specific settings
- **Timeline**: Months 3-4
- **Dependencies**: Simple chat widget completion
- **Notes**: Will extend the existing chat widget implementation

### 3. Custom Training Capabilities
- **Priority**: P1
- **Status**: Planned
- **Components**:
  - ❌ Custom response editor
  - ❌ FAQ management
  - ❌ Response override system
  - ❌ Training data management
- **Timeline**: Months 3-4
- **Dependencies**: MastrAI integration completion
- **Notes**: Will require enhancements to the MastrAI agent configuration

### 4. File Attachment Support
- **Priority**: P1
- **Status**: Planned
- **Components**:
  - ❌ File upload in chat widget
  - ❌ File type validation
  - ❌ Storage integration
  - ❌ File processing for context
- **Timeline**: Months 3-4
- **Dependencies**: Simple chat widget completion
- **Notes**: Will require updates to the chat UI and API

## Phase 3 Features

### 1. Multi-language Support
- **Priority**: P2
- **Status**: Planned
- **Components**:
  - ❌ UI translation
  - ❌ Multi-language model support
  - ❌ Language detection
  - ❌ Language-specific training
- **Timeline**: Months 5-6
- **Dependencies**: Phase 2 completion
- **Notes**: Will require significant enhancements to the MastrAI integration

### 2. CRM Integrations
- **Priority**: P2
- **Status**: Planned
- **Components**:
  - ❌ Integration framework
  - ❌ Popular CRM connectors
  - ❌ Data synchronization
  - ❌ Contact creation
- **Timeline**: Months 5-6
- **Dependencies**: Phase 2 completion
- **Notes**: Will require development of an integration framework

### 3. Advanced Conversation Flows
- **Priority**: P2
- **Status**: Planned
- **Components**:
  - ❌ Conversation flow editor
  - ❌ Conditional responses
  - ❌ Multi-step workflows
  - ❌ Goal tracking
- **Timeline**: Months 5-6
- **Dependencies**: Custom training capabilities
- **Notes**: Will require significant enhancements to the conversation system

### 4. API Access for Developers
- **Priority**: P2
- **Status**: Planned
- **Components**:
  - ❌ API documentation
  - ❌ Authentication system
  - ❌ Rate limiting
  - ❌ SDK development
- **Timeline**: Months 5-6
- **Dependencies**: Phase 2 completion
- **Notes**: Will require development of a comprehensive API layer

## Current Development Focus

### Immediate Priorities (Next 2 Weeks)
1. Complete website management UI in dashboard
2. Implement chat configuration UI
3. ✅ Complete website crawler implementation
4. Enhance conversation memory system
5. ✅ Implement basic analytics visualization
6. ✅ Enhance widget performance and analytics

### Short-term Goals (Next 4 Weeks)
1. ✅ Complete website crawler and content indexing
2. Implement vector search integration with crawler
3. Complete dashboard core functionality
4. Implement conversation viewer
5. Develop basic analytics reporting

## Success Metrics Tracking

### Customer Acquisition
- **Target**: 100 active customers within 3 months
- **Current**: N/A (pre-launch)
- **Tracking Method**: Dashboard user count with active status

### User Engagement
- **Target**: Average of 500 chat interactions per customer website monthly
- **Current**: N/A (pre-launch)
- **Tracking Method**: Analytics system with conversation counting

### Customer Satisfaction
- **Target**: 85% positive feedback on chatbot responses
- **Current**: N/A (pre-launch)
- **Tracking Method**: Feedback collection after conversations

### Business Impact
- **Target**: 20% reduction in support tickets for customers
- **Current**: N/A (pre-launch)
- **Tracking Method**: Integration with support systems and manual reporting
