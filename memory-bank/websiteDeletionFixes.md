# Website Deletion Fixes

## Overview
This document details the implementation of fixes for the website deletion functionality in the Bubl platform. These improvements address foreign key constraint issues and enhance the user experience with a confirmation dialog that requires URL verification.

## Issues Addressed

### Foreign Key Constraint Violation
- Error: "Server error: update or delete on table 'websites' violates foreign key constraint 'website_pages_website_id_websites_id_fk' on table 'website_pages'"
- The website deletion was failing because related records in other tables were referencing the website being deleted
- The deletion process needed to be updated to handle cascading deletion of related records

### UI Not Updating After Deletion
- After successful deletion, the website was still showing in the list until the page was manually refreshed
- The UI state wasn't being properly updated after a website was deleted
- The query cache wasn't being properly invalidated

## Implementation Details

### Cascading Deletion
Implemented a proper deletion sequence to handle foreign key constraints:

1. First, delete all messages related to conversations of the website
2. Next, delete all conversations related to the website
3. Then, delete all website embeddings related to the website
4. After that, delete all website pages related to the website
5. Finally, delete the website itself

```typescript
// First, delete all messages related to conversations of this website
console.log(`Deleting messages for website ${id}...`);
try {
  // Get all conversation IDs for this website
  const conversations = await db
    .select({ id: conversationsTable.id })
    .from(conversationsTable)
    .where(eq(conversationsTable.websiteId, id));
  
  const conversationIds = conversations.map(conv => conv.id);
  
  if (conversationIds.length > 0) {
    // Delete all messages for these conversations
    await db
      .delete(messagesTable)
      .where(sql`${messagesTable.conversationId} IN (${sql.join(conversationIds)})`);
  }
  
  console.log(`Successfully deleted messages for website ${id}`);
} catch (error) {
  console.error(`Error deleting messages for website ${id}:`, error);
  throw new ApiError(
    `Failed to delete website messages: ${error.message}`,
    500
  );
}

// Continue with deleting conversations, embeddings, pages, and finally the website...
```

### Confirmation Dialog
Created a DeleteWebsiteDialog component that requires the user to type the website URL to confirm deletion:

```tsx
export function DeleteWebsiteDialog({
  isOpen,
  onClose,
  onConfirm,
  websiteName,
  websiteUrl,
  isDeleting,
}: DeleteWebsiteDialogProps) {
  const [confirmUrl, setConfirmUrl] = useState("")
  const isConfirmDisabled = confirmUrl !== websiteUrl

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-destructive">Delete Website</DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete the website
            &quot;{websiteName}&quot; and all of its data.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="confirmUrl" className="text-left">
              To confirm, type the website URL below:
            </Label>
            <Input
              id="confirmUrl"
              value={confirmUrl}
              onChange={(e) => setConfirmUrl(e.target.value)}
              placeholder={websiteUrl}
              className={isConfirmDisabled && confirmUrl ? "border-destructive" : ""}
              autoComplete="off"
            />
            {isConfirmDisabled && confirmUrl && (
              <p className="text-xs text-destructive mt-1">
                The URL doesn&apos;t match. Please enter the exact URL.
              </p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isDeleting}>
            Cancel
          </Button>
          <Button 
            variant="destructive" 
            onClick={onConfirm} 
            disabled={isConfirmDisabled || isDeleting}
          >
            {isDeleting ? "Deleting..." : "Delete Website"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
```

### UI State Updates
Enhanced the useDeleteWebsite hook to properly update the UI after deletion:

```typescript
// Hook to delete a website
export function useDeleteWebsite() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => websitesApi.delete(id),
    onSuccess: (_: unknown, id: string) => {
      // Remove the specific website from the cache
      queryClient.removeQueries({ queryKey: websiteKeys.detail(id) });
      
      // Force refetch the websites list
      queryClient.invalidateQueries({ 
        queryKey: websiteKeys.all,
        refetchType: 'active',
        exact: false
      });
      
      // Update the websites list in the cache by removing the deleted website
      queryClient.setQueryData<Website[]>(websiteKeys.all, (oldData) => {
        if (!oldData) return [];
        return oldData.filter(website => website.id !== id);
      });
    },
  });
}
```

Also added a refetch call in the confirmDelete function:

```typescript
const confirmDelete = () => {
  if (!websiteToDelete) return

  deleteWebsite(websiteToDelete.id, {
    onSuccess: () => {
      toast.success("Website deleted", {
        description: `"${websiteToDelete.name}" has been removed from your account.`,
      })
      closeDeleteDialog()
      // Refetch the websites list to update the UI
      refetch()
    },
    onError: (err) => {
      toast.error("Failed to delete website", {
        description:
          err.message || "An error occurred while deleting the website.",
      })
    },
  })
}
```

## Benefits

### Improved Data Integrity
- Properly handles foreign key constraints
- Ensures all related data is deleted in the correct order
- Prevents orphaned records in the database
- Maintains database consistency

### Enhanced User Experience
- Requires URL confirmation to prevent accidental deletions
- Provides immediate visual feedback after deletion
- Shows clear error messages if something goes wrong
- Disables the delete button until the URL is correctly entered

### Better Error Handling
- Adds detailed error messages for each step of the deletion process
- Logs errors to the console for debugging
- Shows user-friendly error messages with toast notifications
- Provides specific error codes and messages for different failure scenarios

### Optimized Performance
- Updates the UI immediately after deletion
- Uses optimistic UI updates to improve perceived performance
- Properly invalidates the query cache to ensure data consistency
- Minimizes unnecessary API calls

## Next Steps

1. **Add Undo Functionality**
   - Implement a soft delete mechanism
   - Add an undo button in the success toast
   - Create an API endpoint to restore deleted websites
   - Add a trash/archive section to view and restore deleted websites

2. **Enhance Error Recovery**
   - Add retry mechanisms for failed deletions
   - Implement transaction-based deletion for better atomicity
   - Add more detailed error reporting
   - Create a system to automatically retry failed operations

3. **Improve Confirmation UX**
   - Add keyboard shortcuts for common actions
   - Implement focus management for better accessibility
   - Add animation for smoother transitions
   - Consider alternative confirmation methods for different user preferences

4. **Add Bulk Operations**
   - Implement multi-select functionality for websites
   - Add bulk delete capability with appropriate safeguards
   - Create batch processing for better performance
   - Add progress indicators for bulk operations
