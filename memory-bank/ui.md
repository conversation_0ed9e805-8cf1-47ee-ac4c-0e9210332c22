# UI Implementation

## Overview
This document details the UI implementation for the Bubl platform, including the design system, component library, and chat widget implementation.

## Design System

### Design Principles
- Clean, minimalistic UI with soft colors
- Rounded UI elements for a softer, more modern appearance
- Subtle shadows and hover effects for depth
- Consistent spacing and alignment
- Mobile-first responsive design
- Accessibility-first approach

### Color System
- OKLCH color space for better perceptual uniformity
- Primary: soft blue (oklch(0.55 0.15 250))
- Background: very light blue tint (oklch(0.98 0.01 240))
- Card Background: white with subtle blue tint
- Border Color: transparent with low opacity
- Text: dark blue-gray for better readability
- Accent Colors: soft variants of primary colors

### Typography
- Clean, readable font hierarchy
- Consistent text sizes and weights
- Proper line heights for readability
- Responsive text sizing

### Spacing and Layout
- Consistent spacing using a 4px grid system
- Proper whitespace for readability
- Responsive layouts with mobile-first approach
- Consistent component sizing

## Shadcn UI Integration

### Overview
Shadcn UI is a collection of reusable components built with Radix UI and Tailwind CSS. It provides accessible, customizable components that have been integrated into our application with a clean, minimalistic design approach.

### Components Used
1. **Card**: Container for content blocks with soft shadows and hover effects
2. **Button**: Rounded buttons with subtle hover states
3. **Input**: Text inputs with clean borders and focus states
4. **ScrollArea**: Scrollable areas with custom scrollbar styling
5. **Avatar**: User avatars with consistent styling
6. **Badge**: Status indicators with soft colors
7. **Sonner**: Toast notifications with custom styling
8. **Progress**: Progress bars with custom styling
9. **Command**: Command palette for search functionality

### Theme Customization
```css
:root {
  --radius: 0.75rem;
  /* Soft light theme colors */
  --background: oklch(0.98 0.01 240);
  --foreground: oklch(0.25 0.02 240);
  --card: oklch(1 0.01 240);
  --card-foreground: oklch(0.25 0.02 240);

  /* Soft blue primary color */
  --primary: oklch(0.55 0.15 250);
  --primary-foreground: oklch(0.98 0.01 240);

  /* Soft secondary color */
  --secondary: oklch(0.96 0.03 250);
  --secondary-foreground: oklch(0.35 0.1 250);
}

.dark {
  /* Soft dark theme colors */
  --background: oklch(0.15 0.02 240);
  --foreground: oklch(0.9 0.02 240);
  --card: oklch(0.2 0.02 240);
  --card-foreground: oklch(0.9 0.02 240);
}
```

## Dashboard UI Components

### Dashboard Layout Structure
```tsx
<div className="flex min-h-screen bg-background">
  <ModernSidebar className="w-64 border-r border-border/40 fixed h-full shadow-sm" />
  <div className="flex-1 ml-64">
    <ModernHeader className="sticky top-0 z-10 backdrop-blur-md bg-background/80 border-b border-border/40" />
    <main className="p-8 max-w-7xl mx-auto">{children}</main>
  </div>
</div>
```

### Stats Card Component
```tsx
<Card className="relative overflow-hidden border-border/40 bg-card p-6 shadow-sm hover:shadow-md transition-shadow duration-200">
  <div className="flex items-center justify-between">
    <div>
      <p className="text-sm font-medium text-muted-foreground">{title}</p>
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.1, duration: 0.3 }}
      >
        <h2 className="mt-2 text-3xl font-bold text-card-foreground">{value}</h2>
      </motion.div>
      {description && (
        <p className="mt-2 text-xs text-muted-foreground">{description}</p>
      )}
      {trend && (
        <div className="mt-2 flex items-center">
          <span className="inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium bg-primary/10 text-primary">
            {trend.isPositive ? "+" : ""}{trend.value}%
          </span>
        </div>
      )}
    </div>
    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 text-primary">
      <Icon className="h-5 w-5" />
    </div>
  </div>
  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-primary/5 via-primary/20 to-primary/5" />
</Card>
```

## Chat Widget Implementation

### Core Components
- **ChatUI**: Main container component for the chat widget with enhanced styling and animations
- **ChatHeader**: Header with title and status indicators for different chat states
- **ChatMessages**: Container for displaying chat messages with smart auto-scrolling that respects manual scrolling
- **ChatBubble**: Individual message bubble with markdown support, code highlighting, and file attachments
- **ChatInput**: Floating inline input field with keyboard shortcuts, file drag-and-drop, and visual feedback
- **ChatToggleButton**: Animated button to toggle the chat widget with smooth transitions
- **SimpleChatWidget**: Wrapper component for ChatUI with client-side rendering and hydration error prevention

### SimpleChatWidget Component
```typescript
export interface SimpleChatWidgetProps {
  websiteId?: string;
  position?: "bottom-right" | "bottom-left";
  title?: string;
  primaryColor?: string;
  secondaryColor?: string;
  welcomeMessage?: string;
  initiallyOpen?: boolean;
}

export function SimpleChatWidget({
  websiteId,
  position = "bottom-right",
  title = "Chat Assistant",
  primaryColor = "#4F46E5",
  secondaryColor = "#FFFFFF",
  welcomeMessage = "Hi there! How can I help you today?",
  initiallyOpen = false,
}: SimpleChatWidgetProps) {
  const [mounted, setMounted] = useState(false);

  // Set mounted to true after the component mounts to avoid hydration issues
  useEffect(() => {
    setMounted(true);
  }, []);

  // Only render the content after the component has mounted on the client
  if (!mounted) {
    return null;
  }

  return (
    <ChatUI
      websiteId={websiteId}
      position={position}
      title={title}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      initiallyOpen={initiallyOpen}
      initialMessages={
        welcomeMessage
          ? [
              {
                role: "assistant",
                content: welcomeMessage,
              },
            ]
          : []
      }
    />
  );
}
```

### ChatUI Component
```typescript
export interface ChatUIProps {
  title?: string;
  position?: "bottom-right" | "bottom-left";
  placeholder?: string;
  initialMessages?: Array<{ role: "user" | "assistant"; content: string }>;
  className?: string;
  style?: React.CSSProperties;
  websiteId?: string;
  visitorId?: string;
  primaryColor?: string;
  secondaryColor?: string;
  initiallyOpen?: boolean;
}

export function ChatUI({
  title = "Chat Assistant",
  position = "bottom-right",
  placeholder = "Type your message...",
  initialMessages = [],
  className,
  style,
  websiteId,
  visitorId,
  primaryColor = "#4F46E5",
  secondaryColor = "#FFFFFF",
  initiallyOpen = false,
}: ChatUIProps) {
  const [isOpen, setIsOpen] = useState(initiallyOpen);
  const [isClient, setIsClient] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Generate a stable visitor ID if not provided
  const [generatedVisitorId] = useState(
    () => visitorId || generateStableId("visitor"),
  );

  // Integration with Vercel AI SDK
  const { messages, input, handleInputChange, handleSubmit, error, status } =
    useChat({
      api: websiteId ? `/api/chat/${websiteId}` : "/api/chat",
      body: {
        websiteId,
        visitorId: generatedVisitorId,
      },
      initialMessages,
      onError: (err) => {
        toast.error("An error occurred", {
          description: err.message || "Failed to send message",
        });
        console.error("Chat error:", err);
      },
    });

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (isClient && isOpen && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [isClient, isOpen, messages]);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  // Show the toggle button when chat is closed
  if (isClient && !isOpen) {
    return (
      <ChatToggleButton
        isOpen={isOpen}
        onClick={handleToggle}
        primaryColor={primaryColor}
        position={position}
      />
    );
  }

  return (
    <>
      {isClient && (
        <Card
          style={
            {
              ...style,
              "--chat-primary-color": primaryColor,
              "--chat-secondary-color": secondaryColor,
            } as React.CSSProperties
          }
          className={cn(
            "fixed z-50 flex flex-col",
            "w-[360px] h-[600px] max-h-[calc(100vh-2rem)]",
            "shadow-lg rounded-lg overflow-hidden",
            "transition-all duration-300 ease-in-out",
            position === "bottom-right"
              ? "bottom-4 right-4"
              : "bottom-4 left-4",
            className,
          )}
        >
          <ChatHeader
            title={title}
            status={
              status === "submitted" ? "thinking" : error ? "error" : "idle"
            }
            onMinimize={handleToggle}
            isMinimized={false}
          />

          <div className="flex-1 flex flex-col overflow-hidden">
            <div className="flex-1 overflow-y-auto p-4" ref={messagesEndRef}>
              <ChatMessages
                messages={messages.map((m) => {
                  // Use a stable timestamp to prevent hydration errors
                  const timestamp = isClient ? new Date() : new Date(0);

                  return {
                    id: m.id || generateStableId("msg"),
                    content: m.content,
                    role: m.role as ChatRole,
                    timestamp,
                  };
                })}
                status={
                  status === "submitted" ? "thinking" : error ? "error" : "idle"
                }
              />
            </div>

            <ChatInput
              value={input}
              onChange={handleInputChange}
              onSubmit={(e) => {
                e.preventDefault();
                handleSubmit(e);
              }}
              disabled={status === "submitted"}
              placeholder={placeholder}
            />
          </div>
        </Card>
      )}
    </>
  );
}
```

## Hydration Error Fixes

### Issues Addressed
- Inconsistent IDs between server and client rendering
- Time-based rendering differences
- Scroll behavior causing hydration mismatches
- Client-side only features being rendered on server

### Solutions Implemented
- Created `generateStableId` utility function for consistent ID generation
- Implemented client-side detection in components with `useState` and `useEffect`
- Fixed timestamp rendering to only occur on the client side
- Improved scroll behavior to only run on the client side
- Ensured deterministic rendering between server and client
- Added conditional rendering for time-dependent content

```typescript
// Example of client-side detection
"use client";

import { useEffect, useState } from "react";

export function ClientComponent() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Only render time-dependent content on the client
  return (
    <div>
      {isClient ? (
        <span>Current time: {new Date().toLocaleTimeString()}</span>
      ) : (
        <span>Loading time...</span>
      )}
    </div>
  );
}
```

## Accessibility Features
- Keyboard navigation support
- ARIA labels for interactive elements
- Focus management
- Color contrast compliance
- Screen reader compatibility

## Implementation Status
- ✅ Dashboard UI redesign: Completed
- ✅ Website management UI redesign: Completed
- ✅ Color system implementation: Completed
- ✅ Component styling: Completed
- ✅ Dark mode support: Completed
- ✅ Collapsible sidebar: Completed
- ✅ Chat widget implementation: Completed
- ✅ Hydration error fixes: Completed
- ✅ Vector search integration: Completed
- ✅ SimpleChatWidget implementation: Completed
- ✅ Chat UI enhancements: Completed
  - ✅ Typing indicators: Completed
  - ✅ Markdown rendering improvements: Completed
  - ✅ File attachment support: Completed
  - ✅ Message reactions: Completed
  - ✅ Mobile responsiveness: Completed
- ❌ Settings UI redesign: Planned
- ❌ Accessibility testing: Planned

## Next Steps
1. **Complete UI Redesign**
   - Redesign settings and authentication screens
   - Add more micro-interactions and animations
   - Test across different browsers and devices

2. **Further Enhance Chat Widget Features**
   - Implement file upload functionality (server-side handling)
   - Add image preview for attachments
   - Implement chat history and conversation persistence
   - Add typing indicators for user messages
   - Improve vector search relevance with hybrid search
   - Add message reactions and emoji support
   - Implement voice input for chat messages

3. **Improve Accessibility**
   - Conduct comprehensive accessibility audit
   - Implement keyboard navigation improvements
   - Enhance screen reader support
   - Ensure proper focus management
   - Maintain color contrast compliance

4. **Performance Optimization**
   - Optimize bundle size for faster loading
   - Implement lazy loading for chat components
   - Add performance monitoring for chat interactions
   - Optimize image and file handling
   - Implement progressive loading for chat history
