# Memory Bank

## Overview
The Memory Bank is the system responsible for storing and retrieving conversation history in the Bubl platform. It enables persistent chat sessions, analytics, and personalized user experiences.

## Architecture

### Database Schema
The Memory Bank uses two primary tables:

1. **Conversations Table**
```typescript
export const conversationsTable = pgTable("conversations", {
  id,                                                  // UUID primary key
  websiteId: uuid("website_id").references(() => websitesTable.id).notNull(),
  visitorId: varchar("visitor_id", { length: 255 }).notNull(),
  status: varchar("status", { length: 50 }).notNull().default("active"),
  createdAt,                                           // Timestamp
  updatedAt,                                           // Timestamp
});
```

2. **Messages Table**
```typescript
export const messagesTable = pgTable("messages", {
  id,                                                  // UUID primary key
  conversationId: uuid("conversation_id").references(() => conversationsTable.id).notNull(),
  content: text("content").notNull(),
  role: varchar("role", { length: 50 }).notNull(),     // "user" or "assistant"
  createdAt,                                           // Timestamp
});
```

### Repository Layer
The Memory Bank uses the repository pattern for data access:

1. **ConversationsRepository**
   - `create(conversation)`: Create a new conversation
   - `getById(id)`: Get a conversation by ID
   - `getByWebsiteId(websiteId, limit, offset)`: Get conversations for a website
   - `getByVisitorId(visitorId, limit, offset)`: Get conversations for a visitor
   - `update(id, conversation)`: Update a conversation
   - `delete(id)`: Delete a conversation
   - `list(limit, offset)`: List all conversations with pagination

2. **MessagesRepository**
   - `create(message)`: Create a new message
   - `getById(id)`: Get a message by ID
   - `getByConversationId(conversationId, limit, offset)`: Get messages for a conversation
   - `update(id, message)`: Update a message
   - `delete(id)`: Delete a message
   - `countByConversationId(conversationId)`: Count messages in a conversation

## Implementation Status

### ✅ Database Schema: Implemented
- `conversationsTable` in `src/lib/db/schema/conversations.ts` implemented
- `messagesTable` in `src/lib/db/schema/messages.ts` implemented
- Schema matches PRD requirements with fields for tracking conversations and messages

### ✅ Repository Layer: Implemented
- `ConversationsRepository` in `src/lib/db/repositories/conversations.ts` implemented
- `MessagesRepository` in `src/lib/db/repositories/messages.ts` implemented
- Repository methods for CRUD operations on conversations and messages are available

### ✅ API Endpoints: Implemented
- Dashboard API endpoints for analytics and website management are implemented
- Chat operation endpoints implemented:
  - `POST /api/chat/:websiteId` in `src/app/api/chat/[websiteId]/route.ts`
  - `GET /api/chat/history/:conversationId` in `src/app/api/chat/history/[conversationId]/route.ts`

### ✅ Chat Widget Implementation: Implemented
- Chat widget component (`ChatUI.tsx`) fully implemented with MastrAI integration
- Uses Vercel AI SDK's `useChat` hook for streaming responses
- Persists messages to the database using the conversation memory system
- Supports website-specific context retrieval

## Implemented Features

1. **Conversation Memory System**
   - Created `ConversationMemory` class in `src/lib/ai/memory/conversation-memory.ts`
   - Implemented methods for creating and retrieving conversations
   - Added functionality to store and retrieve messages
   - Implemented visitor tracking with session management

2. **MastrAI Integration**
   - Connected MastrAI agent to the database for context retrieval
   - Implemented streaming responses with Vercel AI SDK
   - Added error handling and fallback responses
   - Created website-specific chat API route

3. **Chat Widget Integration**
   - Updated `ChatUI` component to pass website and visitor IDs
   - Implemented message persistence across sessions
   - Added support for streaming responses
   - Maintained minimizable chat widget functionality

4. **Hydration Error Fixes**
   - Created `generateStableId` utility function for consistent ID generation
   - Implemented client-side detection in components
   - Fixed timestamp rendering to only occur on the client side
   - Updated API routes to use stable ID generation
   - Fixed params type in the history API route
   - Improved scroll behavior to only run on the client side

## Next Steps

1. **Implement Vector Search for Website Content**
   - Create database tables for website pages and embeddings
   - Implement a crawler to extract content from websites
   - Generate embeddings for website content
   - Enhance the website context tool to use vector search

2. **Test Chat Components and MastrAI Integration**
   - Write unit tests for the website context tool
   - Test the conversation memory system
   - Verify chat API routes with real website data
   - Test the chat UI with the MastrAI integration
   - Verify hydration fixes work across different browsers

3. **Add Rate Limiting and Security**
   - Implement rate limiting for chat endpoints
   - Add request validation
   - Implement proper error handling
   - Add security headers

## Implementation Decisions

- PostgreSQL with pgvector for database storage
- Drizzle ORM for database access
- Repository pattern for data access layer
- Conversation and message tables for storing chat history
- UUID primary keys for all entities
- Visitor ID tracking for session management
- Conversation status tracking (active/inactive)
- Timestamps for all records
- Role-based message storage (user/assistant)
- RESTful API design for chat endpoints
- Standardized response format for all API endpoints
- Type-safe request/response handling with Zod
- Multi-tenant data isolation for organizations
- MastrAI integration for memory management
- Vercel AI SDK for streaming responses
- AssistantUI components for chat interface
