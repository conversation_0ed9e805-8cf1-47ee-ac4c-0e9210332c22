# Persistent Crawl Configuration Implementation

## Overview

This document outlines the implementation of persistent crawl configurations for the Bubl platform. This feature allows the system to remember and reuse crawl settings for each website, providing a better user experience and ensuring consistency in recurring crawls.

## Problem Statement

Previously, the Bubl platform did not store crawl configurations for each website. Every time a user visited the crawl configuration page, they had to reconfigure the crawler from scratch, even if they had previously set up optimal settings for that website. This led to:

1. **Poor User Experience**: Users had to remember and re-enter their preferred settings each time
2. **Inconsistent Crawls**: Different configurations could lead to inconsistent content extraction
3. **Configuration Errors**: Manual reconfiguration increased the chance of errors
4. **Time Inefficiency**: Users spent unnecessary time reconfiguring the crawler

## Implementation Details

### 1. Database Schema Update

Added a `crawlConfiguration` field to the `websitesTable` schema using the `jsonb` type to store the complete crawl configuration:

```typescript
export const websitesTable = pgTable("websites", {
  // Existing fields...
  crawlConfiguration: jsonb("crawl_configuration"), // Store the last used crawl configuration
  // Other fields...
});
```

### 2. Repository Methods

Added methods to the `WebsitesRepository` class to save and retrieve crawl configurations:

```typescript
/**
 * Save crawl configuration for a website
 */
async saveCrawlConfiguration(id: string, configuration: any): Promise<Website> {
  const [updatedWebsite] = await db
    .update(websitesTable)
    .set({
      crawlConfiguration: configuration,
      updatedAt: new Date().toISOString(),
    })
    .where(eq(websitesTable.id, id))
    .returning();

  return updatedWebsite;
}

/**
 * Get crawl configuration for a website
 */
async getCrawlConfiguration(id: string): Promise<any | null> {
  const website = await this.getById(id);
  return website?.crawlConfiguration || null;
}
```

### 3. API Endpoints

#### Saving Configuration

Updated the `/api/dashboard/websites/[id]/crawl` endpoint to save the configuration when a crawl is started:

```typescript
// Save the crawl configuration for future use
await websitesRepository.saveCrawlConfiguration(id, validatedData);
```

#### Retrieving Configuration

Created a new `/api/dashboard/websites/[id]/config` endpoint to retrieve the website configuration:

```typescript
export async function GET(
  req: Request,
  {
    params,
  }: {
    params: Promise<{ id: string }>;
  }
) {
  try {
    // Get the website ID from the URL
    const { id } = await params;

    // Get the user from Clerk
    const { userId } = await auth();

    // Check if the user is authenticated
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the website
    const website = await websitesRepository.getById(id);

    // Check if the website exists
    if (!website) {
      return NextResponse.json({ error: "Website not found" }, { status: 404 });
    }

    // Get the crawl configuration
    const crawlConfiguration = await websitesRepository.getCrawlConfiguration(id);

    // Return the website with its crawl configuration
    return NextResponse.json({
      id: website.id,
      name: website.name,
      url: website.url,
      crawlFrequency: website.crawlFrequency,
      lastCrawledAt: website.lastCrawledAt,
      status: website.status,
      crawlConfiguration,
    });
  } catch (error) {
    console.error("Error in website config API route:", error);
    return NextResponse.json(
      { error: "Failed to get website configuration" },
      { status: 500 }
    );
  }
}
```

### 4. UI Component Updates

Updated the `CrawlConfigurationForm` component to fetch and use the saved configuration:

```typescript
// Default configuration
const defaultConfig: CrawlConfig = {
  maxPages: defaultMaxPages,
  maxDepth: 3,
  includePatterns: [],
  excludePatterns: [
    // Default exclusions...
  ],
  generateEmbeddings: true,
  crawlFrequency: "WEEKLY",
  respectRobotsTxt: true,
  crawlDelay: 1000,
  incrementalCrawl: true,
};

const [config, setConfig] = useState<CrawlConfig>(defaultConfig);
const [isLoadingConfig, setIsLoadingConfig] = useState(true);

// Fetch saved configuration on component mount
useEffect(() => {
  const fetchSavedConfig = async () => {
    try {
      setIsLoadingConfig(true);
      const response = await fetch(`/api/dashboard/websites/${websiteId}/config`);
      
      if (response.ok) {
        const data = await response.json();
        if (data.crawlConfiguration) {
          // Merge saved configuration with default values
          setConfig({
            ...defaultConfig,
            ...data.crawlConfiguration,
            // Ensure maxPages doesn't exceed the plan limit
            maxPages: Math.min(
              data.crawlConfiguration.maxPages || defaultMaxPages,
              pagesPerWebsiteLimit
            ),
          });
        }
      }
    } catch (error) {
      console.error("Error fetching saved configuration:", error);
      // If there's an error, we'll use the default configuration
    } finally {
      setIsLoadingConfig(false);
    }
  };
  
  fetchSavedConfig();
}, [websiteId, defaultMaxPages, pagesPerWebsiteLimit]);
```

## Benefits

1. **Improved User Experience**: Users don't have to reconfigure the crawler each time
2. **Consistency**: Ensures that recurring crawls use the same configuration
3. **Efficiency**: Saves time for users who frequently crawl the same websites
4. **Reduced Errors**: Minimizes the chance of configuration mistakes

## Future Enhancements

1. **Configuration Versioning**: Store multiple versions of configurations for each website
2. **Configuration Templates**: Allow users to save and reuse configurations across websites
3. **Configuration Recommendations**: Suggest optimal settings based on website characteristics
4. **Configuration Sharing**: Allow users to share configurations with team members
5. **Configuration Analytics**: Track which configurations perform best for different websites
