# Widget Analytics and Performance Improvements

This document outlines the enhancements made to the Bubl widget's analytics and performance monitoring capabilities.

## Overview

We've implemented comprehensive analytics tracking and performance optimizations for the chat widget to:

1. Measure the widget's impact on host websites
2. Track user interactions and performance metrics
3. Provide actionable insights for optimization
4. Improve the widget's loading and rendering performance

## Analytics Enhancements

### Core Web Vitals Tracking

We've implemented tracking of all Core Web Vitals metrics:

- **Largest Contentful Paint (LCP)**: Measures loading performance
- **First Input Delay (FID)**: Measures interactivity
- **Cumulative Layout Shift (CLS)**: Measures visual stability
- **First Contentful Paint (FCP)**: Measures when first content is painted
- **Time to First Byte (TTFB)**: Measures server response time
- **Time to Interactive (TTI)**: Measures when the page becomes interactive

### Network Information Tracking

We now collect network information to understand how connection quality affects widget performance:

- **Effective Connection Type**: 4G, 3G, etc.
- **Downlink Speed**: Connection bandwidth
- **Round Trip Time (RTT)**: Network latency
- **Save Data Mode**: Whether data-saving is enabled

### Enhanced Analytics Dashboard

The analytics dashboard has been updated to display:

- Detailed Core Web Vitals metrics
- Network performance statistics
- Automatic performance assessment (Good, Needs Improvement, Poor)
- Personalized optimization recommendations based on collected metrics

## Performance Optimizations

### Widget Loader Script

The widget loader script has been optimized for better performance:

- **Resource Preloading**: CSS is now preloaded to improve loading performance
- **Script Loading Optimization**: Non-critical scripts use `defer` and `async`
- **Resource Prioritization**: Using `fetchPriority` to prioritize critical resources
- **Error Handling**: Improved error tracking and reporting

### CSS Optimizations

The widget's CSS has been optimized for better rendering performance:

- **Hardware Acceleration**: Using `transform: translateZ(0)` for smoother animations
- **Content Visibility**: Using `content-visibility: auto` to optimize rendering
- **Layout Containment**: Using `contain` property to reduce layout recalculations
- **Box Sizing**: Using `box-sizing: border-box` to reduce layout shifts

### Iframe Optimizations

The widget iframe has been optimized for better loading and security:

- **Lazy Loading**: Using `loading="lazy"` for deferred loading
- **Resource Prioritization**: Using `importance="low"` for non-visible iframe
- **Security Enhancements**: Using appropriate `sandbox` attributes
- **Performance Tracking**: Tracking iframe load time and performance metrics

## Implementation Details

### Web Vitals Library Integration

We've integrated the `web-vitals` library to collect standardized performance metrics:

```javascript
import * as webVitals from "web-vitals";

// Track Core Web Vitals
webVitals.onCLS((metric) => trackMetric("widget_cls", metric.value));
webVitals.onFID((metric) => trackMetric("widget_fid", metric.value));
webVitals.onLCP((metric) => trackMetric("widget_lcp", metric.value));
webVitals.onTTFB((metric) => trackMetric("widget_ttfb", metric.value));
webVitals.onFCP((metric) => trackMetric("widget_fcp", metric.value));
```

### Analytics Data Structure

The analytics data structure has been expanded to include:

```typescript
interface WebsiteStats {
  // Existing metrics
  totalConversations: number;
  totalMessages: number;
  averageResponseTime: number;
  ragUsagePercentage: number;
  
  // New performance metrics
  webVitals: {
    cls: { avg: number };
    lcp: { avg: number };
    fcp: { avg: number };
    ttfb: { avg: number };
    fid: { avg: number };
  };
  networkInfo: {
    avgDownlink: number;
    avg4gPercentage: number;
    avg3gPercentage: number;
    avgRtt: number;
  };
}
```

### Key Files Modified

1. **public/widget/v1/loader.js**: Enhanced with performance tracking and optimizations
2. **public/widget/v1/web-vitals.js**: Added for Core Web Vitals tracking
3. **public/widget/v1/styles.css**: Optimized for better rendering performance
4. **src/components/widget/widget.tsx**: Updated with comprehensive performance tracking
5. **src/lib/db/repositories/chatAnalytics.ts**: Enhanced to store and process new metrics
6. **src/app/(main)/dashboard/websites/[id]/analytics/client.tsx**: Updated to display detailed performance metrics

## Future Improvements

1. **Real User Monitoring (RUM)**: Implement more comprehensive RUM to track actual user experiences
2. **Performance Budgets**: Set performance budgets and alert when they're exceeded
3. **A/B Testing**: Test different widget configurations for optimal performance
4. **Adaptive Loading**: Implement adaptive loading based on device and network capabilities
5. **Offline Support**: Add offline support for better user experience in poor network conditions

## Status

✅ Core Web Vitals tracking implemented
✅ Performance optimizations for widget loader and CSS
✅ Enhanced analytics dashboard with performance metrics
✅ Network information tracking
📋 Real User Monitoring (planned)
📋 Performance budgets (planned)
📋 Adaptive loading (planned)
