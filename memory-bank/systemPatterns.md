# System Patterns

## Type Safety Patterns

### Clerk Integration Pattern
```typescript
// 1. Define types for external service data
interface ExternalType {
  // ...
}

// 2. Cast through unknown for type safety
const data = (externalData as unknown as ExternalType);

// 3. Provide fallback for nullable data
const items = data.items || [];
```

### API Route Pattern
```typescript
export async function GET(request: Request) {
  try {
    // 1. Validate input
    const { param } = validateInput(request);

    // 2. Type-safe database queries
    const result = await db
      .select()
      .from(table)
      .where(eq(table.field, param));

    // 3. Typed response
    return NextResponse.json(result);
  } catch (error) {
    // 4. Error handling
    return handleError(error);
  }
}
```

## Authentication Flow

### Organization Access Pattern
1. Check user authentication
2. Verify organization membership
3. Map to database organization
4. Control access to resources

### Setup Flow Pattern
1. Verify user status
2. Check organization existence
3. Validate website setup
4. Grant dashboard access

## Error Handling <PERSON>tern
```typescript
try {
  // Main logic
} catch (error) {
  console.error("Context-specific error message:", error);
  return NextResponse.json(
    { error: "User-friendly message" },
    { status: appropriate_status_code }
  );
}
```

## Type Safety Guidelines

### External Service Integration
1. Define explicit types
2. Use type casting through `unknown`
3. Provide fallback values
4. Handle potential errors

### Database Operations
1. Use DrizzleORM types
2. Type-safe queries
3. Proper error handling
4. Result validation

### API Routes
1. Request validation
2. Type-safe processing
3. Error boundary implementation
4. Typed responses

## Build Process Pattern

### Quality Checks
1. TypeScript compilation
2. Biome linting
3. Type verification
4. Build optimization

### Deployment Flow
1. Quality checks
2. Build process
3. Asset optimization
4. Deployment

## Development Guidelines

### Code Organization
1. Feature-based structure
2. Type definitions
3. API routes
4. Components

### Type Safety
1. Explicit types
2. No any usage
3. Proper error handling
4. Documentation

## Authentication & Authorization Pattern

- Uses Clerk for authentication with Next.js middleware integration
- Route-based protection with public/private route matchers
- Automatic session management and user context
- Protected routes require authentication, public routes are accessible to all
- Dashboard routes have additional user validation checks

## CSRF Protection Pattern

- Middleware checks for CSRF header presence on state-changing requests (POST, PUT, DELETE, PATCH)
- Full CSRF token validation (e.g., hash/cookie) is performed in API handlers (Node.js runtime)
- Ensures Edge compatibility and robust security

## Input Validation Pattern (Zod)

- Each API endpoint defines a Zod schema for request validation
- Schemas are colocated in src/lib/validation (e.g., chat, website, analytics, crawl)
- Use safeParse for validation; on failure, return or throw standardized 400 error with details
- Standard error response: { error: string, details: ZodError.flatten() }
- TypeScript types are inferred from Zod schemas for downstream safety
- Reusable validateRequest utility for DRY validation logic
- Integrated with ApiError for consistent error handling