# Chat Embed and Widget Fixes

This document outlines the issues and fixes related to the chat embed code and widget functionality.

## Issues Addressed

1. **Chat Embed Code Not Working**: The embed code was not working properly when added to external websites.
2. **Authentication Issues**: The widget was trying to connect to Clerk authentication when embedded on external sites.
3. **CORS Errors**: Cross-Origin Resource Sharing (CORS) errors were preventing the widget from communicating with the API.
4. **Hydration Errors**: Server-side rendering mismatches were causing hydration errors in the widget.
5. **Adding New Websites**: The functionality to add new websites was not working properly.

## Implemented Fixes

### 1. Authentication Isolation

- Created a separate route group `(widget)` in `src/app/(widget)/` that doesn't inherit the root layout with Clerk
- Added a dedicated layout file for the widget route group that doesn't include Clerk
- This ensures that the widget routes are completely isolated from the authentication system

```tsx
// src/app/(widget)/layout.tsx
import type { Metadata } from "next"
import { Inter } from "next/font/google"
import "../globals.css"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Bubl Widget",
  description: "Chat widget for Bubl",
}

// This layout is completely separate from the main app layout
// and does not include Clerk or any authentication
export default function WidgetLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>{children}</body>
    </html>
  )
}
```

### 2. CORS Support

- Created a CORS middleware utility in `src/lib/middleware/cors.ts` to handle CORS headers
- Added OPTIONS handlers to all API routes used by the widget to handle CORS preflight requests
- Added CORS headers to all responses from the API routes

```typescript
// Example of CORS middleware implementation
export function corsMiddleware(
  request: NextRequest,
  response: NextResponse,
  options: {
    allowedOrigins?: string[];
    allowedMethods?: string[];
    allowedHeaders?: string[];
    exposedHeaders?: string[];
    maxAge?: number;
    credentials?: boolean;
  } = {}
): NextResponse {
  // Default options
  const {
    allowedOrigins = ["*"],
    allowedMethods = ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH", "HEAD"],
    allowedHeaders = ["Content-Type", "Authorization"],
    exposedHeaders = [],
    maxAge = 86400, // 24 hours
    credentials = true,
  } = options;

  // Get the origin from the request
  const origin = request.headers.get("origin") || "";

  // Check if the origin is allowed
  const isAllowedOrigin = allowedOrigins.includes("*") || allowedOrigins.includes(origin);

  // Set CORS headers
  const headers = new Headers(response.headers);

  // Set Access-Control-Allow-Origin
  if (isAllowedOrigin) {
    headers.set("Access-Control-Allow-Origin", allowedOrigins.includes("*") ? "*" : origin);
  }

  // Set Access-Control-Allow-Credentials if needed
  if (credentials) {
    headers.set("Access-Control-Allow-Credentials", "true");
  }

  // Set Access-Control-Allow-Methods
  headers.set("Access-Control-Allow-Methods", allowedMethods.join(", "));

  // Set Access-Control-Allow-Headers
  headers.set("Access-Control-Allow-Headers", allowedHeaders.join(", "));

  // Set Access-Control-Expose-Headers if needed
  if (exposedHeaders.length > 0) {
    headers.set("Access-Control-Expose-Headers", exposedHeaders.join(", "));
  }

  // Set Access-Control-Max-Age
  headers.set("Access-Control-Max-Age", maxAge.toString());

  // Create a new response with the CORS headers
  return NextResponse.json(response.json(), {
    status: response.status,
    statusText: response.statusText,
    headers,
  });
}
```

### 3. Public API Routes

- Created a public API route at `/api/widget/config/[websiteId]` to fetch chat configuration without authentication
- Updated the middleware to exclude widget and chat API routes from authentication

```typescript
// Example of public API route for chat configuration
export async function GET(
  request: NextRequest,
  {
    params,
  }: {
    params: Promise<{ websiteId: string }>;
  }
) {
  try {
    const { websiteId } = await params;
    
    // Get the website by ID
    const website = await websitesRepository.getById(websiteId);
    
    if (!website) {
      const errorResponse = NextResponse.json(
        { error: "Website not found" },
        { status: 404 }
      );
      return corsMiddleware(request, errorResponse);
    }
    
    // Get the chat configuration
    const chatConfig = await chatConfigurationsRepository.createDefaultIfNotExists(websiteId);
    
    // Convert position from BOTTOM_RIGHT to bottom-right format
    const position = chatConfig.position?.toLowerCase().replace("_", "-");
    
    // Return the chat configuration with CORS headers
    const response = NextResponse.json({
      websiteId,
      primaryColor: chatConfig.primaryColor,
      secondaryColor: chatConfig.secondaryColor,
      position,
      welcomeMessage: chatConfig.welcomeMessage,
      headerText: chatConfig.headerText,
    });
    
    return corsMiddleware(request, response);
  } catch (error) {
    console.error("Error fetching chat configuration for widget:", error);
    const errorResponse = NextResponse.json(
      { error: "Failed to fetch chat configuration" },
      { status: 500 }
    );
    return corsMiddleware(request, errorResponse);
  }
}
```

### 4. Widget Loader Script

- Updated the loader.js script to dynamically determine the base URL from the script source
- Added error handling and improved the widget initialization process
- Modified the script to fetch configuration from the public API route

```javascript
// Get the base URL from the script src
const scriptSrc = document.currentScript ? document.currentScript.src : '';
const baseUrl = scriptSrc.substring(0, scriptSrc.indexOf('/widget/v1/loader.js'));

// Fetch the chat configuration from the public API
fetch(`${baseUrl}/api/widget/config/${config.websiteId}`)
  .then(response => {
    if (!response.ok) {
      throw new Error(`Failed to fetch chat configuration: ${response.status}`);
    }
    return response.json();
  })
  .then(serverConfig => {
    // Merge server config with client config (client config takes precedence)
    const mergedConfig = {
      ...serverConfig,
      ...config,
      // Always use the websiteId from the client config
      websiteId: config.websiteId
    };
    
    // Set the iframe source with configuration parameters
    const params = new URLSearchParams({
      websiteId: mergedConfig.websiteId,
      primaryColor: mergedConfig.primaryColor || '#4F46E5',
      secondaryColor: mergedConfig.secondaryColor || '#FFFFFF',
      position: mergedConfig.position || 'bottom-right',
      welcomeMessage: mergedConfig.welcomeMessage || 'Hi there! How can I help you today?',
      headerText: mergedConfig.headerText || 'Chat Assistant',
      initiallyOpen: mergedConfig.initiallyOpen ? 'true' : 'false'
    });
    
    iframe.src = `${baseUrl}/widget/chat?${params.toString()}`;
  });
```

### 5. Hydration Error Fix

- Made the widget chat page a client component with the "use client" directive
- Added a client-side only rendering approach using the `mounted` state
- This ensures that the component only renders on the client, avoiding hydration mismatches

```tsx
"use client"

import { ChatUI } from "@/components/chat-ui/ChatUI"
import { useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import "../../widget.css"

export default function WidgetChatPage() {
  // Use state to avoid hydration mismatch
  const [mounted, setMounted] = useState(false)
  const searchParams = useSearchParams()
  
  // Set mounted to true after the component mounts
  useEffect(() => {
    setMounted(true)
  }, [])
  
  // Only render the content after the component has mounted on the client
  if (!mounted) {
    return <div className="h-screen w-full flex items-center justify-center">Loading...</div>
  }
  
  // Get parameters from the URL
  const websiteId = searchParams.get("websiteId") || ""
  const primaryColor = searchParams.get("primaryColor") || "#4F46E5"
  const position = (searchParams.get("position") || "bottom-right") as "bottom-right" | "bottom-left"
  const welcomeMessage = searchParams.get("welcomeMessage") || "Hi there! How can I help you today?"
  const headerText = searchParams.get("headerText") || "Chat Assistant"
  
  // Render the chat UI
  return (
    <div className="h-screen w-full" style={{ "--chat-primary-color": primaryColor } as React.CSSProperties}>
      <ChatUI
        websiteId={websiteId}
        position={position}
        title={headerText}
        placeholder="Type your message..."
        initialMessages={[
          {
            role: "assistant",
            content: welcomeMessage,
          },
        ]}
        style={{
          height: "100%",
          width: "100%",
          maxHeight: "none",
          position: "relative",
          bottom: "auto",
          right: "auto",
          left: "auto",
        }}
      />
    </div>
  )
}
```

### 6. API Response Handling

- Updated the client-side API handling in `src/lib/api/client.ts` to correctly handle the API response format
- Ensured the EmbedCodeGenerator component correctly handles errors and success responses

```typescript
// Handle both API response formats:
// 1. { data: T, error?: never } - Standard API result
// 2. { success: boolean, data: T, error?: string } - Dashboard API format

if (responseData.success === false) {
  throw new ApiRequestError(
    responseData.error || "Unknown error",
    "API_ERROR",
    response.status,
    rateLimitInfo as RateLimitInfo,
  );
}

// If the response has a success property and data property, return the data
if (responseData.success === true && responseData.data) {
  return responseData.data as T;
}
```

## Results

These fixes have resolved the issues with the chat embed code and widget functionality:

1. The chat embed code now works correctly when added to external websites
2. The widget no longer tries to connect to Clerk authentication
3. CORS errors have been resolved, allowing cross-origin communication
4. Hydration errors have been fixed by ensuring consistent client-side rendering
5. The functionality to add new websites now works properly

## Next Steps

1. Add rate limiting for public API routes to prevent abuse
2. Implement better error handling and user feedback
3. Add analytics to track chat usage from different websites
4. Enhance the widget with additional features and customization options
