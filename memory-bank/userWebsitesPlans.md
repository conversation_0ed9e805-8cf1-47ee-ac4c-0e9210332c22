# User-Website Associations and SAAS Plans

## Overview
This document details the implementation of user-website associations and SAAS plan limitations for the Bubl platform. These features enable users to manage their own websites and enforce usage limits based on subscription plans.

## Architecture

### Database Schema
The implementation adds three key components to the database schema:

1. **User-Website Association**
   - Added `userId` field to the `websites` table to associate websites with users
   - Created relations between users and websites

2. **Plans Table**
   - Created a new `plans` table to define subscription tiers
   - Includes fields for website limits, pages per website, and messages per day

3. **User Subscription**
   - Added `planId` field to the `users` table to associate users with plans
   - Added subscription status and expiration fields

### Schema Details

#### Plans Table
```typescript
export const plansTable = pgTable("plans", {
  id,
  name: varchar("name", { length: 100 }).notNull(),
  description: varchar("description", { length: 500 }),
  price: integer("price").notNull(), // Price in cents
  websiteLimit: integer("website_limit").notNull().default(1),
  pagesPerWebsiteLimit: integer("pages_per_website_limit").notNull().default(100),
  messagesPerDayLimit: integer("messages_per_day_limit").notNull().default(100),
  features: varchar("features", { length: 1000 }),
  isActive: varchar("is_active", { length: 50 }).default("ACTIVE"),
  createdAt,
  updatedAt,
});
```

#### User Schema Updates
```typescript
// Added to usersTable
planId: uuid("plan_id").references(() => plansTable.id),
subscriptionStatus: varchar("subscription_status", { length: 50 }).default("FREE"),
subscriptionExpiresAt: timestamp("subscription_expires_at", {
  withTimezone: true,
  mode: "string",
}),
```

#### Website Schema Updates
```typescript
// Added to websitesTable
userId: uuid("user_id").references(() => usersTable.id).notNull(),
```

### Relations
To avoid circular dependencies, relations are defined in a separate file:

```typescript
// User relations
export const usersRelations = relations(usersTable, ({ many, one }) => ({
  websites: many(websitesTable),
  plan: one(plansTable, {
    fields: [usersTable.planId],
    references: [plansTable.id],
  }),
}));

// Website relations
export const websitesRelations = relations(websitesTable, ({ one }) => ({
  user: one(usersTable, {
    fields: [websitesTable.userId],
    references: [usersTable.id],
  }),
}));

// Plan relations
export const plansRelations = relations(plansTable, ({ many }) => ({
  users: many(usersTable),
}));
```

## API Implementation

### Website API Routes
Updated the website API routes to:
1. Filter websites by user ID
2. Check plan limits before creating new websites
3. Verify user ownership before updating or deleting websites

Key changes:
- Modified `/api/dashboard/websites` to only return websites owned by the current user
- Added plan limit checks in the POST method to prevent exceeding website limits
- Updated `/api/dashboard/websites/[id]` routes to verify user ownership

### User Plan API
Created a new API route to fetch the user's plan information:
- `/api/dashboard/user/plan` - Returns the user's current plan and subscription details

## UI Components

### Plan Info Component
Created a new `PlanInfo` component to display:
- Current plan name
- Website usage (count/limit)
- Plan features
- Upgrade button for free users

```tsx
export function PlanInfo() {
  const { data: plan } = useUserPlan();
  const { data: websites } = useWebsites();

  const websiteCount = websites?.length || 0;
  const websiteLimit = plan?.websiteLimit || 1;
  const websitePercentage = Math.min(
    Math.round((websiteCount / websiteLimit) * 100),
    100
  );

  // Component rendering...
}
```

### Dashboard Integration
Added the `PlanInfo` component to the dashboard to provide users with visibility into their plan limits.

## Migration Strategy

### SQL Migration
Created a migration file `add_user_websites_plans.sql` that:
1. Creates the plans table
2. Adds plan and subscription fields to the users table
3. Adds userId to the websites table
4. Creates indexes for faster lookups
5. Inserts default plans (Free, Pro, Enterprise)

### Migration Script
Created a script `apply-user-websites-plans-migration.ts` to:
1. Apply the SQL migration
2. Assign existing websites to users
3. Set up default plans

## Implementation Status

### ✅ Database Schema: Implemented
- Added userId to websites table
- Created plans table with subscription tiers
- Added planId and subscription fields to users table
- Created relations between users, websites, and plans

### ✅ API Routes: Implemented
- Updated website API routes to filter by user ID
- Added plan limit checks to prevent exceeding limits
- Created user plan API route
- Implemented plan usage API endpoint
- Fixed plan usage API to correctly display website and page counts

### ✅ UI Components: Implemented
- Created PlanInfo component
- Added plan info to dashboard
- Added visual indicators for website, page, and message limits
- Added helpful messages when limits are reached
- Improved error handling in the plan usage component
- Added refresh button to the plan usage component

### ✅ Plan Limits Enforcement: Implemented
- Updated website creation to respect plan limits
- Updated website crawler to respect page limits
- Updated chat API to respect message limits
- Added visual indicators when limits are reached
- Added upgrade prompts when limits are reached

### ✅ Plans Page: Implemented
- Created a dedicated page for plan management
- Added ability to switch between plans
- Added visual indicators for the current plan
- Added plan comparisons with feature lists

### ✅ Website Management: Improved
- Updated WebsiteList component to display all user websites
- Fixed "Add New Website" form to respect plan limits
- Added plan information to the websites page
- Updated sidebar to show all user websites for easier navigation
- Added collapsible menu sections with toggle functionality

### ✅ Migration: Implemented
- Created SQL migration file
- Created migration script
- Added npm script for running the migration

## Next Steps

1. **Payment Processing**
   - Integrate with a payment processor (Stripe)
   - Implement subscription management
   - Handle plan upgrades and downgrades
   - Add payment history and receipts

2. **Usage Analytics**
   - Track website usage metrics over time
   - Monitor message limits with daily resets
   - Provide usage insights to users
   - Add usage forecasting

3. **Admin Interface**
   - Create an admin interface for managing plans
   - Allow admins to adjust user limits
   - Provide tools for monitoring system usage
   - Add ability to create custom plans

4. **Notifications**
   - Add email notifications for approaching limits
   - Implement in-app notifications for limit warnings
   - Send subscription renewal reminders
   - Notify users of successful plan changes
