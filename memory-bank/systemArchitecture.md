# System Architecture

## Architecture Overview
- Next.js full-stack application with App Router
- API-first design with RESTful endpoints
- Vector database for semantic search
- Event-driven analytics
- Middleware-protected authenticated routes
- Modular directory structure with clear separation of concerns
- User-based website ownership with SAAS plan limitations
- Subscription-based access control
- Browserless integration for headless browser capabilities in Docker environments

## Design Patterns
### Frontend
- Component-based architecture
- Atomic design system
- State management with React hooks
- Server-side rendering for SEO
- UI component library with Shadcn UI

### Backend
- Repository pattern for data access
  - Centralized query methods
  - Entity-specific repositories
  - Type-safe queries with Drizzle
  - Optimized JOIN operations
  - Proper indexing strategy
- Service layer architecture
- Event-driven analytics
- Queue-based background processing
- Middleware-based authentication
- Environment variable validation with T3 Env
- Environment-aware service configuration
  - Local vs. Docker deployment detection
  - Browserless integration for headless browser capabilities
  - Fallback mechanisms for development environments

## Data Model
### Core Entities
- Users (authenticated via Clerk)
- Organizations (multi-tenant support)
- Websites (per-organization chat instances)
- Chat Configurations (customization per website)
- Conversations (chat history tracking)
- Messages (individual chat messages)

### Relationships
- Organization -> Websites (1:many)
- Website -> Chat Configurations (1:1)
- Website -> Conversations (1:many)
- Conversation -> Messages (1:many)
- User -> Organizations (many:many)

## Code Organization
- `/src/app` - Next.js pages and routes
  - `/app/api` - API endpoints
    - `/dashboard` - Dashboard endpoints
    - `/types` - API type definitions
    - `/utils` - API utilities
  - `/app/dashboard` - Dashboard pages
- `/src/components` - Reusable UI components
- `/src/lib` - Core application logic
  - `/lib/db` - Database access and schema
  - `/lib/auth` - Authentication utilities
  - `/lib/ai` - AI integration components
- `/src/types` - TypeScript type definitions
- `/src/styles` - Global styling

## Tech Stack
- Frontend: Next.js 15 with App Router
- Backend: Next.js API routes
- Database: PostgreSQL with pgvector
- Cache & Rate Limiting: Redis
- ORM: Drizzle ORM
- Authentication: Clerk
- AI: MastrAI
- Deployment: Vercel/Coolify
- Styling: TailwindCSS with Shadcn UI components
- End-to-End testing with Playwright
- Web Crawling: Puppeteer with Browserless integration

## Development Environment
- Node.js environment
- TypeScript for type safety
- pnpm for package management
- Biome for linting and formatting
- Husky for git hooks
- TurboPack for development
- Docker Compose for local services (PostgreSQL, Redis)

## Key Dependencies
- Next.js 15.3.1
- React 19.0.0
- Drizzle ORM 0.43.1
- MastrAI Core SDK 0.9.0
- Clerk Auth 6.18.0
- TailwindCSS 4
- Shadcn UI components
- Zod 3.24.3 for schema validation
- T3 Env for environment type safety
- PostgreSQL with pgvector extensions
- Upstash Redis for rate limiting
- Sonner for toast notifications
- Puppeteer for headless browser automation
- Browserless for remote browser capabilities
- Cheerio for HTML parsing
