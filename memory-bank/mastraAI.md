# MastrAI Integration

## Overview
This document details the implementation of MastrAI integration for the Bubl platform. The integration enables the chat widget to use MastrAI for intelligent responses based on website content and maintain conversation history.

## Technology Stack
- **MastrAI**: Core AI framework for building conversational agents
- **Vercel AI SDK**: Provides React hooks and utilities for AI interactions
- **AssistantUI**: Pre-built UI components for chat interfaces

## Components Implemented

### 1. Website Context Retrieval Tool
- Created a tool to fetch website information from the database
- Implemented in `src/lib/ai/tools/website-context-tool.ts`
- Currently uses a placeholder implementation that will be enhanced with vector search
- Exports a `websiteContextTool` that can be used by the MastrAI agent

```typescript
export const websiteContextTool = createTool({
  id: "get-website-context",
  description: "Get context information about a website",
  inputSchema: z.object({
    websiteId: z.string().describe("The ID of the website to get context for"),
  }),
  outputSchema: z.object({
    websiteInfo: z.object({
      id: z.string(),
      name: z.string(),
      url: z.string(),
    }),
    content: z.array(z.object({
      title: z.string(),
      text: z.string(),
      url: z.string().optional(),
    })).optional(),
  }),
  execute: async ({ context }) => {
    // Implementation to fetch website information
  },
});
```

### 2. Conversation Memory System
- Implemented a conversation memory service in `src/lib/ai/memory/conversation-memory.ts`
- Provides methods to:
  - Create and retrieve conversations
  - Add messages to conversations
  - Get conversation history
  - Get or create a conversation for a website visitor
- Uses the existing database schema for conversations and messages

### 3. Enhanced MastrAI Agent
- Updated the agent configuration in `src/mastra/agents/index.ts`
- Added improved instructions for the agent
- Integrated the website context tool

```typescript
export const chatAgent = new Agent({
  name: "WebChat Assistant",
  instructions: `
    You are a helpful assistant for website visitors. Your goal is to provide friendly, 
    concise, and accurate responses to user questions about the website content.
    
    When responding to users:
    - Be conversational but professional
    - Keep responses brief and to the point
    - If you don't know something, admit it rather than making up information
    - Avoid controversial topics and maintain a neutral tone
    - Use markdown formatting when it improves readability
    - Reference information from the website when available
    - Provide helpful links to relevant pages when appropriate
    
    You have access to website context through the websiteContextTool. Use this tool
    to get information about the website when answering questions.
  `,
  model: mistral("mistral-large-latest"),
  tools: { websiteContextTool },
});
```

### 4. Chat API Routes
- Enhanced the main chat API route in `src/app/api/chat/route.ts`
- Created a website-specific chat API route in `src/app/api/chat/[websiteId]/route.ts`
- Added an API route to retrieve conversation history in `src/app/api/chat/history/[conversationId]/route.ts`
- Implemented streaming responses using Vercel AI SDK
- Added error handling and response processing

### 5. Updated Chat UI Component
- Modified the ChatUI component in `src/components/chat-ui/ChatUI.tsx`
- Added support for website and visitor IDs
- Updated the useChat hook to use the website-specific API route

## Implementation Status

- ✅ Installation and setup: Completed
- ✅ Component integration: Completed
- ✅ Theme configuration: Completed
- ✅ Memory integration: Completed
- ❌ Vector search: Planned
- ❌ Accessibility testing: Planned

## Current Limitations
- The website context tool currently uses a placeholder implementation
- Vector search for website content is not yet implemented
- The chat API routes need to be tested with real website data

## Next Steps

1. **Vector Search Implementation**
   - Create database tables for website pages and embeddings
   - Implement a crawler to extract content from websites
   - Generate embeddings for website content
   - Enhance the website context tool to use vector search

2. **Chat UI Improvements**
   - Add support for file attachments
   - Implement typing indicators
   - Add message reactions
   - Support rich content (images, links, etc.)

3. **Testing**
   - Write unit tests for the MastrAI integration
   - Test the chat functionality with real websites
   - Verify conversation history storage and retrieval
   - Ensure accessibility compliance
