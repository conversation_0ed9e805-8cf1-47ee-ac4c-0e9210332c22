# Database Schema Improvements

## Overview
This document outlines the necessary improvements to align our database schema with the PRD requirements. It identifies gaps in the current implementation and provides recommendations for schema enhancements.

## Current Schema Status

### Implemented Tables
1. **Users**
   - Basic implementation with core fields
   - Added subscription-related fields
   - Added plan association

2. **Websites**
   - Basic implementation with core fields
   - Added user association
   - Added crawl-related fields
   - Added status field

3. **Plans**
   - Implemented with subscription tiers
   - Includes website limits and feature flags

4. **Conversations**
   - Basic implementation with core fields
   - Missing several fields specified in the PRD

5. **Messages**
   - Basic implementation with core fields
   - Missing sources field

6. **WebsitePages** - Implemented for vector search
7. **Embeddings** - Implemented for vector search
8. **ChatConfigurations** - Implemented for chat widget customization

### Missing Tables
None - All required tables have been implemented

## Detailed Schema Comparison

### 1. Organizations Table

#### Current Implementation
```typescript
export const organizationsTable = pgTable("organizations", {
  id,
  name: varchar("name", { length: 255 }).notNull(),
  createdAt,
  updatedAt,
});
```

#### PRD Requirements
```
- id (UUID, PK)
- name (String)
- clerkOrganizationId (String)
- createdAt (DateTime)
- updatedAt (DateTime)
- subscription_tier (Enum: FREE, PRO, ENTERPRISE)
- subscription_status (Enum: ACTIVE, INACTIVE)
```

#### Missing Fields
- clerkOrganizationId
- subscription_tier
- subscription_status

### 2. Websites Table

#### Current Implementation
```typescript
export const websitesTable = pgTable("websites", {
  id,
  organizationId: uuid("organization_id")
    .references(() => organizationsTable.id)
    .notNull(),
  url: varchar("url", { length: 255 }).notNull(),
  name: varchar("name", { length: 255 }).notNull(),
  createdAt,
  updatedAt,
});
```

#### PRD Requirements
```
- id (UUID, PK)
- organizationId (UUID, FK)
- url (String)
- name (String)
- createdAt (DateTime)
- updatedAt (DateTime)
- lastCrawledAt (DateTime, nullable)
- crawlFrequency (Enum: DAILY, WEEKLY, MONTHLY)
- status (Enum: ACTIVE, PENDING, ERROR)
```

#### Missing Fields
- lastCrawledAt
- crawlFrequency
- status

### 3. Conversations Table

#### Current Implementation
```typescript
export const conversationsTable = pgTable("conversations", {
  id,
  websiteId: uuid("website_id")
    .references(() => websitesTable.id)
    .notNull(),
  visitorId: varchar("visitor_id", { length: 255 }).notNull(),
  status: varchar("status", { length: 50 }).notNull().default("active"),
  createdAt,
  updatedAt,
});
```

#### PRD Requirements
```
- id (UUID, PK)
- websiteId (UUID, FK)
- visitorId (String)
- startedAt (DateTime)
- endedAt (DateTime, nullable)
- referringUrl (String, nullable)
- deviceInfo (JSON, nullable)
- rating (Integer, nullable)
- feedback (Text, nullable)
```

#### Missing Fields
- endedAt
- referringUrl
- deviceInfo
- rating
- feedback

#### Notes
- We're using createdAt instead of startedAt
- We have an additional status field not in the PRD

### 4. Messages Table

#### Current Implementation
```typescript
export const messagesTable = pgTable("messages", {
  id,
  conversationId: uuid("conversation_id")
    .references(() => conversationsTable.id)
    .notNull(),
  content: text("content").notNull(),
  role: varchar("role", { length: 50 }).notNull(),
  createdAt,
});
```

#### PRD Requirements
```
- id (UUID, PK)
- conversationId (UUID, FK)
- content (Text)
- role (Enum: USER, ASSISTANT)
- createdAt (DateTime)
- sources (JSON, nullable)
```

#### Missing Fields
- sources

## Recommended Schema Enhancements

### 1. Update Organizations Table
```typescript
export const organizationsTable = pgTable("organizations", {
  id,
  name: varchar("name", { length: 255 }).notNull(),
  clerkOrganizationId: varchar("clerk_organization_id", { length: 255 }),
  subscriptionTier: varchar("subscription_tier", { length: 50 }).default("FREE"),
  subscriptionStatus: varchar("subscription_status", { length: 50 }).default("ACTIVE"),
  createdAt,
  updatedAt,
});
```

### 2. Update Websites Table
```typescript
export const websitesTable = pgTable("websites", {
  id,
  organizationId: uuid("organization_id")
    .references(() => organizationsTable.id)
    .notNull(),
  url: varchar("url", { length: 255 }).notNull(),
  name: varchar("name", { length: 255 }).notNull(),
  lastCrawledAt: timestamp("last_crawled_at", { withTimezone: true }),
  crawlFrequency: varchar("crawl_frequency", { length: 50 }).default("WEEKLY"),
  status: varchar("status", { length: 50 }).default("ACTIVE"),
  createdAt,
  updatedAt,
});
```

### 3. Update Conversations Table
```typescript
export const conversationsTable = pgTable("conversations", {
  id,
  websiteId: uuid("website_id")
    .references(() => websitesTable.id)
    .notNull(),
  visitorId: varchar("visitor_id", { length: 255 }).notNull(),
  status: varchar("status", { length: 50 }).notNull().default("active"),
  endedAt: timestamp("ended_at", { withTimezone: true }),
  referringUrl: varchar("referring_url", { length: 2048 }),
  deviceInfo: jsonb("device_info"),
  rating: integer("rating"),
  feedback: text("feedback"),
  createdAt,
  updatedAt,
});
```

### 4. Update Messages Table
```typescript
export const messagesTable = pgTable("messages", {
  id,
  conversationId: uuid("conversation_id")
    .references(() => conversationsTable.id)
    .notNull(),
  content: text("content").notNull(),
  role: varchar("role", { length: 50 }).notNull(),
  sources: jsonb("sources"),
  createdAt,
});
```

### 5. Implement ChatConfigurations Table
```typescript
export const chatConfigurationsTable = pgTable("chat_configurations", {
  id,
  websiteId: uuid("website_id")
    .references(() => websitesTable.id)
    .notNull(),
  name: varchar("name", { length: 255 }).default("Default Configuration"),
  primaryColor: varchar("primary_color", { length: 50 }).default("#4F46E5"),
  secondaryColor: varchar("secondary_color", { length: 50 }).default("#FFFFFF"),
  headerText: varchar("header_text", { length: 255 }).default("Chat Assistant"),
  welcomeMessage: text("welcome_message").default("Hi there! How can I help you today?"),
  position: varchar("position", { length: 50 }).default("BOTTOM_RIGHT"),
  isActive: boolean("is_active").default(true),
  createdAt,
  updatedAt,
});
```

### 6. Implement WebsitePages Table (as planned in vectorSearch.md)
```typescript
export const websitePagesTable = pgTable("website_pages", {
  id,
  websiteId: uuid("website_id")
    .references(() => websitesTable.id)
    .notNull(),
  url: varchar("url", { length: 2048 }).notNull(),
  title: varchar("title", { length: 512 }).notNull(),
  content: text("content").notNull(),
  lastCrawledAt: timestamp("last_crawled_at", { withTimezone: true }).notNull(),
  createdAt,
  updatedAt,
});
```

### 7. Implement Embeddings Table (as planned in vectorSearch.md)
```typescript
export const embeddingsTable = pgTable("embeddings", {
  id,
  pageId: uuid("page_id")
    .references(() => websitePagesTable.id)
    .notNull(),
  chunk: text("chunk").notNull(),
  embedding: vector("embedding", { dimensions: 1024 }).notNull(),
  createdAt,
  updatedAt,
});
```

## Implementation Status

### Completed
1. ✅ Replaced Organizations with Users table
2. ✅ Added Plans table for subscription tiers
3. ✅ Updated Websites table with user association and crawl fields
4. ✅ Implemented WebsitePages table for vector search
5. ✅ Implemented Embeddings table for vector search
6. ✅ Implemented ChatConfigurations table
7. ✅ Created relations between tables
8. ✅ Added API routes for user plans
9. ✅ Added UI components for plan display

### Remaining Work
1. Update Conversations table with missing fields
2. Update Messages table with sources field
3. Implement payment processing for plans
4. Add usage analytics for plan limits

## Migration Strategy

To safely update the schema without disrupting existing data:

1. **Create a new migration file**:
   ```bash
   pnpm drizzle-kit generate:pg
   ```

2. **Review the generated migration**:
   Ensure it correctly adds the new fields and tables without disrupting existing data.

3. **Apply the migration**:
   ```bash
   pnpm db:migrate
   ```

4. **Update TypeScript types and repositories**:
   Update the repository implementations to work with the new schema.

5. **Test thoroughly**:
   Ensure existing functionality continues to work with the updated schema.

## Conclusion

Aligning our database schema with the PRD requirements will ensure we have the necessary data structures to support all planned features. The proposed enhancements maintain compatibility with existing code while adding the missing fields and tables needed for full functionality.
