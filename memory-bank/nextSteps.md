# Next Steps for Bubl

## Current Project Status Overview

The Bubl project has made significant progress in several key areas:

1. **Core Infrastructure**
   - Database schema with user-website associations
   - Plan limits system with Free, Pro, and Enterprise tiers
   - Vector search implementation with pgvector
   - MastrAI integration with website context tool

2. **UI Components**
   - Dashboard with clean, minimalistic design
   - Sidebar showing websites for easier navigation
   - Chat UI with customizable appearance
   - Website management interface

3. **Website Crawler**
   - Core crawler implementation with Puppeteer
   - Content extraction with Cheerio
   - Browserless integration for Docker environments
   - Configurable crawl settings (depth, pages, patterns)
   - Robots.txt compliance and rate limiting

4. **Vector Search**
   - Embedding generation for website content
   - Similarity search using cosine similarity
   - Integration with chat API for relevant responses
   - Testing scripts for vector search functionality

## Prioritized Next Steps

Based on analysis of the PRD, progress tracking, and current implementation status, the following next steps have been prioritized:

### 1. Enhance Vector Search Implementation (High Priority)

The vector search functionality is central to providing accurate and relevant responses to user queries. Enhancing this component will directly improve the quality of chat responses.

**Tasks:**
- Implement hybrid search by combining vector search with keyword search
- Add query preprocessing for better matches
- Improve relevance ranking with additional factors
- Optimize chunking strategies for better context preservation
- Add caching for frequently accessed content
- Implement performance monitoring for search operations

**Implementation Plan:**
1. Update `WebsiteRagService` to support hybrid search
2. Enhance `website-context-tool.ts` to use the improved search capabilities
3. Add preprocessing functions for user queries
4. Implement a more sophisticated relevance ranking algorithm
5. Add metrics collection for search performance

### 2. Enhance Website Crawler (Partially Completed)

Improving the website crawler will enhance content discovery, extraction quality, and overall system performance, leading to better chat responses.

**Completed Tasks:**
- Implemented incremental crawling to process only changed content
- Added ETag and Last-Modified header support for change detection
- Enhanced hasPageChanged method with conditional GET requests
- Added persistent storage of crawl configurations for each website
- Created API endpoint to retrieve saved crawl configurations
- Updated CrawlConfigurationForm to load saved configurations

**Remaining Tasks:**
- Add sitemap.xml support for better content discovery
- Implement llms.txt and llms-full.txt support for ethical content usage
- Extract Open Graph metadata for richer chat responses
- Implement parallel crawling for improved performance
- Enhance content extraction with sophisticated algorithms
- Add error resilience with retry logic
- Add support for different content types beyond HTML

**Implementation Plan:**
1. Update `WebsiteCrawler` class to implement sitemap.xml parsing
2. Add methods for llms.txt detection and compliance
3. Implement Open Graph metadata extraction
4. Create parallel processing capabilities with configurable batch sizes
5. Enhance content extraction with content density scoring
6. Implement retry logic with exponential backoff
7. See `memory-bank/crawlerImprovements.md` for detailed implementation plan

### 3. Enhance Vector Search Implementation (High Priority)

The vector search functionality is central to providing accurate and relevant responses to user queries. Enhancing this component will directly improve the quality of chat responses.

**Tasks:**
- Implement hybrid search by combining vector search with keyword search
- Add query preprocessing for better matches
- Improve relevance ranking with additional factors
- Optimize chunking strategies for better context preservation
- Add caching for frequently accessed content
- Implement performance monitoring for search operations

**Implementation Plan:**
1. Update `WebsiteRagService` to support hybrid search
2. Enhance `website-context-tool.ts` to use the improved search capabilities
3. Add preprocessing functions for user queries
4. Implement a more sophisticated relevance ranking algorithm
5. Add metrics collection for search performance

### 4. Implement Scheduled Crawling System

A scheduled crawling system is essential for keeping website content up-to-date without manual intervention.

**Tasks:**
- Design a background job system for scheduled crawls
- Create a database schema for storing crawl schedules
- Implement a scheduler service to trigger crawls at specified intervals
- Update the CrawlConfigurationForm to include scheduling options
- Create a UI for viewing and managing scheduled crawls
- Add API endpoints for managing crawl schedules
- Implement status tracking for scheduled crawls

**Implementation Plan:**
1. Add new tables to the database schema for crawl schedules
2. Create a scheduler service that runs at regular intervals
3. Update the crawler to support being triggered by the scheduler
4. Enhance the UI to support scheduling configuration
5. Add API endpoints for managing schedules

### 5. Create Crawl Status Monitoring Page

A dedicated page for monitoring crawl status will provide visibility into the crawling process and help users troubleshoot issues.

**Tasks:**
- Design a dedicated page for viewing crawl status
- Implement real-time updates using WebSockets or polling
- Create detailed logging for crawl operations
- Display crawl progress, errors, and statistics
- Add the ability to cancel ongoing crawls
- Implement filtering and sorting of crawl history

**Implementation Plan:**
1. Create a new page at `/dashboard/websites/[id]/crawl-status`
2. Implement a status tracking system for crawl operations
3. Create API endpoints for retrieving crawl status
4. Add UI components for displaying crawl progress and history
5. Implement real-time updates using polling or WebSockets

### 6. Implement Manual Content Upload Functionality

Manual content upload will allow users to add content that can't be crawled automatically, such as PDFs and other documents.

**Tasks:**
- Create a UI for uploading files (PDFs, docs, etc.)
- Implement file parsing and content extraction
- Integrate with the existing embedding generation pipeline
- Add support for different file types
- Implement validation and error handling
- Update the database schema to track content source

**Implementation Plan:**
1. Create a new page at `/dashboard/websites/[id]/upload`
2. Add file upload components with drag-and-drop support
3. Implement file parsing for different document types
4. Update the database schema to track uploaded files
5. Integrate with the embedding generation pipeline

### 7. Complete Security Enhancements

While many security features have been implemented, some remain to be completed or fixed.

**Tasks:**
- Fix API rate limiting implementation (currently disabled due to runtime errors)
- Complete CSRF protection implementation for state-changing operations
- Set up dependency scanning for vulnerable packages
- Implement data encryption for sensitive information
- Develop a regular security audit process

**Implementation Plan:**
1. Investigate and fix the issues with the Redis-based rate limiting
2. Complete the CSRF protection implementation
3. Set up automated scanning for vulnerable dependencies
4. Implement encryption for sensitive data

### 8. Test Chat Components and MastrAI Integration

Comprehensive testing of the chat functionality is critical to ensure the core product works correctly.

**Tasks:**
- Write unit tests for the website context tool
- Test the conversation memory system
- Verify chat API routes with real website data
- Test the chat UI with the MastrAI integration
- Verify hydration fixes work across different browsers
- Add tests to catch potential hydration issues

**Implementation Plan:**
1. Create test files for each component
2. Set up test fixtures with sample website data
3. Implement integration tests for the chat flow
4. Test across different browsers and devices
5. Add automated tests to the CI pipeline

## Technical Considerations

### Performance Optimization
- Implement caching for frequently accessed content
- Optimize database queries for vector search
- Use incremental updates for embeddings
- Implement batch processing for large websites

### Scalability
- Design the scheduled crawling system to handle many websites
- Ensure the vector search can scale with increasing content
- Plan for database growth with efficient indexing

### Security
- Implement rate limiting for API endpoints
- Add request validation for all inputs
- Ensure proper error handling and logging
- Add security headers to all responses

## Timeline Estimate

1. **Enhance Vector Search Implementation**: 1-2 weeks
2. **Implement Scheduled Crawling System**: 2-3 weeks
3. **Create Comprehensive Crawl Status Monitoring Page**: 1-2 weeks
4. **Implement Manual Content Upload**: 2 weeks
5. **Complete Security Enhancements**: 1 week
6. **Comprehensive Testing**: 1-2 weeks
7. **Prepare for Production Launch**: 1-2 weeks

Total estimated time: 9-14 weeks

## Success Metrics

- **Search Relevance**: Improved relevance of search results measured by user feedback
- **Crawl Reliability**: Reduced failures in scheduled crawls
- **Crawl Efficiency**: Decreased time to crawl websites with improved parallel processing
- **Content Discovery**: Increased number of pages discovered through sitemap.xml parsing
- **Content Quality**: Higher quality content extraction with improved algorithms
- **Content Coverage**: Increased percentage of website content successfully indexed
- **User Satisfaction**: Improved ratings for chat responses
- **System Performance**: Reduced latency for search operations and chat responses
- **Metadata Richness**: Increased percentage of pages with extracted Open Graph metadata
