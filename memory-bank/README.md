# Memory Bank

This folder contains documentation and context for the Bubl project. It serves as a knowledge repository for the development team.

## Purpose

The memory bank is designed to:

1. Provide context for the development team
2. Document architectural decisions
3. Track implementation status
4. Store technical specifications
5. Maintain a record of progress

## File Organization

### Core Documentation

- **README.md** - This file, explaining the memory bank's purpose and organization
- **projectbrief.md** - Initial project brief and goals
- **projectStatus.md** - Current focus, recent changes, and next steps
- **featureRoadmap.md** - Feature roadmap aligned with PRD phases

### Architecture & Technical Design

- **systemArchitecture.md** - Overview of the system architecture, design patterns, and tech stack
- **systemPatterns.md** - Common patterns and approaches used in the codebase
- **techContext.md** - Technical context and environment setup
- **schemaImprovements.md** - Database schema improvements to align with PRD
- **security.md** - Security implementation plan and best practices

### Feature Implementation

- **memoryBank.md** - Conversation memory implementation
- **mastraAI.md** - MastrAI integration details
- **vectorSearch.md** - Vector search implementation
- **vector-search.md** - Updated vector search implementation details
- **websiteCrawler.md** - Website crawler implementation
- **crawlerImprovements.md** - Planned improvements for the website crawler
- **ui.md** - UI implementation and design system
- **chat-implementation.md** - Chat implementation details
- **userWebsitesPlans.md** - User-website associations and SAAS plans
- **planLimitsSystem.md** - Plan limits system implementation
- **chatEmbedFixes.md** - Chat widget embed code fixes
- **websiteDeletionFixes.md** - Website deletion fixes and confirmation dialog
- **sidebarImprovements.md** - Sidebar navigation improvements
- **sidebarCollapsible.md** - Collapsible sidebar implementation for responsive design
- **widgetAnalyticsPerformance.md** - Widget analytics and performance improvements
- **domainAllowlisting.md** - Domain allowlisting implementation for chat widget security

### Maintenance & Progress

- **technicalMaintenance.md** - Technical maintenance work and bug fixes
- **progress.md** - Progress tracking and metrics
- **nextSteps.md** - Prioritized next steps and implementation plans

## Maintenance Guidelines

1. **Keep it updated**: Update documentation as the project evolves
2. **Consolidate related information**: Avoid redundancy by keeping related information in a single file
3. **Use consistent formatting**: Maintain consistent formatting across all files
4. **Track implementation status**: Use checkmarks (✅) for completed items and other indicators for in-progress (🔄) and planned (📋) items
5. **Link to code**: Reference specific files and code snippets when relevant
6. **Update after major changes**: Review and update documentation after implementing major features
7. **Remove outdated information**: Regularly clean up outdated or irrelevant information
8. **Be concise**: Focus on key information and decisions
9. **Use markdown**: Format documents consistently using markdown
10. **Link related content**: Reference related documents when appropriate

## Usage

Reference these documents when:

1. **Onboarding new team members**: Provide context about the project architecture and decisions
2. **Planning new features**: Understand existing implementations and patterns
3. **Debugging issues**: Find information about specific components and their implementation
4. **Making architectural decisions**: Review existing patterns and approaches
5. **Tracking progress**: Monitor project status and upcoming tasks
6. **Implementing features**: Reference technical details and implementation guidelines
