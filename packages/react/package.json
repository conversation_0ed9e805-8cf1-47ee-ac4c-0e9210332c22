{"name": "@bubl/react", "version": "0.1.5", "description": "React component for embedding Bubl chat widget in React applications", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint \"src/**/*.ts*\"", "typecheck": "tsc --noEmit"}, "keywords": ["bubl", "chat", "widget", "react", "ai", "chatbot"], "author": "Bubl Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/bublchat/bubl"}, "homepage": "https://bublai.com", "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "eslint": "^8.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^5.0.0", "tsup": "^8.4.0", "typescript": "^5.0.0"}}