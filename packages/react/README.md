# Bubl React

A React component for embedding the Bubl chat widget in React applications.

## Installation

```bash
# npm
npm install @bubl/react

# yarn
yarn add @bubl/react

# pnpm
pnpm add @bubl/react
```

## Usage

```tsx
import { BublChat } from '@bubl/react';

function App() {
  return (
    <div>
      {/* Your website content */}

      <BublChat
        websiteId="your-website-id"
        primaryColor="#4F46E5"
        secondaryColor="#FFFFFF"
        position="bottom-right"
        welcomeMessage="Hi there! How can I help you today?"
        headerText="Chat Assistant"
        initiallyOpen={false}
        onReady={() => console.log("Bubl widget is ready")}
      />
    </div>
  );
}
```

## Props

| Prop | Type | Default | Description |
| --- | --- | --- | --- |
| `websiteId` | `string` | **Required** | Your website ID from the Bubl dashboard |
| `primaryColor` | `string` | `"#4F46E5"` | Primary color for the chat widget (hex code) |
| `secondaryColor` | `string` | `"#FFFFFF"` | Secondary color for the chat widget (hex code) |
| `position` | `"bottom-right"` \| `"bottom-left"` | `"bottom-right"` | Position of the chat widget |
| `welcomeMessage` | `string` | `"Hi there! How can I help you today?"` | Welcome message displayed when the chat is opened |
| `headerText` | `string` | `"Chat Assistant"` | Text displayed in the header of the chat widget |
| `initiallyOpen` | `boolean` | `false` | Whether the chat widget should be initially open |
| `onReady` | `() => void` | `undefined` | Callback function called when the widget is ready |

## Controlling the Widget

You can control the widget programmatically using the global `Bubl.api` object:

```tsx
// Open the chat widget
window.Bubl?.api?.open();

// Close the chat widget
window.Bubl?.api?.close();

// Toggle the chat widget
window.Bubl?.api?.toggle();

// Maximize the chat widget to fullscreen
window.Bubl?.api?.maximize();

// Restore the chat widget to normal size
window.Bubl?.api?.restore();

// Toggle between maximized and normal size
window.Bubl?.api?.toggleMaximize();
```

## Custom App URL

If you're self-hosting Bubl, you can specify a custom app URL by setting the `window.bublConfig.appUrl` property before rendering the component:

```tsx
// Set custom app URL
window.bublConfig = {
  appUrl: "https://your-bubl-instance.com"
};

// Then render the component
ReactDOM.render(
  <BublChat websiteId="your-website-id" />,
  document.getElementById('root')
);
```

## Styling

The Bubl chat widget is designed to be non-intrusive and will not affect the styles of your website. All styles are carefully scoped to the widget elements only.

- The widget uses scoped CSS variables that only apply to the widget elements
- No global styles are added that could affect your website's appearance
- The widget's iframe has a transparent background by default
- The widget uses `color-scheme: none` to prevent inheriting color scheme preferences from the parent website

## License

MIT
