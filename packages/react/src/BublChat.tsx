import { useEffect, useRef } from "react"
import "./styles.css"
import "./types" // Import types for global window extensions

export interface BublChatProps {
  /**
   * Your website ID from the Bubl dashboard
   */
  websiteId: string

  /**
   * Primary color for the chat widget (hex code)
   */
  primaryColor?: string

  /**
   * Secondary color for the chat widget (hex code)
   */
  secondaryColor?: string

  /**
   * Position of the chat widget
   */
  position?: "bottom-right" | "bottom-left"

  /**
   * Welcome message displayed when the chat is opened
   */
  welcomeMessage?: string

  /**
   * Text displayed in the header of the chat widget
   */
  headerText?: string

  /**
   * Whether the chat widget should be initially open
   */
  initiallyOpen?: boolean

  /**
   * Callback function called when the widget is ready
   */
  onReady?: () => void
}

/**
 * BublChat is a React component for embedding the Bubl chat widget in React applications.
 *
 * @example
 * ```tsx
 * import { BublChat } from "@bubl/react";
 *
 * function App() {
 *   return (
 *     <div>
 *       <h1>My Website</h1>
 *       <BublChat
 *         websiteId="your-website-id"
 *         primaryColor="#4F46E5"
 *         position="bottom-right"
 *       />
 *     </div>
 *   );
 * }
 * ```
 */
export function BublChat({
  websiteId,
  primaryColor = "#4F46E5",
  secondaryColor = "#FFFFFF",
  position = "bottom-right",
  welcomeMessage = "Hi there! How can I help you today?",
  headerText = "Chat Assistant",
  initiallyOpen = false,
  onReady,
}: BublChatProps) {
  const scriptLoaded = useRef(false)
  const appUrl = typeof window !== 'undefined'
    ? (window.bublConfig?.appUrl || "https://bublai.com")
    : "https://bublai.com"

  useEffect(() => {
    // Skip if the script is already loaded or if we're not in the browser
    if (scriptLoaded.current || typeof window === "undefined") {
      return
    }

    // Mark as loaded to prevent duplicate loading
    scriptLoaded.current = true

    // Inject CSS variables for transparency - SCOPED to widget elements only
    const styleTag = document.createElement("style")
    styleTag.id = "bubl-widget-styles"
    styleTag.innerHTML = `
      /* Scoped CSS variables - only applied to widget elements */
      #bubl-widget-container {
        background-color: transparent !important;
        --chat-primary-color: ${primaryColor};
        --chat-secondary-color: ${secondaryColor};
        --background: transparent;
        color-scheme: none !important;
        pointer-events: none !important; /* Allow clicks to pass through to underlying elements */
      }

      #bubl-widget {
        background-color: transparent !important;
        background: transparent !important;
        color-scheme: none !important;
        pointer-events: auto !important; /* Ensure iframe receives pointer events */
      }

      /* Ensure no styles leak outside the widget */
      #bubl-widget-container * {
        font-family: inherit;
        box-sizing: border-box;
        color-scheme: none !important;
      }
    `
    document.head.appendChild(styleTag)

    // Set up the global Bubl object with configuration
    window.Bubl = window.Bubl || {
      config: {
        websiteId,
        primaryColor,
        secondaryColor,
        position,
        welcomeMessage,
        headerText,
        initiallyOpen,
      },
      onReady: onReady || function () {
        console.log("Bubl widget is ready")
      },
    }

    // Create and load the script
    const script = document.createElement("script")
    script.async = true
    script.src = `${appUrl}/widget/v1/loader.js`

    // Append the script to the document
    const firstScript = document.getElementsByTagName("script")[0]
    firstScript.parentNode?.insertBefore(script, firstScript)

    // Clean up function
    return () => {
      // Remove the widget container if it exists
      const container = document.getElementById("bubl-widget-container")
      if (container) {
        container.remove()
      }

      // Remove the script if it exists
      if (script.parentNode) {
        script.parentNode.removeChild(script)
      }

      // Remove the style tag if it exists
      const styleTag = document.getElementById("bubl-widget-styles")
      if (styleTag) {
        styleTag.remove()
      }

      // Reset the loaded flag
      scriptLoaded.current = false

      // Clean up global object
      delete window.Bubl
    }
  }, [
    websiteId,
    primaryColor,
    secondaryColor,
    position,
    welcomeMessage,
    headerText,
    initiallyOpen,
    onReady,
    appUrl,
  ])

  // This component doesn't render anything visible
  return null
}

// Global type declarations are now in types.ts
