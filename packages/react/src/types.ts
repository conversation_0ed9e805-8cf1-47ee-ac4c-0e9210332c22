/**
 * Global type declarations for the Bubl chat widget
 */

interface BublConfig {
  websiteId: string;
  primaryColor?: string;
  secondaryColor?: string;
  position?: string;
  welcomeMessage?: string;
  headerText?: string;
  initiallyOpen?: boolean;
}

interface BublApi {
  open: () => void;
  close: () => void;
  toggle: () => void;
  maximize: () => void;
  restore: () => void;
  toggleMaximize: () => void;
}

interface BublObject {
  config: BublConfig;
  onReady?: () => void;
  api?: BublApi;
  _internal?: Record<string, unknown>;
}

interface BublGlobalConfig {
  appUrl?: string;
}

// Extend the Window interface
declare global {
  interface Window {
    Bubl?: BublObject;
    bublConfig?: BublGlobalConfig;
  }
}

export {};
