import { defineConfig } from "tsup";

export default defineConfig({
  entry: ["src/index.ts"],
  format: ["cjs", "esm"],
  dts: true,
  splitting: false,
  sourcemap: true,
  clean: true,
  external: ["react", "react-dom"],
  // Include CSS files in the build
  injectStyle: true,
  // Copy CSS files to the dist directory
  esbuildOptions(options) {
    options.assetNames = "assets/[name]-[hash]";
    options.loader = {
      ...options.loader,
      ".css": "copy",
    };
  },
});
