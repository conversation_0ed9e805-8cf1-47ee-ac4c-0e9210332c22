{"name": "@bubl/packages", "version": "0.0.0", "private": true, "description": "Bubl packages monorepo", "scripts": {"build": "pnpm --filter=\"./react\" --filter=\"./chat\" --filter=\"./pnpm packages:publish\" build", "dev": "pnpm --filter=\"./react\" --filter=\"./chat\" --filter=\"./pnpm packages:publish\" dev", "clean": "pnpm --filter=\"./react\" --filter=\"./chat\" --filter=\"./pnpm packages:publish\" clean", "lint": "pnpm --filter=\"./react\" --filter=\"./chat\" --filter=\"./pnpm packages:publish\" lint", "typecheck": "pnpm --filter=\"./react\" --filter=\"./chat\" --filter=\"./pnpm packages:publish\" typecheck", "publish-packages": "pnpm build && pnpm --filter=\"./react\" --filter=\"./chat\" --filter=\"./pnpm packages:publish\" publish --access public"}, "keywords": ["bubl", "monorepo"], "author": "Bubl Team", "license": "MIT"}