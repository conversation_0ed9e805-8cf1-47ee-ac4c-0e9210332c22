# Bubl Packages

This directory contains all the packages that are part of the Bubl ecosystem.

## Available Packages

| Package | Description | Version |
|---------|-------------|---------|
| [@bubl/react](./react) | React component for embedding Bubl chat widget | 0.1.0 |
| [@bubl/chat](./chat) | Bubl Chat widget | 0.1.0 |
| [@bubl/pnpm packages:publish](./pnpm packages:publish) | Chat widget for Bubl AI | 0.1.0 |

## Development

### Prerequisites

- Node.js 18+
- pnpm 8+

### Setup

```bash
# Install dependencies for all packages
pnpm install
```

### Build

```bash
# Build all packages
pnpm build

# Build a specific package
pnpm --filter @bubl/react build
```

### Development Mode

```bash
# Run development mode for all packages
pnpm dev

# Run development mode for a specific package
pnpm --filter @bubl/react dev
```

### Publishing

```bash
# Publish all packages
pnpm publish-packages

# Publish a specific package
pnpm --filter @bubl/react publish --access public
```

## Adding a New Package

1. Create a new directory in the `packages` directory
2. Initialize the package with `pnpm init`
3. Add the package to the workspace by updating the root `package.json`
4. Add build scripts to the package
5. Update the scripts in the packages `package.json` to include the new package

## License

MIT
