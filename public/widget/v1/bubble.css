/**
 * Bubl Bubble Button Styles
 * Optimized for performance and non-intrusive UI
 */

#bubl-bubble-button {
  /* Base styles */
  position: fixed;
  z-index: 99999999;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  background:red;
  
  /* Flex layout for centering icon */
  display: flex;
  align-items: center;
  justify-content: center;
  
  /* Animation */
  transition: all 0.3s ease;
  transform: translateZ(0); /* Force hardware acceleration */
  will-change: transform;
  
  /* Hover effect */
  opacity: 0.9;
}

#bubl-bubble-button:hover {
  transform: scale(1.05) translateZ(0);
  opacity: 1;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

#bubl-bubble-button:active {
  transform: scale(0.95) translateZ(0);
}

/* Animation for bubble appearance */
@keyframes bubl-bubble-appear {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(10px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0) translateZ(0);
  }
}

#bubl-bubble-button {
  animation: bubl-bubble-appear 0.3s ease-out forwards;
}

/* Ensure the bubble doesn't interfere with page interactions */
#bubl-bubble-button * {
  pointer-events: none;
}
