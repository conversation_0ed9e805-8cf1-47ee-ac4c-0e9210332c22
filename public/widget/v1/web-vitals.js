// /**
//  * Web Vitals library for Bubl Widget
//  * Minified version of web-vitals for performance monitoring
//  */
// !function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).webVitals={})}(this,(function(e){"use strict";var t,n,i,r,a=function(e,t){return{name:e,value:void 0===t?-1:t,delta:0,entries:[],id:"v2-"+Date.now()+"-"+(Math.floor(8999999999999*Math.random())+1e12)}},o=function(e,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){if("first-input"===e&&!("PerformanceEventTiming"in self))return;var n=new PerformanceObserver((function(e){return e.getEntries().map(t)}));return n.observe({type:e,buffered:!0}),n}}catch(e){}},u=function(e,t){var n=function n(i){"pagehide"!==i.type&&"hidden"!==document.visibilityState||(e(i),t&&(removeEventListener("visibilitychange",n,!0),removeEventListener("pagehide",n,!0)))};addEventListener("visibilitychange",n,!0),addEventListener("pagehide",n,!0)},c=function(e){addEventListener("pageshow",(function(t){t.persisted&&e(t)}),!0)},f=function(e,t,n){var i;return function(r){t.value>=0&&(r||n)&&(t.delta=t.value-(i||0),(t.delta||void 0===i)&&(i=t.value,e(t)))}},s=function(e,t){var n,i=a("CLS",0),r=0,s=[],d=function(e){e.forEach((function(e){if(!e.hadRecentInput){var t=s[0],a=s[s.length-1];r&&e.startTime-a.startTime<1e3&&e.startTime-t.startTime<5e3?(r+=e.value,s.push(e)):(r=e.value,s=[e])}})),r>i.value&&(i.value=r,i.entries=s,n&&n())},l=o("layout-shift",d);l&&(n=f(e,i,t),u((function(){l.takeRecords().map(d),n(!0)})),c((function(){r=0,i=a("CLS",0),n=f(e,i,t),requestAnimationFrame((function(){return requestAnimationFrame((function(){return n()}))}))})))},d=function(e,t){var n,i=a("FID"),r=function(e){e.startTime<i.value&&(i.value=e.startTime,i.entries.push(e),n&&n())},s=o("first-input",r);n=f(e,i,t),s&&u((function(){s.takeRecords().map(r),s.disconnect()}),!0),s&&c((function(){var o;i=a("FID"),n=f(e,i,t),r=[],s=-1,o=null,m(addEventListener),r.push({entryType:"first-input",name:o.type,target:o.target,cancelable:o.cancelable,startTime:o.timeStamp,processingStart:o.timeStamp+s}),m((function(){r.map((function(e){return i.value=e.processingStart-e.startTime,i.entries.push(e),n(!0)}))}))}))}},l=function(e,t){var n,i=a("LCP"),r=function(e){var t=e.startTime;t<i.value&&(i.value=t,i.entries.push(e)),n&&n()},s=o("largest-contentful-paint",r);if(s){n=f(e,i,t);var d=function(){s.takeRecords().map(r),s.disconnect()};["keydown","click"].forEach((function(e){addEventListener(e,d,{once:!0,capture:!0})})),u(d,!0),c((function(){i=a("LCP"),n=f(e,i,t),requestAnimationFrame((function(){return requestAnimationFrame((function(){return n()}))}))}))}},p=function(e){var t,n=a("TTFB");t=function(){try{var t=performance.getEntriesByType("navigation")[0]||function(){var e=performance.timing,t={entryType:"navigation",startTime:0};for(var n in e)"navigationStart"!==n&&"toJSON"!==n&&(t[n]=Math.max(e[n]-e.navigationStart,0));return t}();if(n.value=n.delta=t.responseStart,n.value<0||n.value>performance.now())return;n.entries=[t],e(n)}catch(e){}},"complete"===document.readyState?setTimeout(t,0):addEventListener("load",(function(){return setTimeout(t,0)}))};e.getCLS=s,e.getFCP=function(e,t){var n,i=a("FCP"),r=function(e){"first-contentful-paint"===e.name&&(s&&s.disconnect(),e.startTime<i.value&&(i.value=e.startTime,i.entries.push(e),n&&n()))},s=o("paint",r);s&&(n=f(e,i,t),c((function(){r(performance.getEntriesByName("first-contentful-paint")[0]),n(!0)})))},e.getFID=d,e.getLCP=l,e.getTTFB=p,e.onCLS=s,e.onFCP=function(e,t){var n,i=a("FCP"),r=function(e){"first-contentful-paint"===e.name&&(s&&s.disconnect(),e.startTime<i.value&&(i.value=e.startTime,i.entries.push(e),n&&n()))},s=o("paint",r);s&&(n=f(e,i,t),c((function(){r(performance.getEntriesByName("first-contentful-paint")[0]),n(!0)})))},e.onFID=d,e.onLCP=l,e.onTTFB=p,Object.defineProperty(e,"__esModule",{value:!0})}));
