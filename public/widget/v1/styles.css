/**
 * <PERSON><PERSON>l Widget Styles - Optimized for performance
 * - Minimized selectors
 * - Hardware-accelerated animations
 * - Content-visibility optimizations
 */


#webchat-ai-widget-container {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  animation: webchat-ai-fade-in 0.3s ease-out;
  will-change: opacity, transform;
  content-visibility: auto;
  contain: layout style paint;
}

#webchat-ai-widget {
  background-color: transparent;
  will-change: transform;
  transform: translateZ(0); /* Force hardware acceleration */
  backface-visibility: hidden;
  perspective: 1000px;
  content-visibility: auto;
}

/* Animation for widget appearance - optimized for performance */
@keyframes webchat-ai-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

/* Reduce layout shifts */
#webchat-ai-widget-container * {
  box-sizing: border-box;
}

/* Optimize rendering performance */
#webchat-ai-widget-container img,
#webchat-ai-widget-container video {
  content-visibility: auto;
  contain: layout style paint;
}
