/**
 * Bubl Widget Loader v2.0
 * Complete rewrite with Shadow DOM isolation and zero host interference
 */
(function() {
  'use strict';
  
  // Prevent multiple widget instances
  if (window.__BUBL_WIDGET_LOADED__) {
    console.warn('Bubl widget already loaded');
    return;
  }
  window.__BUBL_WIDGET_LOADED__ = true;

  // Configuration validation
  const config = window.Bubl?.config;
  if (!config?.websiteId) {
    console.error('Bubl: Missing websiteId in configuration');
    return;
  }

  // Get base URL from script source
  const currentScript = document.currentScript || 
    Array.from(document.scripts).find(s => s.src.includes('/widget/v2/loader.js'));
  
  if (!currentScript) {
    console.error('Bubl: Could not determine widget base URL');
    return;
  }

  const baseUrl = currentScript.src.replace('/widget/v2/loader.js', '');
  
  // Widget state management
  let widgetInstance = null;
  let shadowRoot = null;
  let isInitialized = false;

  // Utility functions
  function generateId() {
    return 'bubl-' + Math.random().toString(36).substr(2, 9);
  }

  function getStoredVisitorId() {
    try {
      const key = `bubl_visitor_${config.websiteId}`;
      let visitorId = localStorage.getItem(key);
      if (!visitorId) {
        visitorId = generateId();
        localStorage.setItem(key, visitorId);
      }
      return visitorId;
    } catch (e) {
      return generateId();
    }
  }

  // Performance tracking (minimal and non-blocking)
  function trackEvent(eventType, data = {}) {
    if (!config.websiteId) return;
    
    // Use requestIdleCallback for non-critical tracking
    const track = () => {
      fetch(`${baseUrl}/api/analytics/track-event`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          websiteId: config.websiteId,
          visitorId: getStoredVisitorId(),
          conversationId: generateId(),
          eventType,
          metadata: { ...data, source: 'widget_v2' }
        })
      }).catch(() => {}); // Silent fail for analytics
    };

    if (window.requestIdleCallback) {
      requestIdleCallback(track);
    } else {
      setTimeout(track, 0);
    }
  }

  // Create isolated widget container with Shadow DOM
  function createWidgetContainer() {
    const container = document.createElement('div');
    container.id = 'bubl-widget-v2';
    
    // Position the container
    container.style.cssText = `
      position: fixed !important;
      z-index: 2147483647 !important;
      pointer-events: none !important;
      ${config.position === 'bottom-left' ? 'left: 20px !important;' : 'right: 20px !important;'}
      bottom: 20px !important;
      width: 0 !important;
      height: 0 !important;
      margin: 0 !important;
      padding: 0 !important;
      border: none !important;
      background: transparent !important;
      overflow: visible !important;
    `;

    // Create Shadow DOM for complete isolation
    if (container.attachShadow) {
      shadowRoot = container.attachShadow({ mode: 'closed' });
    } else {
      // Fallback for older browsers
      shadowRoot = container;
      console.warn('Bubl: Shadow DOM not supported, using fallback');
    }

    document.body.appendChild(container);
    return container;
  }

  // Load widget React bundle
  function loadWidgetBundle() {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.async = true;
      script.onload = resolve;
      script.onerror = reject;
      script.src = `${baseUrl}/widget/v2/bundle.js`;
      
      // Append to shadow root or document head
      if (shadowRoot.appendChild) {
        shadowRoot.appendChild(script);
      } else {
        document.head.appendChild(script);
      }
    });
  }

  // Initialize widget
  async function initializeWidget() {
    if (isInitialized) return;
    isInitialized = true;

    try {
      const startTime = performance.now();
      
      // Create container
      const container = createWidgetContainer();
      
      // Load widget bundle
      await loadWidgetBundle();
      
      // Initialize React widget
      if (window.BublWidgetV2) {
        widgetInstance = window.BublWidgetV2.init({
          shadowRoot,
          config: {
            ...config,
            baseUrl,
            visitorId: getStoredVisitorId()
          }
        });

        // Track successful initialization
        const loadTime = performance.now() - startTime;
        trackEvent('widget_v2_loaded', { loadTime });

        // Call ready callback
        if (typeof config.onReady === 'function') {
          config.onReady();
        }
      } else {
        throw new Error('Widget bundle failed to load');
      }
    } catch (error) {
      console.error('Bubl widget initialization failed:', error);
      trackEvent('widget_v2_error', { error: error.message });
    }
  }

  // Public API
  window.Bubl = window.Bubl || {};
  window.Bubl.api = {
    open() {
      if (widgetInstance?.open) widgetInstance.open();
    },
    close() {
      if (widgetInstance?.close) widgetInstance.close();
    },
    toggle() {
      if (widgetInstance?.toggle) widgetInstance.toggle();
    },
    destroy() {
      if (widgetInstance?.destroy) widgetInstance.destroy();
      const container = document.getElementById('bubl-widget-v2');
      if (container) container.remove();
      isInitialized = false;
      widgetInstance = null;
      shadowRoot = null;
    }
  };

  // Initialize widget when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeWidget);
  } else {
    // DOM is already ready
    setTimeout(initializeWidget, 0);
  }

})();
