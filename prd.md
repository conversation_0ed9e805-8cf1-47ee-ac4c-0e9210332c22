# Product Requirements Document: Bubl

## Product Overview

Bubl is a SaaS platform enabling businesses to embed intelligent chatbots on their websites that understand and can answer questions about the website's content. The chatbot provides conversational access to website information, improving user engagement and reducing support requests.

## Tech Stack

- **Frontend**: Next.js
- **Backend**: Next.js API routes
- **Database**: PostgreSQL with pgvector extension
- **ORM**: Drizzle
- **Authentication**: Clerk
- **AI Integration**: MastrAI
- **Deployment**: Vercel

## User Personas

### Website Owner (Primary Customer)
- Small to medium-sized business owners
- Marketing managers
- Content creators
- Support team managers

### Website Visitor (End User)
- Customers seeking information
- Prospects researching solutions
- Users needing help navigating the website

## Core Features

### 1. Website Content Ingestion
- **Priority**: P0
- **Description**: System crawls and indexes client websites for use in the chatbot knowledge base
- **Requirements**:
  - Support for standard HTML websites
  - Handling of JavaScript-rendered content
  - Configurable crawl depth and frequency
  - Support for inclusion/exclusion patterns
  - Ability to manually upload additional content (PDFs, docs)
- **Technical Notes**: Use a headless browser for crawling complex sites

### 2. Intelligent Chat Widget
- **Priority**: P0
- **Description**: Embeddable chat interface that allows website visitors to ask questions
- **Requirements**:
  - Responsive design that works on mobile and desktop
  - Customizable appearance (colors, position, size)
  - Support for rich media responses (images, links)
  - Conversation history preserved within session
  - Typing indicators and loading states
- **Technical Notes**: Implement using React components with MastrAI integration

### 3. Management Dashboard
- **Priority**: P0
- **Description**: Central interface for customers to manage their chatbot implementation
- **Requirements**:
  - Website connection configuration
  - Chatbot customization options
  - Analytics dashboard
  - Conversation logs and feedback review
  - Access control for team members
- **Technical Notes**: Build with Next.js and secure with Clerk authentication

### 4. Analytics and Reporting
- **Priority**: P1
- **Description**: Data on chatbot usage and performance
- **Requirements**:
  - Conversation volume metrics
  - Common questions and topics
  - User satisfaction metrics
  - Conversion tracking
  - Exportable reports
- **Technical Notes**: Implement event tracking and aggregation in PostgreSQL

### 5. Training and Customization
- **Priority**: P1
- **Description**: Tools to improve chatbot responses and behavior
- **Requirements**:
  - Custom responses for specific questions
  - FAQ management
  - Response review and feedback loop
  - Conversation flow customization
- **Technical Notes**: Store custom responses and rules in PostgreSQL

## Sample Embed Code

Website owners will add the Bubl widget to their sites using this embed code:

```html
<!-- Bubl Embed Code -->
<script>
  (function(w, d, s, o) {
    // Create script element
    var js = d.createElement(s);
    js.async = true;
    js.src = 'https://cdn.webchat-ai.com/widget/v1/loader.js'; // Your CDN URL
    
    // Add initialization data
    w.WebChatAI = w.WebChatAI || {
      config: {
        websiteId: 'YOUR_WEBSITE_ID', // Will be dynamically replaced with customer's unique ID
        primaryColor: '#4F46E5', // Can be customized by customer
        position: 'bottom-right', // Can be customized
        welcomeMessage: 'Hi there! How can I help you today?', // Can be customized
        headerText: 'Website Assistant', // Can be customized
        widgetSize: 'standard', // Can be customized
        avatar: 'https://cdn.webchat-ai.com/avatars/default.png', // Optional custom avatar
        allowAttachments: false, // Feature flag
        initiallyOpen: false // Start closed or open
      },
      // Optional callback when widget is ready
      onReady: function() {
        console.log('Bubl widget is ready');
      }
    };
    
    // Append script to document
    var s = d.getElementsByTagName(s)[0];
    s.parentNode.insertBefore(js, s);
  })(window, document, 'script');
</script>
<!-- End Bubl Embed Code -->
```

This embed code will:
- Load asynchronously to avoid blocking page rendering
- Allow for customization via configuration parameters
- Connect to the Bubl backend using a unique website identifier
- Initialize the chat interface with customer-specific settings

## Database Schema

### Tables

#### 1. Websites
- id (UUID, PK)
- url (String)
- name (String)
- createdAt (DateTime)
- updatedAt (DateTime)
- lastCrawledAt (DateTime, nullable)
- crawlFrequency (Enum: DAILY, WEEKLY, MONTHLY)
- status (Enum: ACTIVE, PENDING, ERROR)

#### 2. WebsitePages
- id (UUID, PK)
- websiteId (UUID, FK)
- url (String)
- title (String)
- content (Text)
- createdAt (DateTime)
- updatedAt (DateTime)
- lastCrawledAt (DateTime)

#### 3. Embeddings
- id (UUID, PK)
- pageId (UUID, FK)
- chunk (Text)
- embedding (Vector)
- createdAt (DateTime)
- updatedAt (DateTime)

#### 4. ChatConfigurations
- id (UUID, PK)
- websiteId (UUID, FK)
- name (String)
- primaryColor (String)
- secondaryColor (String)
- headerText (String)
- welcomeMessage (String)
- position (Enum: BOTTOM_RIGHT, BOTTOM_LEFT, etc.)
- createdAt (DateTime)
- updatedAt (DateTime)
- isActive (Boolean)

#### 5. Conversations
- id (UUID, PK)
- websiteId (UUID, FK)
- visitorId (String)
- startedAt (DateTime)
- endedAt (DateTime, nullable)
- referringUrl (String, nullable)
- deviceInfo (JSON, nullable)
- rating (Integer, nullable)
- feedback (Text, nullable)

#### 6. Messages
- id (UUID, PK)
- conversationId (UUID, FK)
- content (Text)
- role (Enum: USER, ASSISTANT)
- createdAt (DateTime)
- sources (JSON, nullable)

## API Endpoints

### Authentication
- `POST /api/auth/callback` - Clerk authentication callback
- `GET /api/auth/user` - Get current user information

### Website Management
- `GET /api/websites` - List websites 
- `POST /api/websites` - Add new website
- `GET /api/websites/:id` - Get website details
- `PUT /api/websites/:id` - Update website settings
- `DELETE /api/websites/:id` - Remove website
- `POST /api/websites/:id/crawl` - Trigger website crawl

### Chatbot Configuration
- `GET /api/websites/:id/chat-config` - Get chat configuration
- `PUT /api/websites/:id/chat-config` - Update chat configuration
- `GET /api/websites/:id/embed-code` - Get embed code

### Chat Operations
- `POST /api/chat/:websiteId` - Process chat message
- `GET /api/chat/:conversationId/history` - Get conversation history

### Analytics
- `GET /api/websites/:id/analytics` - Get website analytics
- `GET /api/websites/:id/conversations` - List conversations
- `GET /api/conversations/:id` - Get conversation details

## User Flows

### Website Owner Onboarding
1. Sign up with email/password or OAuth via Clerk
2. Add website URL
3. System crawls website and builds knowledge base
4. Configure chatbot appearance
5. Get embed code and add to website

### Website Visitor Interaction
1. Visit website with embedded chatbot
2. Open chat widget
3. Ask question
4. Receive answer based on website content
5. Continue conversation or close chat
6. Optionally provide feedback

### Monitoring and Improvement
1. Website owner logs into dashboard
2. Views analytics and conversation logs
3. Identifies common questions or issues
4. Adds custom responses or trains chatbot
5. Monitors improvement in user satisfaction

## Non-Functional Requirements

### Performance
- Chat responses delivered in < 2 seconds
- Website crawling completed within 10 minutes for average sites
- Dashboard loads in < 3 seconds

### Scalability
- System handles up to 1,000 concurrent chat sessions
- Support for websites with up to 10,000 pages
- Ability to serve 1,000+ customer accounts

### Security
- All traffic encrypted via HTTPS
- Customer data isolation
- Authentication with JWT tokens
- Regular security audits

### Compliance
- GDPR compliance for EU customers
- Data retention policies configurable by customer
- Privacy policy and terms of service templates provided

## MVP Scope

For the initial MVP release, focus on:

1. Basic website crawling and content indexing
2. Simple, customizable chat widget
3. Core management dashboard
4. MastrAI integration for natural language understanding
5. Basic analytics

## Feature Roadmap

### Phase 1 (MVP) - Month 1-2
- Website crawling and indexing
- Basic chat widget
- Core dashboard functionality
- Simple customization options

### Phase 2 - Month 3-4
- Enhanced analytics
- Advanced customization
- Custom training capabilities
- File attachment support

### Phase 3 - Month 5-6
- Multi-language support
- Integration with popular CRMs
- Advanced conversation flows
- API access for developers

## Success Metrics

- **Customer Acquisition**: 100 active customers within 3 months
- **User Engagement**: Average of 500 chat interactions per customer website monthly
- **Customer Satisfaction**: 85% positive feedback on chatbot responses
- **Business Impact**: 20% reduction in support tickets for customers

## Technical Implementation Notes

### MastrAI Integration
- Use MastrAI's API for question answering capabilities
- Implement retrieval-augmented generation pattern
- Store website content as embeddings in PostgreSQL with pgvector
- Match user queries against relevant content before generating responses

### Data Processing Pipeline
1. Crawl website pages
2. Extract main content
3. Split into manageable chunks
4. Generate embeddings via MastrAI
5. Store in PostgreSQL with pgvector
6. Update periodically based on configured frequency

### Chatbot Frontend
- Lightweight React component
- Minimal dependencies
- Customizable via CSS variables
- Responsive design with mobile-first approach

### Deployment Strategy
- Next.js application deployed on Vercel
- PostgreSQL database on managed provider (Supabase or Neon)
- Serverless functions for API endpoints

## Monitoring and Maintenance

- Error tracking via Sentry
- Performance monitoring via Vercel Analytics
- Database monitoring via provider tools
- Regular backups and disaster recovery plan

## Budget and Resource Allocation

### Development Resources
- 1 Full-stack developer (You)
- Optional: UI/UX designer for polished interfaces

### Operating Costs (Monthly Estimate)
- Vercel hosting: $20-100/month
- PostgreSQL database: $50-200/month
- MastrAI API: Variable based on usage
- Clerk Authentication: $25-100/month

### Development Timeline
- MVP development: 4-6 weeks
- Beta testing: 2 weeks
- Initial launch: Week 8-9