# Bubl

Bubl is a SaaS platform that enables businesses to embed intelligent chatbots on their websites. The chatbots are designed to understand and answer questions about website content through a conversational interface.

## Features

- **Website Crawling**: Automatically crawl and index website content
- **Vector Search**: Use semantic search to find relevant content
- **Chat Widget**: Customizable chat interface for websites
- **Admin Dashboard**: Manage websites, chat configurations, and analytics
- **MastrAI Integration**: Intelligent responses powered by MastrAI

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL with pgvector extension
- Redis (optional, for rate limiting)
- Browserless (optional, for Docker/server deployments)

### Setup

1. Clone the repository:

```bash
git clone https://github.com/yourusername/bubl.git
cd bubl
```

2. Install dependencies:

```bash
pnpm install
```

3. Set up environment variables:

```bash
cp .env.example .env.local
```

Edit `.env.local` with your database credentials and API keys.

4. Set up the database:

```bash
# Create the database
createdb bubl

# Enable pgvector extension and run migrations
pnpm db:setup
```

5. Start the development server:

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Vector Search Implementation with MastrAI RAG and pgvector

The vector search functionality is implemented using MastrAI's built-in RAG (Retrieval-Augmented Generation) capabilities and PostgreSQL's pgvector extension:

1. **Website Crawler**: Extracts content from websites using Puppeteer and Cheerio
2. **MastrAI RAG**: Handles document processing, chunking, and embedding generation
3. **PgVector Integration**: Uses pgvector for efficient vector similarity search
4. **Website Context Tool**: Integrates with the chat system to provide relevant content

### pgvector Implementation

We use PostgreSQL with the pgvector extension for vector similarity search:

- **Custom Vector Type**: Implemented using Drizzle ORM's custom type system
- **IVFFLAT Indexing**: Optimized for fast approximate nearest neighbor search
- **Cosine Similarity**: Used for semantic matching between queries and content
- **Efficient Storage**: Vectors stored directly in PostgreSQL alongside other data

MastrAI's RAG capabilities provide several advantages:

- **Optimized Chunking**: Automatically splits documents into semantically meaningful chunks
- **Efficient Embedding Generation**: Uses state-of-the-art embedding models
- **Seamless Integration**: Works natively with MastrAI's agent framework
- **Vector Database Support**: Built-in support for PostgreSQL with pgvector

For detailed setup instructions, see [Vector Search Setup](docs/vector-search-setup.md).

## Browserless Integration for Website Crawling

For Docker and server deployments, we recommend using Browserless to handle website crawling. This eliminates the need to install Chrome in your container and provides a more reliable crawling experience.

### Browserless Setup Options

1. **Self-hosted**: Run your own Browserless instance using Docker
2. **Cloud Service**: Use [Browserless.io](https://browserless.io) cloud service

### Configuration

Add these environment variables to your deployment:

```
USE_BROWSERLESS=true
BROWSERLESS_URL=wss://your-browserless-instance-url/
BROWSERLESS_API_KEY=your-api-key-if-needed
```

For detailed setup instructions, see [Browserless Setup](docs/browserless-setup.md).

### Crawling a Website

To crawl a website, use the API endpoint:

```bash
curl -X POST http://localhost:3000/api/dashboard/websites/{websiteId}/crawl \
  -H "Content-Type: application/json" \
  -d '{
    "maxPages": 100,
    "maxDepth": 3,
    "generateEmbeddings": true
  }'
```

### Using Vector Search in Chat

The chat system automatically uses vector search to find relevant content based on the user's query. The `websiteContextTool` is enhanced to use MastrAI's RAG capabilities to retrieve the most relevant content from the website.

```typescript
// Example of how the website context tool uses MastrAI RAG
const ragService = new WebsiteRagService();
const similarContent = await ragService.searchSimilarContent(
  websiteId,
  query,
  5 // Limit to 5 results
);
```

## Tech Stack

- **Frontend**: Next.js 15, React 19, TailwindCSS, Shadcn UI
- **Backend**: Next.js API routes
- **Database**: PostgreSQL with pgvector
- **ORM**: Drizzle ORM
- **Authentication**: Clerk
- **AI**: MastrAI, OpenAI
- **Deployment**: Vercel

## Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [MastrAI Documentation](https://docs.mastra.ai)
- [pgvector Documentation](https://github.com/pgvector/pgvector)
