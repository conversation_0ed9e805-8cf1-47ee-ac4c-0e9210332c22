#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to prompt for input
function prompt(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function main() {
  try {
    // Get package name
    const packageName = await prompt('Package name (without @bubl/ prefix): ');
    if (!packageName) {
      console.error('Package name is required');
      process.exit(1);
    }

    // Get package description
    const description = await prompt('Package description: ');
    
    // Create package directory
    const packageDir = path.join(__dirname, '..', 'packages', packageName);
    if (fs.existsSync(packageDir)) {
      console.error(`Package directory already exists: ${packageDir}`);
      process.exit(1);
    }
    
    fs.mkdirSync(packageDir, { recursive: true });
    fs.mkdirSync(path.join(packageDir, 'src'), { recursive: true });
    
    // Create package.json
    const packageJson = {
      name: `@bubl/${packageName}`,
      version: '0.1.0',
      description: description || `Bubl ${packageName} package`,
      main: 'dist/index.js',
      module: 'dist/index.mjs',
      types: 'dist/index.d.ts',
      files: ['dist'],
      scripts: {
        build: 'tsup',
        dev: 'tsup --watch',
        clean: 'rimraf dist',
        lint: 'eslint "src/**/*.ts*"',
        typecheck: 'tsc --noEmit'
      },
      keywords: [
        'bubl',
        packageName
      ],
      author: 'Bubl Team',
      license: 'MIT',
      repository: {
        type: 'git',
        url: 'https://github.com/bublchat/bubl'
      },
      homepage: 'https://bublai.com',
      devDependencies: {
        '@types/node': '^18.0.0',
        'eslint': '^8.0.0',
        'rimraf': '^5.0.0',
        'tsup': '^8.0.0',
        'typescript': '^5.0.0'
      }
    };
    
    fs.writeFileSync(
      path.join(packageDir, 'package.json'),
      JSON.stringify(packageJson, null, 2)
    );
    
    // Create tsconfig.json
    const tsConfig = {
      compilerOptions: {
        target: 'es2018',
        lib: ['dom', 'dom.iterable', 'esnext'],
        allowJs: true,
        skipLibCheck: true,
        strict: true,
        forceConsistentCasingInFileNames: true,
        noEmit: true,
        esModuleInterop: true,
        module: 'esnext',
        moduleResolution: 'node',
        resolveJsonModule: true,
        isolatedModules: true,
        declaration: true,
        declarationDir: 'dist',
        outDir: 'dist'
      },
      include: ['src'],
      exclude: ['node_modules', 'dist']
    };
    
    fs.writeFileSync(
      path.join(packageDir, 'tsconfig.json'),
      JSON.stringify(tsConfig, null, 2)
    );
    
    // Create tsup.config.ts
    const tsupConfig = `import { defineConfig } from 'tsup';

export default defineConfig({
  entry: ['src/index.ts'],
  format: ['cjs', 'esm'],
  dts: true,
  splitting: false,
  sourcemap: true,
  clean: true,
});`;
    
    fs.writeFileSync(path.join(packageDir, 'tsup.config.ts'), tsupConfig);
    
    // Create .npmignore
    const npmIgnore = `# Source files
src/
tsconfig.json
tsup.config.ts

# Development files
.eslintrc
.prettierrc
.vscode/
.idea/
.github/

# Build artifacts
*.tsbuildinfo
*.log
.turbo/

# Test files
__tests__/
*.test.ts
*.test.tsx
*.spec.ts
*.spec.tsx
coverage/

# Misc
.DS_Store
.env*
node_modules/`;
    
    fs.writeFileSync(path.join(packageDir, '.npmignore'), npmIgnore);
    
    // Create README.md
    const readme = `# @bubl/${packageName}

${description || `Bubl ${packageName} package`}

## Installation

\`\`\`bash
npm install @bubl/${packageName}
\`\`\`

## Usage

\`\`\`typescript
import { ... } from '@bubl/${packageName}';

// Your code here
\`\`\`

## License

MIT`;
    
    fs.writeFileSync(path.join(packageDir, 'README.md'), readme);
    
    // Create index.ts
    fs.writeFileSync(path.join(packageDir, 'src', 'index.ts'), '// Export your package functionality here\n');
    
    // Update packages/package.json to include the new package
    const packagesPackageJsonPath = path.join(__dirname, '..', 'packages', 'package.json');
    const packagesPackageJson = JSON.parse(fs.readFileSync(packagesPackageJsonPath, 'utf8'));
    
    // Update build script
    packagesPackageJson.scripts.build = packagesPackageJson.scripts.build.replace(
      'pnpm --filter="./react"',
      `pnpm --filter="./react" --filter="./${packageName}"`
    );
    
    // Update dev script
    packagesPackageJson.scripts.dev = packagesPackageJson.scripts.dev.replace(
      'pnpm --filter="./react"',
      `pnpm --filter="./react" --filter="./${packageName}"`
    );
    
    // Update clean script
    packagesPackageJson.scripts.clean = packagesPackageJson.scripts.clean.replace(
      'pnpm --filter="./react"',
      `pnpm --filter="./react" --filter="./${packageName}"`
    );
    
    // Update lint script
    packagesPackageJson.scripts.lint = packagesPackageJson.scripts.lint.replace(
      'pnpm --filter="./react"',
      `pnpm --filter="./react" --filter="./${packageName}"`
    );
    
    // Update typecheck script
    packagesPackageJson.scripts.typecheck = packagesPackageJson.scripts.typecheck.replace(
      'pnpm --filter="./react"',
      `pnpm --filter="./react" --filter="./${packageName}"`
    );
    
    // Update publish-packages script
    packagesPackageJson.scripts['publish-packages'] = packagesPackageJson.scripts['publish-packages'].replace(
      'pnpm --filter="./react"',
      `pnpm --filter="./react" --filter="./${packageName}"`
    );
    
    fs.writeFileSync(packagesPackageJsonPath, JSON.stringify(packagesPackageJson, null, 2));
    
    // Update packages/README.md to include the new package
    const packagesReadmePath = path.join(__dirname, '..', 'packages', 'README.md');
    let packagesReadme = fs.readFileSync(packagesReadmePath, 'utf8');
    
    // Find the table and add a new row
    const tableRow = `| [@bubl/${packageName}](./${packageName}) | ${description || `Bubl ${packageName} package`} | 0.1.0 |`;
    packagesReadme = packagesReadme.replace(
      '| [@bubl/react](./react) | React component for embedding Bubl chat widget | 0.1.0 |',
      `| [@bubl/react](./react) | React component for embedding Bubl chat widget | 0.1.0 |\n${tableRow}`
    );
    
    fs.writeFileSync(packagesReadmePath, packagesReadme);
    
    console.log(`Package @bubl/${packageName} created successfully!`);
    console.log(`\nNext steps:`);
    console.log(`1. cd packages/${packageName}`);
    console.log(`2. pnpm install`);
    console.log(`3. Start developing in src/index.ts`);
    
  } catch (error) {
    console.error('Error creating package:', error);
    process.exit(1);
  } finally {
    rl.close();
  }
}

main();
