#!/usr/bin/env node

/**
 * Build script for Widget V2
 * Creates a self-contained bundle with React and all dependencies
 */

const esbuild = require('esbuild');
const fs = require('fs');
const path = require('path');

async function buildWidget() {
  console.log('🔨 Building Widget V2...');

  try {
    // Build the widget bundle
    const result = await esbuild.build({
      entryPoints: ['src/components/widget-v2/bundle-entry.tsx'],
      bundle: true,
      minify: true,
      format: 'iife',
      outfile: 'public/widget/v2/bundle.js',
      platform: 'browser',
      target: ['es2015'],
      external: [], // Bundle everything
      define: {
        'process.env.NODE_ENV': '"production"',
        'global': 'window',
      },
      jsx: 'automatic',
      jsxImportSource: 'react',
      loader: {
        '.tsx': 'tsx',
        '.ts': 'tsx',
      },
      banner: {
        js: `
/**
 * Bubl Widget V2 Bundle
 * Self-contained React widget with Shadow DOM isolation
 * Generated: ${new Date().toISOString()}
 */
`,
      },
    });

    if (result.errors.length > 0) {
      console.error('❌ Build errors:', result.errors);
      process.exit(1);
    }

    if (result.warnings.length > 0) {
      console.warn('⚠️ Build warnings:', result.warnings);
    }

    // Get bundle size
    const bundlePath = 'public/widget/v2/bundle.js';
    const stats = fs.statSync(bundlePath);
    const sizeKB = (stats.size / 1024).toFixed(2);

    console.log(`✅ Widget V2 built successfully!`);
    console.log(`📦 Bundle size: ${sizeKB} KB`);
    console.log(`📁 Output: ${bundlePath}`);

  } catch (error) {
    console.error('❌ Build failed:', error);
    process.exit(1);
  }
}

// Run the build
buildWidget();
