# Project Rules and Patterns

## Code Style
- Use TypeScript for all new code
- Follow Next.js App Router patterns
- Implement component-based architecture
- Use TailwindCSS for styling
- Format code with Biome
- Run linting checks before commits with Husky

## Naming Conventions
- React components: PascalCase
- Files: kebab-case
- Functions: camelCase
- Database columns: snake_case
- Database tables: plural snake_case

## Project Structure
- /src for application code
- /docs for documentation
- /public for static assets
- /memory-bank for project context
- /src/lib for utility code
- /src/lib/db for database code
- /src/components for UI components
- /src/app for pages and routes

## Development Workflow
- Feature branches from main
- Commit message format: conventional commits
- PR reviews required
- Tests required for core functionality
- Run db:migrate for database changes

## Best Practices
- Server components by default
- Client components when needed
- Type safety throughout
- Progressive enhancement
- Use Clerk for authentication
- Use Recharts for dashboard UI components
- Use Zod for validation 