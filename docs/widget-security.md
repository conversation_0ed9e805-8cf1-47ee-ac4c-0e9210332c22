# Bubl Widget Security Guide

This document outlines the security measures implemented in the Bubl widget to protect both website owners and their visitors.

## Security Features

### 1. Isolation and Sandboxing

The widget is designed with security isolation in mind:

- **iFrame Isolation**: The widget is loaded in an iframe, providing a security boundary between the widget and the parent website.
- **Sandbox Attributes**: The iframe uses sandbox attributes to restrict capabilities while allowing necessary functionality:
  ```javascript
  iframe.sandbox = "allow-scripts allow-forms allow-same-origin";
  ```
- **Cross-Origin Isolation**: The widget uses proper cross-origin techniques to prevent unauthorized access.

### 2. Content Security Policy (CSP)

A strict Content Security Policy is applied to all widget routes:

- **Restricted Sources**: Only allows resources from trusted sources
- **Inline Script Protection**: Limits the use of inline scripts
- **Frame Ancestors Control**: Controls which sites can embed the widget
- **Object Source Restriction**: Prevents loading of potentially dangerous objects

### 3. Input Validation and Sanitization

All inputs are validated and sanitized:

- **Parameter Validation**: Website IDs and other parameters are validated using Zod schemas
- **Input Sanitization**: User-provided content is sanitized to prevent XSS attacks
- **Error Handling**: Proper error handling prevents information leakage

### 4. Subresource Integrity (SRI)

The widget loader script uses Subresource Integrity to ensure it hasn't been tampered with:

```html
<script src="https://your-domain.com/widget/v1/loader.js" 
        integrity="sha384-..." 
        crossorigin="anonymous">
</script>
```

### 5. CORS Protection

Cross-Origin Resource Sharing (CORS) is properly configured:

- **Allowed Origins**: Only specified origins can access the widget API
- **Preflight Handling**: OPTIONS requests are properly handled
- **Credentials Policy**: Strict credentials policy to prevent CSRF attacks

### 6. Rate Limiting

API rate limiting prevents abuse:

- **Widget-Specific Limits**: Higher limits for widget routes (120 requests per minute)
- **IP-Based Tracking**: Limits are tracked by IP address
- **Proper Headers**: Rate limit headers are included in responses

### 7. HTTPS Enforcement

The widget requires HTTPS:

```javascript
// Security check: Ensure we're running in a secure context (HTTPS)
if (window.location.protocol !== 'https:' && window.location.hostname !== 'localhost') {
  console.error("Bubl: Widget requires HTTPS for security reasons");
  return;
}
```

### 8. Secure Communication

The widget uses secure communication methods:

- **Origin Verification**: Messages are only accepted from trusted origins
- **Timestamped Messages**: Messages include timestamps to prevent replay attacks
- **Secure Tokens**: Random tokens are included in messages for verification

## Best Practices for Website Owners

### 1. Keep the Widget Code Updated

Always use the latest version of the widget code provided in your dashboard. We regularly update the widget with security improvements.

### 2. Use HTTPS

Ensure your website uses HTTPS to protect the communication between your visitors and the widget.

### 3. Be Careful with Custom Configurations

If you customize the widget, be careful not to introduce security vulnerabilities:

- Don't modify the widget code unless you understand the security implications
- Don't include sensitive information in the widget configuration
- Use the provided API methods instead of directly manipulating the widget

### 4. Monitor Widget Usage

Regularly check the analytics dashboard to monitor widget usage and detect any unusual patterns that might indicate abuse.

### 5. Report Security Issues

If you discover a security vulnerability, please report it to our security team <NAME_EMAIL>.

## Technical Security Details

### Widget Loader Security

The widget loader script includes several security features:

1. **Strict Mode**: Uses JavaScript strict mode to catch common errors
2. **HTTPS Enforcement**: Checks for HTTPS protocol
3. **Input Validation**: Validates configuration parameters
4. **Timeout Handling**: Implements fetch timeouts to prevent hanging requests
5. **Error Handling**: Properly handles and contains errors
6. **Sanitization**: Sanitizes configuration values to prevent XSS
7. **Secure Messaging**: Uses secure postMessage communication

### API Security

The widget API routes implement:

1. **Input Validation**: Uses Zod schemas to validate all inputs
2. **Rate Limiting**: Implements rate limiting to prevent abuse
3. **Security Headers**: Applies strict security headers to all responses
4. **CORS Protection**: Properly configures CORS headers
5. **Error Handling**: Implements proper error handling to prevent information leakage

## Security Headers

The following security headers are applied to all widget responses:

```
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'; frame-ancestors *; form-action 'self'; base-uri 'self'; object-src 'none'; media-src 'self'
Strict-Transport-Security: max-age=63072000; includeSubDomains; preload
X-Content-Type-Options: nosniff
Referrer-Policy: origin
Permissions-Policy: geolocation=(), microphone=(), camera=(), payment=(), autoplay=(), fullscreen=(), display-capture=(), battery=(), accelerometer=(), gyroscope=(), magnetometer=(), midi=(), usb=(), xr-spatial-tracking=()
X-XSS-Protection: 1; mode=block
Cache-Control: no-store, no-cache, must-revalidate, proxy-revalidate
Pragma: no-cache
Expires: 0
```

## Regular Security Audits

We regularly conduct security audits of the widget code and infrastructure to identify and address potential vulnerabilities.

## Compliance

The widget is designed to help website owners comply with privacy regulations such as GDPR and CCPA by:

1. Minimizing data collection
2. Providing clear privacy notices
3. Implementing proper data handling practices
4. Allowing visitors to control their data

## Contact

For security-related questions or to report a vulnerability, please contact our security <NAME_EMAIL>.
