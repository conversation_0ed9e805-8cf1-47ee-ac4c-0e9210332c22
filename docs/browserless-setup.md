# Browserless Setup for Bubl

This document explains how to set up and use <PERSON><PERSON>erless with <PERSON><PERSON><PERSON> for website crawling.

## What is Browserless?

Browserless is a service that provides headless Chrome instances as a service. It eliminates the need to install and manage Chrome within your Docker container or server environment, making it ideal for serverless or containerized deployments.

## Why Use Browserless?

1. **Docker Compatibility**: Eliminates Chrome installation issues in Docker containers
2. **Resource Efficiency**: Reduces memory and CPU usage on your application server
3. **Scalability**: Can handle multiple concurrent crawling operations
4. **Reliability**: Provides a stable browser environment for crawling

## Setup Options

### Option 1: Self-hosted Browserless

You can run your own Browserless instance using Docker:

```bash
docker run -p 3000:3000 browserless/chrome
```

### Option 2: Browserless.io Cloud Service

You can use the [Browserless.io](https://browserless.io) cloud service, which offers a free tier and paid plans for higher usage.

## Configuration

1. Add the following environment variables to your `.env` file:

```
USE_BROWSERLESS=true
BROWSERLESS_URL=wss://your-browserless-instance-url/
BROWSERLESS_API_KEY=your-api-key-if-needed
```

2. If using Coolify, add these environment variables to your Coolify service configuration.

## Testing the Setup

You can test your Browserless setup by running a crawl operation on a website:

1. From the dashboard, select a website
2. Click "Crawl Website"
3. Check the logs for messages indicating Browserless connection

## Troubleshooting

### Common Issues

1. **Connection Errors**:
   - Ensure your BROWSERLESS_URL is correct and accessible from your server
   - Check if your API key is valid (if using a service that requires authentication)

2. **Timeout Errors**:
   - Increase the navigation timeout in the crawler settings
   - Check if your Browserless instance has enough resources

3. **Missing Content**:
   - Ensure the website doesn't block headless browsers
   - Try adjusting the waitUntil parameter in page.goto()

### Logs to Check

When troubleshooting, look for these log messages:

- `Crawler configuration: Using Browserless: Yes` - Confirms Browserless is enabled
- `Connecting to Browserless at: [URL]` - Shows the connection attempt
- `Successfully connected to Browserless` - Confirms successful connection
- `Failed to connect to Browserless: [Error]` - Shows connection failures

## Performance Considerations

- Set appropriate concurrency limits based on your Browserless instance capacity
- Consider implementing rate limiting for crawling operations
- Monitor memory usage on your application server

## Security Considerations

- Use an API key with Browserless to prevent unauthorized access
- Ensure your Browserless instance is not publicly accessible without authentication
- Consider using a VPN or private network for communication with Browserless
