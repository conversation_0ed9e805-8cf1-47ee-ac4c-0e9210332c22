# Automated RAG Optimization

This document explains the automated RAG (Retrieval-Augmented Generation) optimization system implemented in Bubl.

## Overview

The RAG optimization process has been automated to ensure optimal performance without manual intervention. The system:

1. Automatically processes website content using optimal chunking strategies
2. Rebuilds vector indexes with parameters optimized for the dataset size
3. Runs on a scheduled basis to maintain performance
4. Can be manually triggered when needed

## Scheduled Optimization

The system automatically runs RAG optimization on a weekly basis:

- **Schedule**: Every Sunday at midnight
- **Process**: All active websites are optimized
- **Optimization Criteria**: Only websites that need optimization are processed (based on data changes)

## Manual Optimization

You can manually trigger RAG optimization in several ways:

### From the Dashboard UI

1. Navigate to the website details page
2. Click the "Optimize RAG" button
3. Choose whether to force optimization or only optimize if needed

### Using the API

For a specific website:

```typescript
import { ragOptimizer } from "@/lib/api/rag-optimizer";

// Optimize a specific website
await ragOptimizer.optimizeWebsite("website-id", {
  force: false // Set to true to force optimization
});
```

For all websites:

```typescript
import { ragOptimizer } from "@/lib/api/rag-optimizer";

// Optimize all websites
await ragOptimizer.optimizeAllWebsites({
  force: false // Set to true to force optimization
});
```

### Using the CLI

You can also trigger optimization from the command line:

```bash
# Optimize a specific website
curl -X POST https://your-domain.com/api/dashboard/websites/{websiteId}/optimize-rag \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {your-auth-token}" \
  -d '{"force": false}'

# Optimize all websites
curl -X POST https://your-domain.com/api/dashboard/websites/optimize-all-rag \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {your-auth-token}" \
  -d '{"force": false}'
```

## Optimization Process

The optimization process includes:

1. **Content Processing**:
   - Uses content-aware chunking strategies
   - Adapts to different types of content (articles, documentation, FAQs)
   - Generates high-quality embeddings

2. **Vector Index Optimization**:
   - Calculates optimal index parameters based on dataset size
   - Rebuilds indexes with these parameters
   - Updates database statistics for better query planning

3. **Cache Management**:
   - Clears caches to ensure fresh results
   - Rebuilds caches for frequently accessed queries

## Monitoring

You can monitor the optimization process through:

1. **Inngest Dashboard**: View the status and logs of optimization jobs
2. **Application Logs**: Check server logs for detailed information
3. **Website Status**: The website status will show "PROCESSING" during optimization

## Troubleshooting

If you encounter issues with RAG optimization:

1. **Check Website Status**: If the status is stuck in "PROCESSING", there might be an issue
2. **Review Inngest Logs**: Check the Inngest dashboard for error messages
3. **Manual Rebuild**: Try manually rebuilding the vector index:
   ```bash
   pnpm db:rebuild-vectors
   ```
4. **Force Optimization**: Use the force option to completely rebuild all embeddings and indexes

## Performance Impact

During optimization:
- The website remains accessible
- Search performance might be temporarily affected
- CPU and memory usage will increase

The optimization process is designed to be non-disruptive, but it's recommended to schedule it during off-peak hours for production environments.
