# Widget Analytics and Performance Improvements

This document outlines the improvements made to the Bubl widget's analytics and performance monitoring capabilities.

## Analytics Enhancements

### Core Web Vitals Tracking

We've implemented comprehensive tracking of Core Web Vitals metrics to better understand the widget's performance impact:

- **Largest Contentful Paint (LCP)**: Measures loading performance
- **First Input Delay (FID)**: Measures interactivity
- **Cumulative Layout Shift (CLS)**: Measures visual stability
- **First Contentful Paint (FCP)**: Measures when first content is painted
- **Time to First Byte (TTFB)**: Measures server response time
- **Time to Interactive (TTI)**: Measures when the page becomes interactive

### Network Information Tracking

We now collect network information to understand how connection quality affects widget performance:

- **Effective Connection Type**: 4G, 3G, etc.
- **Downlink Speed**: Connection bandwidth
- **Round Trip Time (RTT)**: Network latency
- **Save Data Mode**: Whether data-saving is enabled

### Enhanced Analytics Dashboard

The analytics dashboard has been updated to display:

- Detailed Core Web Vitals metrics
- Network performance statistics
- Automatic performance assessment (Good, Needs Improvement, Poor)
- Personalized optimization recommendations based on collected metrics

## Performance Optimizations

### Widget Loader Script

The widget loader script has been optimized for better performance:

- **Resource Preloading**: CSS is now preloaded to improve loading performance
- **Script Loading Optimization**: Non-critical scripts use `defer` and `async`
- **Resource Prioritization**: Using `fetchPriority` to prioritize critical resources
- **Error Handling**: Improved error tracking and reporting

### CSS Optimizations

The widget's CSS has been optimized for better rendering performance:

- **Hardware Acceleration**: Using `transform: translateZ(0)` for smoother animations
- **Content Visibility**: Using `content-visibility: auto` to optimize rendering
- **Layout Containment**: Using `contain` property to reduce layout recalculations
- **Box Sizing**: Using `box-sizing: border-box` to reduce layout shifts

### Iframe Optimizations

The widget iframe has been optimized for better loading and security:

- **Lazy Loading**: Using `loading="lazy"` for deferred loading
- **Resource Prioritization**: Using `importance="low"` for non-visible iframe
- **Security Enhancements**: Using appropriate `sandbox` attributes
- **Performance Tracking**: Tracking iframe load time and performance metrics

## Implementation Details

### Web Vitals Library Integration

We've integrated the `web-vitals` library to collect standardized performance metrics:

```javascript
import { getCLS, getFID, getLCP, getTTFB, getFCP } from 'web-vitals';

// Track Core Web Vitals
getCLS(({ value }) => trackMetric("widget_cls", value));
getFID(({ value }) => trackMetric("widget_fid", value));
getLCP(({ value }) => trackMetric("widget_lcp", value));
getTTFB(({ value }) => trackMetric("widget_ttfb", value));
getFCP(({ value }) => trackMetric("widget_fcp", value));
```

### Analytics Data Structure

The analytics data structure has been expanded to include:

```typescript
interface WebsiteStats {
  // Existing metrics
  totalConversations: number;
  totalMessages: number;
  averageResponseTime: number;
  ragUsagePercentage: number;
  
  // New performance metrics
  webVitals: {
    cls: { avg: number };
    lcp: { avg: number };
    fcp: { avg: number };
    ttfb: { avg: number };
  };
  networkInfo: {
    avgDownlink: number;
    avg4gPercentage: number;
    avg3gPercentage: number;
    avgRtt: number;
  };
}
```

## Future Improvements

1. **Real User Monitoring (RUM)**: Implement more comprehensive RUM to track actual user experiences
2. **Performance Budgets**: Set performance budgets and alert when they're exceeded
3. **A/B Testing**: Test different widget configurations for optimal performance
4. **Adaptive Loading**: Implement adaptive loading based on device and network capabilities
5. **Offline Support**: Add offline support for better user experience in poor network conditions
