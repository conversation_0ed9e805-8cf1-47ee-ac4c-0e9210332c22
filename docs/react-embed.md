# React Embed for Bubl Chat Widget

This document explains how to use the Bubl chat widget in React applications.

## Installation

You can install the Bubl React package from npm:

```bash
# npm
npm install @bubl/react

# yarn
yarn add @bubl/react

# pnpm
pnpm add @bubl/react
```

## Basic Usage

Once installed, you can use the `BublChat` component in your React application:

```tsx
import { BublChat } from '@bubl/react';

function App() {
  return (
    <div>
      {/* Your website content */}
      
      <BublChat 
        websiteId="your-website-id"
        primaryColor="#4F46E5"
        secondaryColor="#FFFFFF"
        position="bottom-right"
        welcomeMessage="Hi there! How can I help you today?"
        headerText="Chat Assistant"
        initiallyOpen={false}
        onReady={() => console.log("Bubl widget is ready")}
      />
    </div>
  );
}
```

## Props

The `BublChat` component accepts the following props:

| Prop | Type | Default | Description |
| --- | --- | --- | --- |
| `websiteId` | `string` | **Required** | Your website ID from the Bubl dashboard |
| `primaryColor` | `string` | `"#4F46E5"` | Primary color for the chat widget (hex code) |
| `secondaryColor` | `string` | `"#FFFFFF"` | Secondary color for the chat widget (hex code) |
| `position` | `"bottom-right"` \| `"bottom-left"` | `"bottom-right"` | Position of the chat widget |
| `welcomeMessage` | `string` | `"Hi there! How can I help you today?"` | Welcome message displayed when the chat is opened |
| `headerText` | `string` | `"Chat Assistant"` | Text displayed in the header of the chat widget |
| `initiallyOpen` | `boolean` | `false` | Whether the chat widget should be initially open |
| `onReady` | `() => void` | `undefined` | Callback function called when the widget is ready |

## Controlling the Widget

You can control the widget programmatically using the global `Bubl.api` object:

```tsx
// Open the chat widget
window.Bubl?.api?.open();

// Close the chat widget
window.Bubl?.api?.close();

// Toggle the chat widget
window.Bubl?.api?.toggle();
```

## Self-hosting

If you're self-hosting Bubl, you can specify a custom app URL by setting the `window.bublConfig.appUrl` property before rendering the component:

```tsx
// Set custom app URL
window.bublConfig = {
  appUrl: "https://your-bubl-instance.com"
};

// Then render the component
ReactDOM.render(
  <BublChat websiteId="your-website-id" />,
  document.getElementById('root')
);
```

## Next.js App Router

When using Next.js App Router, make sure to use the component only on the client side:

```tsx
'use client';

import { BublChat } from '@bubl/react';

export default function MyPage() {
  return (
    <div>
      {/* Your page content */}
      
      <BublChat 
        websiteId="your-website-id"
        primaryColor="#4F46E5"
      />
    </div>
  );
}
```

## TypeScript Support

The package includes TypeScript definitions, so you'll get full type checking and autocompletion in TypeScript projects.

## Troubleshooting

If you encounter any issues with the widget, check the following:

1. Make sure your website ID is correct
2. Ensure your website domain is allowed in the Bubl dashboard
3. Check the browser console for any error messages

For more help, contact <NAME_EMAIL>.
