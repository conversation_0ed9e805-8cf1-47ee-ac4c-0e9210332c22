# Bubl Security Documentation

This document outlines the security measures implemented in the Bubl application to protect against common web vulnerabilities and ensure data privacy.

## Security Features

### 1. Authentication & Authorization

Authentication is handled through <PERSON> with proper session management:

- **Protected Routes**: Dashboard and admin routes require authentication
- **Public Routes**: Marketing pages, widget API, and chat endpoints are public
- **Implementation**: Clerk middleware with route-based protection

### 2. CSRF Protection

Cross-Site Request Forgery protection is implemented for all state-changing operations:

- **Token Generation**: Cryptographically secure random tokens
- **Storage**: HTTP-only, secure cookies with proper SameSite attribute
- **Validation**: Server-side validation for all POST, PUT, DELETE, and PATCH requests
- **Implementation**: Double-submit cookie pattern
- **Location**: `src/lib/csrf.ts` and `src/lib/middleware/csrf.ts`

### 3. Security Headers

Comprehensive security headers are applied to all responses:

- **Content Security Policy (CSP)**: Restricts resource loading to trusted sources
- **Strict Transport Security (HSTS)**: Ensures HTTPS usage
- **X-Frame-Options**: Prevents clickjacking
- **X-Content-Type-Options**: Prevents MIME type sniffing
- **Referrer Policy**: Controls referrer information
- **Permissions Policy**: Restricts browser feature usage
- **Location**: `src/lib/middleware/securityHeaders.ts`

### 4. Secure Cookies

All cookies are configured with security best practices:

- **HTTP-Only**: Prevents JavaScript access
- **Secure**: Only sent over HTTPS in production
- **SameSite**: Restricts cross-site cookie usage
- **Path**: Limited to specific paths
- **Expiration**: Proper expiration times
- **Location**: `src/lib/cookies.ts`

### 5. Input Validation

Comprehensive input validation for all API endpoints:

- **Schema Validation**: Using Zod for type-safe validation
- **Request Body Validation**: For JSON payloads
- **Query Parameter Validation**: For URL parameters
- **Error Handling**: Standardized error responses
- **Location**: `src/lib/middleware/validation.ts`

## Security Best Practices

### For API Routes

1. **Always validate input**:
   ```typescript
   const validationResult = await validateRequestBody(mySchema, request);
   if (!("success" in validationResult)) {
     return validationResult;
   }
   const validatedData = validationResult.data;
   ```

2. **Always apply security headers**:
   ```typescript
   const response = NextResponse.json({ data });
   return applySecurityHeaders(response);
   ```

3. **Use CSRF protection for state-changing operations**:
   - CSRF protection is automatically applied via middleware
   - Client-side forms should use the `useCsrfToken` hook

4. **Use secure cookies**:
   ```typescript
   setSecureCookie("cookieName", "value", { maxAge: 3600 });
   ```

### For Client Components

1. **Use the CSRF token hook**:
   ```typescript
   const { token, headerName, getRequestOptions } = useCsrfToken();

   // In fetch calls
   fetch("/api/endpoint", getRequestOptions({
     method: "POST",
     body: JSON.stringify(data)
   }));
   ```

2. **Sanitize user input**:
   - Validate all user input before submission
   - Use controlled components for forms

## Security Testing

1. **Run security scans regularly**:
   ```bash
   # Scan for vulnerable dependencies
   pnpm audit

   # Run security linting
   pnpm security-scan
   ```

2. **Test API endpoints for security vulnerabilities**:
   - Test rate limiting by sending multiple requests
   - Test CSRF protection by attempting cross-site requests
   - Test input validation with malformed data

## Reporting Security Issues

If you discover a security vulnerability, <NAME_EMAIL> instead of using the public issue tracker.
