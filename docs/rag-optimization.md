# RAG Optimization Guide

This document outlines the optimizations made to the Retrieval-Augmented Generation (RAG) system to improve performance, accuracy, and efficiency.

## Implemented Optimizations

### 1. Adaptive Vector Indexing

We've implemented adaptive vector indexing that automatically adjusts the index parameters based on dataset size:

- Small datasets (<1,000 vectors): 10 lists
- Medium datasets (1,000-10,000 vectors): sqrt(N) lists
- Large datasets (10,000-100,000 vectors): N/100 lists
- Very large datasets (>100,000 vectors): N/200 lists

This optimization ensures that the IVFFlat index is properly balanced for your specific dataset size, improving both query performance and recall.

To rebuild vector indexes with optimal parameters:

```bash
pnpm db:rebuild-vectors
```

### 2. Intelligent Chunking Strategies

We've implemented content-aware chunking strategies that adapt to different types of content:

- **Default**: 512 tokens with 50 token overlap (general content)
- **Long-form**: 1024 tokens with 100 token overlap (articles, blog posts)
- **Technical**: 384 tokens with 75 token overlap (documentation, code)
- **FAQ**: 256 tokens with 25 token overlap (Q&A content)

The system automatically detects the best chunking strategy based on content analysis, improving the quality of chunks and resulting in better search results.

### 3. Result Reranking

We've added a reranking step that improves the relevance of search results:

- Initial vector search retrieves more candidates than needed
- Reranking uses a combination of:
  - Semantic relevance (70%)
  - Vector similarity (20%)
  - Position-based scoring (10%)
- Reranking is performed using the Mistral model for optimal results

This two-stage retrieval process significantly improves the quality of results compared to vector search alone.

### 4. Query Result Caching

We've implemented an in-memory cache for frequently accessed queries:

- Cache size: 100 entries
- TTL: 15 minutes
- Automatic cache invalidation when content changes
- Cache key based on website ID, normalized query, and limit

The cache significantly improves response times for repeated queries, which is common in chat scenarios where similar questions are asked.

## Usage

The optimizations are automatically applied when using the `WebsiteRagService`. No changes to your existing code are required.

```typescript
import { WebsiteRagService } from "@/lib/services/rag/website-rag-service";

// Create a RAG service instance
const ragService = new WebsiteRagService();

// Search for similar content
const results = await ragService.searchSimilarContent(
  websiteId,
  query,
  5, // Limit
  true // Use reranking (default: true)
);
```

## Maintenance

### Rebuilding Vector Indexes

It's recommended to rebuild vector indexes periodically, especially after adding a significant amount of new content. The system will automatically check if rebuilding is needed during website processing, but you can also manually trigger a rebuild:

```bash
pnpm db:rebuild-vectors
```

### Clearing the Cache

The cache is automatically cleared when content changes, but you can also manually clear it for a specific website:

```typescript
const ragService = new WebsiteRagService();
ragService.clearCache(websiteId);
```

## Performance Considerations

- **Memory Usage**: The in-memory cache uses approximately 1MB per 100 entries (depending on content size)
- **CPU Usage**: Reranking is more CPU-intensive than vector search alone
- **Response Time**: 
  - Cold queries: 200-500ms (depending on dataset size)
  - Cached queries: <50ms
  - Reranking adds 100-300ms to query time

## Future Improvements

Potential future optimizations:

1. **Distributed Caching**: Replace in-memory cache with Redis for multi-server deployments
2. **Hybrid Search**: Combine vector search with keyword search for better results
3. **Query Preprocessing**: Improve query understanding with entity extraction and query expansion
4. **Embedding Model Upgrades**: Evaluate newer embedding models for better semantic representation
5. **Parallel Processing**: Implement batch processing for large datasets
