# Vector Search Setup

This document explains how to set up and use vector search in the Bubl platform.

## Prerequisites

- PostgreSQL with pgvector extension
- Node.js 18+
- pnpm

## Setup Steps

1. **Start the PostgreSQL Database with pgvector**

   If using Docker Compose:

   ```bash
   docker-compose up -d postgres
   ```

   This will start a PostgreSQL instance with the pgvector extension pre-installed.

2. **Enable the pgvector Extension**

   Run the following command to enable the pgvector extension in your database:

   ```bash
   pnpm db:pgvector
   ```

3. **Run Migrations**

   Apply the database migrations:

   ```bash
   pnpm db:migrate
   ```

   Or use the combined setup command:

   ```bash
   pnpm db:setup
   ```

## Schema Structure

The vector search functionality uses two main tables:

1. **embeddings** - Stores embeddings for general content
   - `id` - UUID primary key
   - `pageId` - Reference to the website page
   - `chunk` - Text chunk that was embedded
   - `embedding` - Vector representation (1024 dimensions)
   - `createdAt` - Timestamp
   - `updatedAt` - Timestamp

2. **website_embeddings** - Stores embeddings specifically for website content
   - `id` - UUID primary key
   - `websiteId` - Reference to the website
   - `pageId` - Reference to the website page
   - `chunkText` - Text chunk that was embedded
   - `embedding` - Vector representation (1024 dimensions)
   - `metadata` - Additional metadata (JSON)
   - `createdAt` - Timestamp
   - `updatedAt` - Timestamp

## Usage

### Generating Embeddings

To generate embeddings for website content:

```typescript
import { MDocument } from "@mastra/rag";
import { embed } from "ai";
import { mistral } from "@ai-sdk/mistral";
import { db } from "@/lib/db";
import { websiteEmbeddingsTable } from "@/lib/db/schema";

// Create a document from text content
const doc = MDocument.fromText(pageContent);

// Chunk the document
const chunks = await doc.chunk({
  strategy: "recursive",
  size: 512,
  overlap: 50,
});

// Generate embeddings
const { embedding } = await embed({
  model:  mistral.textEmbeddingModel("mistral-embed"),
  value: chunk.text,
});

// Store in database
await db.insert(websiteEmbeddingsTable).values({
  websiteId,
  pageId,
  chunkText: chunk.text,
  embedding, // pgvector handles the conversion
  metadata: { source: pageUrl },
});
```

### Searching Similar Content

To search for similar content:

```typescript
import { embed } from "ai";
import { mistral } from "@ai-sdk/mistral";
import { sql } from "drizzle-orm";
import { db } from "@/lib/db";

// Generate embedding for the query
const { embedding } = await embed({
  model: mistral.textEmbeddingModel("text-embed"),
  value: query,
});

// Search for similar content
const results = await db.execute(sql`
  SELECT
    chunk_text as text,
    page_id,
    1 - (embedding <-> ${sql.raw(`'[${embedding.join(",")}]'::vector`)}) as similarity
  FROM
    website_embeddings
  WHERE
    website_id = ${websiteId}
  ORDER BY
    embedding <-> ${sql.raw(`'[${embedding.join(",")}]'::vector`)}
  LIMIT 5
`);

// Convert results to the expected format
const similarContent = results.map(row => ({
  text: row.text,
  pageId: row.page_id,
  similarity: row.similarity,
}));
```

## Performance Considerations

- The database uses IVFFLAT indexes for efficient similarity search
- Consider increasing the `lists` parameter for larger datasets
- For very large datasets, consider using approximate nearest neighbor search with a higher `probes` parameter

## Troubleshooting

If you encounter issues with pgvector:

1. Verify the extension is enabled:
   ```sql
   SELECT * FROM pg_extension WHERE extname = 'vector';
   ```

2. Check the vector dimensions:
   ```sql
   SELECT octet_length(embedding::bytea) / 4 as dimensions FROM website_embeddings LIMIT 1;
   ```

3. Ensure indexes are created:
   ```sql
   SELECT indexname FROM pg_indexes WHERE tablename = 'website_embeddings';
   ```
