# Website Crawler Documentation

## Overview

The Website Crawler is a core component of the Bubl platform that extracts content from client websites for use in the chatbot knowledge base. It uses a headless browser to crawl websites, extract content, and store it in the database for later use by the vector search system.

## Features

- **Configurable Crawl Settings**: Control the depth and breadth of crawling
- **Content Extraction**: Intelligent extraction of main content from web pages
- **Robots.txt Compliance**: Respect website crawling rules
- **Rate Limiting**: Configurable delay between requests to avoid overloading websites
- **Browserless Integration**: Support for using Browserless in Docker environments
- **Vector Search Integration**: Automatic generation of embeddings for search

## Configuration Options

| Option | Description | Default |
|--------|-------------|---------|
| `maxPages` | Maximum number of pages to crawl | 100 |
| `maxDepth` | Maximum link depth to crawl | 3 |
| `includePatterns` | Only crawl URLs containing these patterns | [] |
| `excludePatterns` | Skip URLs containing these patterns | ["/wp-admin", "/login", etc.] |
| `generateEmbeddings` | Create vector embeddings for search | true |
| `respectRobotsTxt` | Follow robots.txt rules | true |
| `crawlDelay` | Delay between requests in milliseconds | 1000 |
| `crawlFrequency` | How often to automatically crawl | "WEEKLY" |

## Usage

### Dashboard UI

1. Navigate to the website detail page
2. Click "Configure Crawler" to access the crawler configuration page
3. Set your desired crawl settings
4. Click "Start Crawling" to begin the crawl process

### API

You can also trigger a crawl via the API:

```bash
curl -X POST http://localhost:3000/api/dashboard/websites/{websiteId}/crawl \
  -H "Content-Type: application/json" \
  -d '{
    "maxPages": 100,
    "maxDepth": 3,
    "includePatterns": ["/blog", "/products"],
    "excludePatterns": ["/admin", "/login"],
    "generateEmbeddings": true,
    "respectRobotsTxt": true,
    "crawlDelay": 1000,
    "crawlFrequency": "WEEKLY"
  }'
```

## Content Extraction

The crawler uses a sophisticated approach to extract the main content from web pages:

1. Removes non-content elements like scripts, styles, navigation, etc.
2. Tries to identify the main content container using common selectors
3. Falls back to the body content if no main container is found
4. Cleans up the content by removing boilerplate text and extra whitespace

## Robots.txt Compliance

The crawler respects robots.txt rules by default:

1. Fetches the robots.txt file from the website
2. Parses the rules using the robots-parser library
3. Checks each URL against the rules before crawling
4. Skips URLs that are disallowed by robots.txt

## Browserless Integration

For Docker environments, the crawler can use Browserless for headless browser capabilities:

1. Set the `USE_BROWSERLESS` environment variable to "true"
2. Set the `BROWSERLESS_URL` environment variable to your Browserless endpoint
3. Optionally set the `BROWSERLESS_API_KEY` if your Browserless instance requires authentication

## Vector Search Integration

After crawling, the crawler can automatically generate embeddings for vector search:

1. Processes each crawled page
2. Chunks the content into manageable pieces
3. Generates embeddings using MastrAI's embedding models
4. Stores the embeddings in the database for later use by the vector search system

## Troubleshooting

### Common Issues

1. **Connection Errors**:
   - Ensure your Browserless URL is correct and accessible
   - Check if your API key is valid (if using a service that requires authentication)

2. **Timeout Errors**:
   - Increase the navigation timeout in the crawler settings
   - Check if your Browserless instance has enough resources

3. **Missing Content**:
   - Ensure the website doesn't block headless browsers
   - Try adjusting the waitUntil parameter in page.goto()

4. **Rate Limiting**:
   - Increase the crawlDelay parameter to avoid being rate-limited

## Best Practices

1. **Start Small**: Begin with a small maxPages value to test the crawler
2. **Respect Website Owners**: Keep crawlDelay high enough to avoid overloading websites
3. **Use Include Patterns**: Narrow down the crawl scope to relevant content
4. **Monitor Crawl Status**: Check the website status in the dashboard
5. **Schedule Regular Crawls**: Use the crawlFrequency setting to keep content fresh
