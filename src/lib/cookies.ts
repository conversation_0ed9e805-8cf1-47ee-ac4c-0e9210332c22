import { env } from "@/env";
import { cookies } from "next/headers";

// Define our own CookieOptions type based on Next.js documentation
type CookieOptions = {
	domain?: string;
	expires?: Date;
	httpOnly?: boolean;
	maxAge?: number;
	path?: string;
	priority?: "low" | "medium" | "high";
	sameSite?: boolean | "lax" | "strict" | "none";
	secure?: boolean;
	encode?: (value: string) => string;
	partitioned?: boolean;
};

/**
 * Set a secure cookie with appropriate security options
 * @param name Cookie name
 * @param value Cookie value
 * @param options Additional cookie options
 */
export async function setSecureCookie(
	name: string,
	value: string,
	options: Partial<CookieOptions> = {},
): Promise<void> {
	const cookieStore = await cookies();

	// Default cookie options
	const defaultOptions: Partial<CookieOptions> = {
		// Only send cookie over HTTPS in production
		secure: env.NODE_ENV === "production",
		// Prevent JavaScript access to the cookie
		httpOnly: true,
		// In production, use 'none' to allow cross-origin requests
		// This is needed when the frontend and API might be on different domains
		sameSite: env.NODE_ENV === "production" ? "none" : "lax",
		// Set a default path
		path: "/",
		// Add domain if in production (optional, uncomment if needed)
		// domain: env.NODE_ENV === "production" ? ".yourdomain.com" : undefined,
	};

	// Merge default options with provided options
	const mergedOptions: Partial<CookieOptions> = {
		...defaultOptions,
		...options,
	};

	// Set the cookie
	cookieStore.set(name, value, mergedOptions);
}

/**
 * Get a cookie value
 * @param name Cookie name
 * @returns Cookie value or null if not found
 */
export async function getCookie(name: string): Promise<string | null> {
	const cookieStore = await cookies();
	return cookieStore.get(name)?.value || null;
}

/**
 * Delete a cookie
 * @param name Cookie name
 * @param options Additional cookie options
 */
export async function deleteCookie(
	name: string,
	options: Partial<CookieOptions> = {},
): Promise<void> {
	const cookieStore = await cookies();

	// Default cookie options for deletion
	const defaultOptions: Partial<CookieOptions> = {
		// Only send cookie over HTTPS in production
		secure: env.NODE_ENV === "production",
		// Prevent JavaScript access to the cookie
		httpOnly: true,
		// In production, use 'none' to allow cross-origin requests
		// This is needed when the frontend and API might be on different domains
		sameSite: env.NODE_ENV === "production" ? "none" : "lax",
		// Set a default path
		path: "/",
		// Set expiration in the past to delete the cookie
		maxAge: -1,
	};

	// Merge default options with provided options
	const mergedOptions: Partial<CookieOptions> = {
		...defaultOptions,
		...options,
	};

	// Delete the cookie by setting it with an expired date
	cookieStore.set(name, "", mergedOptions);
}
