import { api } from "./client";

/**
 * Options for RAG optimization
 */
interface RagOptimizationOptions {
	force?: boolean; // Force optimization even if not needed
}

/**
 * Client-side utility for triggering RAG optimization
 */
export const ragOptimizer = {
	/**
	 * Trigger RAG optimization for a specific website
	 *
	 * @param websiteId - The ID of the website to optimize
	 * @param options - Optional configuration options
	 * @returns A promise that resolves when the optimization is triggered
	 */
	optimizeWebsite: async (
		websiteId: string,
		options: RagOptimizationOptions = {},
	) => {
		return api.post<{ message: string }>(
			`/dashboard/websites/${websiteId}/optimize-rag`,
			{
				force: options.force || false,
			},
		);
	},

	/**
	 * Trigger RAG optimization for all websites
	 *
	 * @param options - Optional configuration options
	 * @returns A promise that resolves when the optimization is triggered
	 */
	optimizeAllWebsites: async (options: RagOptimizationOptions = {}) => {
		return api.post<{ message: string }>(
			"/dashboard/websites/optimize-all-rag",
			{
				force: options.force || false,
			},
		);
	},
};
