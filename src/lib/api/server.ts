import type { Website } from "./types";

/**
 * Server-side API client for fetching data
 * This should only be used in server components
 */

/**
 * Generic fetch function for server-side API requests
 */
async function serverFetch<T>(
	endpoint: string,
	options: RequestInit = {},
): Promise<T> {
	// For server components, we need to use the absolute URL with origin
	// In Next.js, we can use the relative URL for API routes
	const url = new URL(endpoint, "http://localhost:3000");

	const response = await fetch(url, {
		...options,
		headers: {
			"Content-Type": "application/json",
			...options.headers,
		},
		// This ensures the request is made from the server
		// and not cached by the browser
		cache: "no-store",
	});

	if (!response.ok) {
		// Try to get error details from the response
		try {
			const errorData = await response.json();
			throw new Error(
				errorData.error?.message || `API error: ${response.status}`,
			);
		} catch (e) {
			// If we can't parse the error, throw a generic one
			throw new Error(`API error: ${response.status}`);
		}
	}

	const data = await response.json();
	return data.data as T;
}

/**
 * Server-side API functions for websites
 */
export const serverWebsitesApi = {
	/**
	 * Get all websites for the current organization
	 */
	getAll: async (): Promise<Website[]> => {
		// We need to handle errors gracefully in server components
		try {
			return await serverFetch<Website[]>("/api/dashboard/websites");
		} catch (error) {
			console.error("Error fetching websites:", error);
			return [];
		}
	},

	/**
	 * Get a single website by ID
	 */
	getById: async (id: string): Promise<Website | null> => {
		try {
			return await serverFetch<Website>(`/api/dashboard/websites/${id}`);
		} catch (error) {
			console.error(`Error fetching website ${id}:`, error);
			return null;
		}
	},
};
