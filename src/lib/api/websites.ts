import { api } from "./client";
import type { CreateWebsiteInput, UpdateWebsiteInput, Website } from "./types";

export const websitesApi = {
	/**
	 * Get all websites for the current organization
	 */
	getAll: () => api.get<Website[]>("/dashboard/websites"),

	/**
	 * Get a single website by ID
	 */
	getById: (id: string) => api.get<Website>(`/dashboard/websites/${id}`),

	/**
	 * Create a new website
	 */
	create: (data: CreateWebsiteInput) =>
		api.post<Website>("/dashboard/websites", data),

	/**
	 * Update an existing website
	 */
	update: (id: string, data: UpdateWebsiteInput) =>
		api.put<Website>(`/dashboard/websites/${id}`, data),

	/**
	 * Delete a website
	 */
	delete: (id: string) => api.delete<void>(`/dashboard/websites/${id}`),
};
