import { z } from "zod";

export const chatMessageSchema = z.object({
	role: z.enum(["user", "assistant"]),
	content: z.string().min(1),
});

export const chatRequestSchema = z.object({
	messages: z.array(chatMessageSchema).min(1),
	websiteId: z.string().min(1),
	visitorId: z.string().optional(),
	conversationId: z.string().optional(),
});

export type ChatRequest = z.infer<typeof chatRequestSchema>;
