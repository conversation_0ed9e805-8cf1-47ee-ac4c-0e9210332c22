import { z } from "zod";

export const crawlRequestSchema = z.object({
	maxPages: z.number().optional(),
	maxDepth: z.number().optional(),
	includePatterns: z.array(z.string()).optional(),
	excludePatterns: z.array(z.string()).optional(),
	generateEmbeddings: z.boolean().optional(),
	crawlFrequency: z.enum(["DAILY", "WEEKLY", "MONTHLY", "MANUAL"]).optional(),
	respectRobotsTxt: z.boolean().optional(),
	crawlDelay: z.number().optional(),
	incrementalCrawl: z.boolean().optional(),
});

export type CrawlRequest = z.infer<typeof crawlRequestSchema>;
