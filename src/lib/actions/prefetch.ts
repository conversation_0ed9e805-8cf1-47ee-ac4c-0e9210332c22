import { serverWebsitesApi } from "@/lib/api/server";
import type { QueryClient } from "@tanstack/react-query";

/**
 * Prefetch websites data for server-side rendering
 */
export async function prefetchWebsites(queryClient: QueryClient) {
	// Prefetch all websites
	await queryClient.prefetchQuery({
		queryKey: ["websites"],
		queryFn: () => serverWebsitesApi.getAll(),
	});
}

/**
 * Prefetch a single website by ID
 */
export async function prefetchWebsite(queryClient: QueryClient, id: string) {
	// Prefetch a single website
	await queryClient.prefetchQuery({
		queryKey: ["websites", id],
		queryFn: () => serverWebsitesApi.getById(id),
	});
}
