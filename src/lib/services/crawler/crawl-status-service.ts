import { db } from "@/lib/db";
import { crawlOperationsTable } from "@/lib/db/schema/crawlOperations";
import type {
	CrawlOperation,
	NewCrawlOperation,
} from "@/lib/db/schema/crawlOperations";
import { eq, sql } from "drizzle-orm";
import { desc } from "drizzle-orm";
import type { CrawlOptions } from "./website-crawler";

export class CrawlStatusService {
	/**
	 * Create a new crawl operation
	 */
	async createOperation(
		websiteId: string,
		options: CrawlOptions,
	): Promise<CrawlOperation> {
		const [operation] = await db
			.insert(crawlOperationsTable)
			.values({
				websiteId,
				status: "PENDING",
				// biome-ignore lint/suspicious/noExplicitAny: Using any type for configuration JSON
				configuration: options as any,
			})
			.returning();

		// biome-ignore lint/suspicious/noExplicitAny: Using any type for configuration JSON
		return operation as any;
	}

	/**
	 * Update a crawl operation
	 */
	async updateOperation(
		id: string,
		data: Partial<NewCrawlOperation>,
	): Promise<CrawlOperation> {
		const [operation] = await db
			.update(crawlOperationsTable)
			.set({
				...data,
				updatedAt: new Date().toISOString(),
			})
			.where(eq(crawlOperationsTable.id, id))
			.returning();

		// biome-ignore lint/suspicious/noExplicitAny: Using any type for configuration JSON
		return operation as any;
	}

	/**
	 * Get a crawl operation by ID
	 */
	async getOperationById(id: string): Promise<CrawlOperation | undefined> {
		const [operation] = await db
			.select()
			.from(crawlOperationsTable)
			.where(eq(crawlOperationsTable.id, id));

		// biome-ignore lint/suspicious/noExplicitAny: Using any type for configuration JSON
		return operation as any;
	}

	/**
	 * Get crawl operations by website ID
	 */
	async getOperationsByWebsiteId(
		websiteId: string,
		limit = 10,
		offset = 0,
	): Promise<CrawlOperation[]> {
		const operations = await db
			.select()
			.from(crawlOperationsTable)
			.where(eq(crawlOperationsTable.websiteId, websiteId))
			.limit(limit)
			.offset(offset)
			.orderBy(desc(crawlOperationsTable.createdAt));

		// biome-ignore lint/suspicious/noExplicitAny: Using any type for configuration JSON
		return operations as any;
	}

	/**
	 * Increment the pages processed count
	 */
	async incrementPagesProcessed(id: string): Promise<void> {
		await db
			.update(crawlOperationsTable)
			.set({
				pagesProcessed: sql`pages_processed + 1`,
				updatedAt: new Date().toISOString(),
			})
			.where(eq(crawlOperationsTable.id, id));
	}

	/**
	 * Increment the pages succeeded count
	 */
	async incrementPagesSucceeded(id: string): Promise<void> {
		await db
			.update(crawlOperationsTable)
			.set({
				pagesSucceeded: sql`pages_succeeded + 1`,
				updatedAt: new Date().toISOString(),
			})
			.where(eq(crawlOperationsTable.id, id));
	}

	/**
	 * Increment the pages failed count
	 */
	async incrementPagesFailed(id: string): Promise<void> {
		await db
			.update(crawlOperationsTable)
			.set({
				pagesFailed: sql`pages_failed + 1`,
				updatedAt: new Date().toISOString(),
			})
			.where(eq(crawlOperationsTable.id, id));
	}

	/**
	 * Mark a crawl operation as running
	 */
	async markAsRunning(id: string): Promise<CrawlOperation> {
		return this.updateOperation(id, {
			status: "RUNNING",
			startedAt: new Date().toISOString(),
		});
	}

	/**
	 * Mark a crawl operation as completed
	 */
	async markAsCompleted(id: string): Promise<CrawlOperation> {
		return this.updateOperation(id, {
			status: "COMPLETED",
			completedAt: new Date().toISOString(),
		});
	}

	/**
	 * Mark a crawl operation as failed
	 */
	async markAsFailed(id: string, error: string): Promise<CrawlOperation> {
		return this.updateOperation(id, {
			status: "FAILED",
			completedAt: new Date().toISOString(),
			error,
		});
	}
}

export const crawlStatusService = new CrawlStatusService();
