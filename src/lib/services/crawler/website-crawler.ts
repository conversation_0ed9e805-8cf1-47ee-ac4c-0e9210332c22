import { env } from "@/env";
import {
	crawlOperationsRepository,
	websitePagesRepository,
	websitesRepository,
} from "@/lib/db/repositories";
import type { NewWebsitePage } from "@/lib/db/schema/websitePages";
import type { Website } from "@/lib/db/schema/websites";
import * as cheerio from "cheerio";
import * as puppeteer from "puppeteer";
import robotsParser from "robots-parser";
import { WebsiteRagService } from "../rag/website-rag-service";

export interface CrawlOptions {
	maxPages?: number;
	maxDepth?: number;
	includePatterns?: string[];
	excludePatterns?: string[];
	generateEmbeddings?: boolean;
	respectRobotsTxt?: boolean;
	crawlDelay?: number; // Delay between requests in milliseconds
	useSitemap?: boolean; // Whether to use sitemap.xml for URL discovery
	respectLlmsTxt?: boolean; // Whether to respect llms.txt directives
	extractMetadata?: boolean; // Whether to extract metadata like Open Graph tags
	parallelCrawl?: boolean; // Whether to crawl pages in parallel
	maxConcurrency?: number; // Maximum number of concurrent page crawls
	incrementalCrawl?: boolean; // Whether to only process new or changed content
	crawlOperationId?: string; // ID of the crawl operation to update
}

export class WebsiteCrawler {
	private visitedUrls: Set<string> = new Set();
	private queue: { url: string; depth: number }[] = [];
	private baseUrl: string;
	private website: Website;
	private options: CrawlOptions;
	private robotsParser: unknown = null;
	private llmsRules: { allow: string[]; disallow: string[] } | null = null;
	private sitemapUrls: string[] = [];
	private crawlOperationId?: string;

	constructor(website: Website, options: CrawlOptions = {}) {
		this.website = website;
		this.baseUrl = new URL(website.url).origin;
		this.options = {
			maxPages: options.maxPages || 100,
			maxDepth: options.maxDepth || 3,
			includePatterns: options.includePatterns || [],
			generateEmbeddings:
				options.generateEmbeddings !== undefined
					? options.generateEmbeddings
					: true,
			respectRobotsTxt:
				options.respectRobotsTxt !== undefined
					? options.respectRobotsTxt
					: true,
			crawlDelay: options.crawlDelay || 1000, // Default to 1 second delay
			useSitemap: options.useSitemap !== undefined ? options.useSitemap : true,
			respectLlmsTxt:
				options.respectLlmsTxt !== undefined ? options.respectLlmsTxt : true,
			extractMetadata:
				options.extractMetadata !== undefined ? options.extractMetadata : true,
			parallelCrawl:
				options.parallelCrawl !== undefined ? options.parallelCrawl : false,
			maxConcurrency: options.maxConcurrency || 3,
			incrementalCrawl:
				options.incrementalCrawl !== undefined
					? options.incrementalCrawl
					: true, // Default to true for incremental crawling
			excludePatterns: options.excludePatterns || [
				// Default exclusions
				"/wp-admin",
				"/wp-login",
				"/login",
				"/admin",
				"/logout",
				"/cart",
				"/checkout",
				".pdf",
				".jpg",
				".png",
				".gif",
				".zip",
				".doc",
				".xls",
			],
		};

		// Set the crawl operation ID if provided
		if (options.crawlOperationId) {
			this.crawlOperationId = options.crawlOperationId;
		}

		// Log the include and exclude patterns for debugging
		console.log(
			`Crawler configured for website: ${website.name} (${website.url})`,
		);
		console.log(
			`Include patterns: ${
				this.options.includePatterns?.length
					? this.options.includePatterns.join(", ")
					: "None (all URLs allowed)"
			}`,
		);
		console.log(
			`Exclude patterns: ${this.options.excludePatterns?.join(", ")}`,
		);
	}

	/**
	 * Fetch and parse the sitemap.xml file
	 */
	private async fetchSitemap(): Promise<void> {
		if (!this.options.useSitemap) {
			console.log("Sitemap parsing is disabled");
			return;
		}

		try {
			const sitemapUrl = `${this.baseUrl}/sitemap.xml`;
			console.log(`Fetching sitemap from: ${sitemapUrl}`);

			const response = await fetch(sitemapUrl);

			if (!response.ok) {
				console.log(
					`No sitemap found or unable to fetch (status: ${response.status})`,
				);
				return;
			}

			const sitemapXml = await response.text();

			// Use cheerio to parse the XML
			const $ = cheerio.load(sitemapXml, { xmlMode: true });

			// Extract URLs from the sitemap
			$("url > loc").each((_, element) => {
				const url = $(element).text().trim();
				if (url) {
					// Check if the URL is from the same domain
					try {
						const parsedUrl = new URL(url);
						if (parsedUrl.origin === this.baseUrl) {
							// Pre-filter URLs that match common exclude patterns
							const pathname = parsedUrl.pathname;

							// Skip RSS feeds and other common excluded files
							if (
								pathname.endsWith("/rss.xml") ||
								pathname.endsWith(".pdf") ||
								pathname.endsWith(".jpg") ||
								pathname.endsWith(".png") ||
								pathname.endsWith(".gif") ||
								pathname.endsWith(".zip") ||
								pathname.endsWith(".doc") ||
								pathname.endsWith(".xls")
							) {
								console.log(
									`Skipping sitemap URL with excluded extension: ${url}`,
								);
								return;
							}

							this.sitemapUrls.push(url);
						}
					} catch (error) {
						// Skip invalid URLs
					}
				}
			});

			console.log(`Found ${this.sitemapUrls.length} URLs in sitemap`);
		} catch (error) {
			console.error("Error fetching sitemap:", error);
		}
	}

	/**
	 * Fetch and parse the llms.txt file
	 */
	private async fetchLlmsTxt(): Promise<void> {
		if (!this.options.respectLlmsTxt) {
			console.log("llms.txt compliance is disabled");
			return;
		}

		try {
			// Try llms.txt first
			let llmsUrl = `${this.baseUrl}/llms.txt`;
			let response = await fetch(llmsUrl);

			// If not found, try llms-full.txt
			if (!response.ok) {
				llmsUrl = `${this.baseUrl}/llms-full.txt`;
				response = await fetch(llmsUrl);
			}

			if (!response.ok) {
				console.log("No llms.txt or llms-full.txt found");
				return;
			}

			const llmsTxt = await response.text();
			const lines = llmsTxt.split("\n");

			const rules = { allow: [] as string[], disallow: [] as string[] };

			for (const line of lines) {
				const trimmedLine = line.trim();
				if (!trimmedLine || trimmedLine.startsWith("#")) continue;

				if (trimmedLine.startsWith("Allow:")) {
					const path = trimmedLine.substring(6).trim();
					rules.allow.push(path);
				} else if (trimmedLine.startsWith("Disallow:")) {
					const path = trimmedLine.substring(9).trim();
					rules.disallow.push(path);
				}
			}

			this.llmsRules = rules;
			console.log(
				`Parsed llms.txt: ${rules.allow.length} allow rules, ${rules.disallow.length} disallow rules`,
			);
		} catch (error) {
			console.error("Error fetching llms.txt:", error);
		}
	}

	/**
	 * Check if a URL is allowed by llms.txt
	 */
	private isAllowedByLlmsTxt(url: string): boolean {
		if (
			!this.options.respectLlmsTxt ||
			!this.llmsRules ||
			(!this.llmsRules.allow.length && !this.llmsRules.disallow.length)
		) {
			return true;
		}

		const path = new URL(url).pathname;

		// If there are allow rules, check if the path matches any
		if (this.llmsRules.allow.length > 0) {
			return this.llmsRules.allow.some((rule) => path.includes(rule));
		}

		// If there are only disallow rules, check if the path matches any
		return !this.llmsRules.disallow.some((rule) => path.includes(rule));
	}

	/**
	 * Fetch and parse the robots.txt file
	 */
	private async fetchRobotsTxt(): Promise<void> {
		if (!this.options.respectRobotsTxt) {
			console.log("Robots.txt compliance is disabled");
			return;
		}

		try {
			const robotsUrl = `${this.baseUrl}/robots.txt`;
			console.log(`Fetching robots.txt from: ${robotsUrl}`);

			const response = await fetch(robotsUrl);

			if (!response.ok) {
				console.log(
					`No robots.txt found or unable to fetch (status: ${response.status})`,
				);
				return;
			}

			const robotsTxt = await response.text();
			this.robotsParser = robotsParser(robotsUrl, robotsTxt);

			console.log("Robots.txt parsed successfully");
		} catch (error) {
			console.error("Error fetching robots.txt:", error);
			// Continue without robots.txt
		}
	}

	/**
	 * Check if a URL is allowed by robots.txt
	 */
	private isAllowedByRobotsTxt(url: string): boolean {
		if (!this.options.respectRobotsTxt || !this.robotsParser) {
			return true;
		}

		try {
			// Use a type assertion to access the isAllowed method
			// biome-ignore lint/suspicious/noExplicitAny: Using any type to access dynamic property
			const parser = this.robotsParser as any;
			if (parser && typeof parser.isAllowed === "function") {
				const allowed = parser.isAllowed(url, "WebChatAI-Crawler");
				if (!allowed) {
					console.log(`URL disallowed by robots.txt: ${url}`);
				}
				return allowed;
			}
			// If the method doesn't exist, allow the URL
			console.log(
				`Robots parser doesn't have isAllowed method, allowing URL: ${url}`,
			);
			return true;
		} catch (error) {
			console.error(`Error checking robots.txt for URL ${url}:`, error);
			// In case of error, allow the URL
			return true;
		}
	}

	/**
	 * Start crawling the website
	 */
	async crawl(): Promise<void> {
		console.log(
			`Starting crawl for website: ${this.website.name} (${this.website.url})`,
		);

		// Create a new crawl operation if one wasn't provided
		if (!this.crawlOperationId) {
			const operation = await crawlOperationsRepository.create({
				websiteId: this.website.id,
				status: "PENDING",
				// biome-ignore lint/suspicious/noExplicitAny: Using any type for configuration JSON
				configuration: this.options as any,
			});

			this.crawlOperationId = operation.id;
		}

		// Mark the operation as running
		await crawlOperationsRepository.update(this.crawlOperationId, {
			status: "RUNNING",
			startedAt: new Date().toISOString(),
		});

		// Fetch and parse robots.txt
		await this.fetchRobotsTxt();

		// Fetch and parse sitemap.xml if enabled
		if (this.options.useSitemap) {
			await this.fetchSitemap();

			// Add sitemap URLs to the queue (respecting include/exclude patterns)
			for (const url of this.sitemapUrls) {
				if (!this.visitedUrls.has(url) && this.shouldCrawlUrl(url)) {
					console.log(`Adding sitemap URL to queue: ${url}`);
					this.queue.push({ url, depth: 0 });
				}
			}
		}

		// Fetch and parse llms.txt if enabled
		if (this.options.respectLlmsTxt) {
			await this.fetchLlmsTxt();
		}

		// Add the starting URL to the queue (respecting include/exclude patterns)
		const startUrl = this.website.url;
		if (this.shouldCrawlUrl(startUrl)) {
			console.log(`Adding starting URL to queue: ${startUrl}`);
			this.queue.push({ url: startUrl, depth: 0 });
		} else {
			console.warn(
				`Starting URL ${startUrl} doesn't match include/exclude patterns. This may result in no pages being crawled.`,
			);
			// Still add it to the queue as a fallback to ensure at least the homepage is crawled
			this.queue.push({ url: startUrl, depth: 0 });
		}

		// Launch a headless browser
		// Check if we should use Browserless
		const useBrowserless =
			env.USE_BROWSERLESS === "true" && env.BROWSERLESS_URL;

		console.log(
			`Crawler configuration: Using Browserless: ${
				useBrowserless ? "Yes" : "No"
			}`,
		);

		let browser: puppeteer.Browser;

		if (useBrowserless) {
			// Connect to Browserless service
			console.log(`Connecting to Browserless at: ${env.BROWSERLESS_URL}`);

			const browserlessUrl = env.BROWSERLESS_API_KEY
				? `${env.BROWSERLESS_URL}?token=${env.BROWSERLESS_API_KEY}`
				: env.BROWSERLESS_URL;

			try {
				browser = await puppeteer.connect({
					browserWSEndpoint: browserlessUrl,
				});
				console.log("Successfully connected to Browserless");
			} catch (error) {
				console.error("Failed to connect to Browserless:", error);

				// Mark the operation as failed
				if (this.crawlOperationId) {
					await crawlOperationsRepository.update(this.crawlOperationId, {
						status: "FAILED",
						completedAt: new Date().toISOString(),
						error: error instanceof Error ? error.message : String(error),
					});
				}

				throw new Error("Failed to connect to Browserless service");
			}
		} else {
			// Use local browser with enhanced configuration for stability
			browser = await puppeteer.launch({
				headless: true,
				args: [
					"--no-sandbox",
					"--disable-setuid-sandbox",
					"--disable-dev-shm-usage",
					"--disable-accelerated-2d-canvas",
					"--disable-gpu",
					"--disable-extensions",
					"--disable-component-extensions-with-background-pages",
					"--disable-default-apps",
					"--mute-audio",
					"--no-default-browser-check",
					"--no-first-run",
					"--disable-background-timer-throttling",
					"--disable-backgrounding-occluded-windows",
					"--disable-renderer-backgrounding",
					"--disable-background-networking",
					"--disable-breakpad",
					"--disable-sync",
					"--disable-translate",
					"--metrics-recording-only",
					"--disable-hang-monitor",
					"--disable-features=site-per-process,TranslateUI",
					"--disable-ipc-flooding-protection",
					"--enable-features=NetworkService,NetworkServiceInProcess",
				],
				ignoreDefaultArgs: false,
				timeout: 60000, // 60 second timeout
			});
		}

		try {
			// Update the website's lastCrawledAt timestamp and status
			await websitesRepository.update(this.website.id, {
				lastCrawledAt: new Date().toISOString(),
				status: "CRAWLING",
			});

			// Process the queue until it's empty or we've reached the maximum number of pages
			if (this.options.parallelCrawl) {
				// Process pages in parallel
				while (
					this.queue.length > 0 &&
					this.visitedUrls.size < (this.options.maxPages ?? 100)
				) {
					// Create a batch of URLs to process in parallel
					const batch = [];
					const batchSize = Math.min(
						this.options.maxConcurrency || 3,
						this.queue.length,
					);

					for (let i = 0; i < batchSize; i++) {
						const queueItem = this.queue.shift();
						if (!queueItem) continue;
						const { url, depth } = queueItem;

						// Skip if already visited or beyond max depth
						if (
							this.visitedUrls.has(url) ||
							depth > (this.options.maxDepth ?? 3)
						) {
							i--; // Don't count this as part of the batch
							continue;
						}

						// Check if the URL matches include/exclude patterns
						if (!this.shouldCrawlUrl(url)) {
							console.log(`Skipping URL that doesn't match patterns: ${url}`);
							i--; // Don't count this as part of the batch
							continue;
						}

						// Mark as visited
						this.visitedUrls.add(url);

						// Check if allowed by robots.txt and llms.txt
						if (
							!this.isAllowedByRobotsTxt(url) ||
							(this.options.respectLlmsTxt && !this.isAllowedByLlmsTxt(url))
						) {
							i--; // Don't count this as part of the batch
							continue;
						}

						batch.push({ url, depth });
					}

					if (batch.length > 0) {
						console.log(
							`Processing batch of ${batch.length} pages in parallel`,
						);

						// Process the batch in parallel
						await Promise.all(
							batch.map((item) =>
								this.processPage(browser, item.url, item.depth).catch((error) =>
									console.error(`Error processing page ${item.url}:`, error),
								),
							),
						);

						// Add a delay between batches
						if (this.options.crawlDelay && this.options.crawlDelay > 0) {
							console.log(
								`Waiting for ${this.options.crawlDelay}ms before next batch`,
							);
							await new Promise((resolve) =>
								setTimeout(resolve, this.options.crawlDelay),
							);
						}
					}
				}
			} else {
				// Process pages sequentially (original implementation)
				while (
					this.queue.length > 0 &&
					this.visitedUrls.size < (this.options.maxPages ?? 100)
				) {
					const queueItem = this.queue.shift();
					if (!queueItem) continue;
					const { url, depth } = queueItem;

					// Skip if we've already visited this URL or if it's beyond our max depth
					if (
						this.visitedUrls.has(url) ||
						depth > (this.options.maxDepth ?? 3)
					) {
						continue;
					}

					// Check if the URL matches include/exclude patterns
					if (!this.shouldCrawlUrl(url)) {
						console.log(`Skipping URL that doesn't match patterns: ${url}`);
						continue;
					}

					// Mark as visited
					this.visitedUrls.add(url);

					try {
						// Check if the URL is allowed by robots.txt
						if (!this.isAllowedByRobotsTxt(url)) {
							console.log(`Skipping URL disallowed by robots.txt: ${url}`);
							continue;
						}

						// Check if the URL is allowed by llms.txt
						if (this.options.respectLlmsTxt && !this.isAllowedByLlmsTxt(url)) {
							console.log(`Skipping URL disallowed by llms.txt: ${url}`);
							continue;
						}

						// Process the page
						await this.processPage(browser, url, depth);

						// Add a delay between requests to avoid overloading the server
						if (this.options.crawlDelay && this.options.crawlDelay > 0) {
							console.log(
								`Waiting for ${this.options.crawlDelay}ms before next request`,
							);
							await new Promise((resolve) =>
								setTimeout(resolve, this.options.crawlDelay),
							);
						}
					} catch (error) {
						console.error(`Error processing page ${url}:`, error);
					}
				}
			}

			// Generate embeddings if requested
			if (this.options.generateEmbeddings) {
				console.log(`Generating embeddings for website: ${this.website.name}`);

				try {
					// Create a RAG service instance
					const ragService = new WebsiteRagService();

					// Process the website
					// biome-ignore lint/suspicious/noExplicitAny: Using any type to match Website type from schema
					await ragService.processWebsite(this.website as any);

					console.log(
						`Embeddings generated successfully for website: ${this.website.name}`,
					);
				} catch (error) {
					console.error(
						`Error generating embeddings for website ${this.website.name}:`,
						error,
					);
				}
			}

			// Update the website's status to ACTIVE
			await websitesRepository.update(this.website.id, {
				status: "ACTIVE",
			});

			// Mark the crawl operation as completed
			if (this.crawlOperationId) {
				await crawlOperationsRepository.update(this.crawlOperationId, {
					status: "COMPLETED",
					completedAt: new Date().toISOString(),
					pagesProcessed: this.visitedUrls.size,
					pagesSucceeded: this.visitedUrls.size,
				});
			}

			console.log(
				`Crawl completed for website: ${this.website.name}. Processed ${this.visitedUrls.size} pages.`,
			);
		} catch (error) {
			console.error(`Crawl failed for website: ${this.website.name}:`, error);

			// Update the website's status to ERROR
			await websitesRepository.update(this.website.id, {
				status: "ERROR",
			});

			// Mark the crawl operation as failed
			if (this.crawlOperationId) {
				await crawlOperationsRepository.update(this.crawlOperationId, {
					status: "FAILED",
					completedAt: new Date().toISOString(),
					error: error instanceof Error ? error.message : String(error),
				});
			}
		} finally {
			// Close the browser
			await browser.close();
		}
	}

	/**
	 * Check if a page has been modified since the last crawl
	 * Uses both Last-Modified and ETag headers for more accurate change detection
	 */
	private async hasPageChanged(
		url: string,
		lastCrawledAt: string | null,
	): Promise<boolean> {
		if (!lastCrawledAt || !this.options.incrementalCrawl) return true;

		try {
			// Store ETag values for each URL to compare in future crawls
			const existingPage = await websitePagesRepository.getByUrl(
				this.website.id,
				url,
			);

			// Get the stored ETag from the page metadata if it exists
			const metadata = existingPage?.metadata as
				| Record<string, unknown>
				| undefined;
			const storedETag = metadata?.etag as string | undefined;

			// Make a conditional request with If-None-Match and If-Modified-Since headers
			const headers = new Headers();
			if (storedETag) {
				headers.append("If-None-Match", storedETag);
			}
			if (lastCrawledAt) {
				headers.append(
					"If-Modified-Since",
					new Date(lastCrawledAt).toUTCString(),
				);
			}

			const response = await fetch(url, {
				method: "HEAD",
				headers,
			});

			// If the server returns 304 Not Modified, the page hasn't changed
			if (response.status === 304) {
				console.log(`Server confirmed page not modified: ${url}`);
				return false;
			}

			// Check ETag if available
			const currentETag = response.headers.get("etag");
			if (currentETag && storedETag && currentETag === storedETag) {
				console.log(`ETag unchanged for ${url}: ${currentETag}`);
				return false;
			}

			// Check Last-Modified header
			const lastModified = response.headers.get("last-modified");
			if (lastModified && lastCrawledAt) {
				const lastModifiedDate = new Date(lastModified);
				const lastCrawledDate = new Date(lastCrawledAt);

				if (lastModifiedDate <= lastCrawledDate) {
					console.log(`Last-Modified date indicates no change for ${url}`);
					return false;
				}
			}

			// If we get here, either the page has changed or we couldn't determine
			// In either case, we should process the page to be safe
			return true;
		} catch (error) {
			console.error(`Error checking if page has changed: ${url}`, error);
			return true; // Assume changed on error
		}
	}

	/**
	 * Check if browser is connected and attempt to reconnect if not
	 * @returns A connected browser instance
	 */
	private async ensureBrowserConnected(
		browser: puppeteer.Browser,
	): Promise<puppeteer.Browser> {
		// biome-ignore lint/suspicious/noExplicitAny: Handling browser connection check
		if ((browser as any).connected) {
			return browser;
		}

		console.log("Browser disconnected, attempting to reconnect...");

		// Maximum number of reconnection attempts
		const MAX_RECONNECT_ATTEMPTS = 3;
		let reconnectAttempt = 0;
		let reconnectedBrowser: puppeteer.Browser | null = null;

		while (reconnectAttempt < MAX_RECONNECT_ATTEMPTS) {
			reconnectAttempt++;
			try {
				// Check if we should use Browserless
				const useBrowserless =
					env.USE_BROWSERLESS === "true" && env.BROWSERLESS_URL;

				if (useBrowserless) {
					// Connect to Browserless service
					console.log(
						`Reconnecting to Browserless (attempt ${reconnectAttempt}/${MAX_RECONNECT_ATTEMPTS})...`,
					);

					const browserlessUrl = env.BROWSERLESS_API_KEY
						? `${env.BROWSERLESS_URL}?token=${env.BROWSERLESS_API_KEY}`
						: env.BROWSERLESS_URL;

					reconnectedBrowser = await puppeteer.connect({
						browserWSEndpoint: browserlessUrl,
					});
				} else {
					// Launch a new local browser
					console.log(
						`Launching new local browser (attempt ${reconnectAttempt}/${MAX_RECONNECT_ATTEMPTS})...`,
					);
					reconnectedBrowser = await puppeteer.launch({
						headless: true,
						args: [
							"--no-sandbox",
							"--disable-setuid-sandbox",
							"--disable-dev-shm-usage",
							"--disable-accelerated-2d-canvas",
							"--disable-gpu",
							"--disable-extensions",
							"--disable-component-extensions-with-background-pages",
							"--disable-default-apps",
							"--mute-audio",
							"--no-default-browser-check",
							"--no-first-run",
							"--disable-background-timer-throttling",
							"--disable-backgrounding-occluded-windows",
							"--disable-renderer-backgrounding",
							"--disable-background-networking",
							"--disable-breakpad",
							"--disable-sync",
							"--disable-translate",
							"--metrics-recording-only",
							"--disable-hang-monitor",
							"--disable-features=site-per-process,TranslateUI",
							"--disable-ipc-flooding-protection",
							"--enable-features=NetworkService,NetworkServiceInProcess",
						],
						ignoreDefaultArgs: false,
						timeout: 60000, // 60 second timeout
					});
				}

				// Check if the new browser is connected
				// biome-ignore lint/suspicious/noExplicitAny: Handling browser connection check
				if ((reconnectedBrowser as any).connected) {
					console.log("Browser reconnected successfully");
					return reconnectedBrowser;
				}
			} catch (error) {
				console.error(
					`Failed to reconnect browser (attempt ${reconnectAttempt}/${MAX_RECONNECT_ATTEMPTS}):`,
					error,
				);

				// Exponential backoff before retrying
				const backoffTime = Math.min(1000 * 2 ** reconnectAttempt, 10000);
				console.log(
					`Waiting ${backoffTime}ms before next reconnection attempt...`,
				);
				await new Promise((resolve) => setTimeout(resolve, backoffTime));
			}
		}

		// If we couldn't reconnect after multiple attempts, throw an error
		throw new Error(
			`Failed to reconnect browser after ${MAX_RECONNECT_ATTEMPTS} attempts`,
		);
	}

	/**
	 * Process a single page with retry logic and browser reconnection
	 */
	private async processPage(
		browser: puppeteer.Browser,
		url: string,
		depth: number,
		retryCount = 0,
	): Promise<void> {
		console.log(`Processing page: ${url} (depth: ${depth})`);

		// Maximum number of retries
		const MAX_RETRIES = 3;

		// Increment the pages processed count
		if (this.crawlOperationId) {
			await crawlOperationsRepository.incrementField(
				this.crawlOperationId,
				"pagesProcessed",
			);
		}

		// Check if the page already exists
		const existingPage = await websitePagesRepository.getByUrl(
			this.website.id,
			url,
		);

		// Check if incremental crawling is enabled and if the page has changed
		if (this.options.incrementalCrawl && existingPage) {
			const hasChanged = await this.hasPageChanged(
				url,
				existingPage.lastCrawledAt,
			);

			if (!hasChanged) {
				console.log(`Skipping unchanged page: ${url}`);

				// Increment the pages succeeded count even though we're skipping
				if (this.crawlOperationId) {
					await crawlOperationsRepository.incrementField(
						this.crawlOperationId,
						"pagesSucceeded",
					);
				}

				return;
			}

			console.log(`Page has changed since last crawl: ${url}`);
		}

		// Variable to track if we need to close the page
		let page: puppeteer.Page | null = null;
		let connectedBrowser = browser;

		try {
			// Ensure browser is connected before proceeding
			try {
				connectedBrowser = await this.ensureBrowserConnected(browser);
			} catch (reconnectError) {
				console.error("Failed to ensure browser connection:", reconnectError);
				throw new Error(
					`Cannot process page ${url}: Browser reconnection failed`,
				);
			}

			// Open a new page
			page = await connectedBrowser.newPage();

			// Configure page settings
			page.setDefaultNavigationTimeout(60000); // Increase timeout to 60 seconds

			// Set user agent to avoid being blocked
			await page.setUserAgent(
				"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			);

			// Block unnecessary resource types to improve performance
			await page.setRequestInterception(true);
			page.on("request", (request) => {
				const resourceType = request.resourceType();
				if (
					resourceType === "image" ||
					resourceType === "media" ||
					resourceType === "font"
				) {
					request.abort();
				} else {
					request.continue();
				}
			});

			// Add error handler for page errors
			page.on("error", (err) => {
				console.error(`Page error on ${url}:`, err);
			});

			// Navigate to the URL with enhanced retry logic for navigation
			let content = "";
			try {
				const response = await page.goto(url, {
					waitUntil: "networkidle2",
					timeout: 60000, // 60 second timeout
				});

				// Check if the response was successful
				if (!response) {
					console.warn(`No response received for ${url}, continuing anyway`);
				} else if (!response.ok()) {
					console.warn(
						`Received HTTP ${response.status()} for ${url}, continuing anyway`,
					);
				}

				content = await page.content();
			} catch (navError) {
				console.warn(
					`Navigation error for ${url}, trying with domcontentloaded: ${navError}`,
				);
				// Try with a less strict wait condition
				try {
					await page.goto(url, {
						waitUntil: "domcontentloaded",
						timeout: 60000,
					});
					content = await page.content();
				} catch (domError) {
					console.warn(
						`domcontentloaded also failed for ${url}, trying with load: ${domError}`,
					);
					// Try with an even less strict wait condition
					await page.goto(url, {
						waitUntil: "load",
						timeout: 60000,
					});
					content = await page.content();
				}
			}

			// Parse the HTML
			const $ = cheerio.load(content);

			// Extract the title
			const title = $("title").text().trim() || url;

			// Extract the main content with our improved algorithm
			const mainContent = this.extractMainContent($);

			// Extract Open Graph metadata
			const metadata = this.extractOpenGraphMetadata($);

			// Get the ETag header if available and store it in metadata
			const etag = page.url().startsWith("http")
				? await page.evaluate(() => {
						return (
							document
								.querySelector('meta[http-equiv="ETag"]')
								?.getAttribute("content") || null
						);
					})
				: null;

			if (etag) {
				metadata.etag = etag;
			}

			// Store the page in the database
			const websitePage: NewWebsitePage = {
				websiteId: this.website.id,
				url,
				title,
				content: mainContent,
				// biome-ignore lint/suspicious/noExplicitAny: Using any type for metadata JSON
				metadata: metadata as any,
				lastCrawledAt: new Date().toISOString(),
			};

			if (existingPage) {
				// Update the existing page
				await websitePagesRepository.update(existingPage.id, websitePage);
			} else {
				// Create a new page
				await websitePagesRepository.create(websitePage);
			}

			// If we're not at the maximum depth, extract links and add them to the queue
			if (depth < (this.options.maxDepth ?? 3)) {
				const links = this.extractLinks($);

				for (const link of links) {
					// Skip if we've already visited this URL or if it's in the queue
					if (this.visitedUrls.has(link)) {
						continue;
					}

					// Add to the queue
					this.queue.push({ url: link, depth: depth + 1 });
				}
			}

			// Increment the pages succeeded count
			if (this.crawlOperationId) {
				await crawlOperationsRepository.incrementField(
					this.crawlOperationId,
					"pagesSucceeded",
				);
			}
		} catch (error) {
			console.error(`Error processing page ${url}:`, error);

			// Check if we should retry
			const errorMessage =
				error instanceof Error ? error.message : String(error);

			// Enhanced error detection for browser/connection issues
			const isBrowserError =
				errorMessage.includes("Protocol error") ||
				errorMessage.includes("Connection closed") ||
				errorMessage.includes("Browser disconnected") ||
				errorMessage.includes("Navigation timeout") ||
				errorMessage.includes("Target closed") ||
				errorMessage.includes("Session closed") ||
				errorMessage.includes("WebSocket") ||
				errorMessage.includes("net::") ||
				errorMessage.includes("Cannot find context") ||
				errorMessage.includes(
					"Target page, context or browser has been closed",
				);

			if (retryCount < MAX_RETRIES && isBrowserError) {
				// Calculate exponential backoff time
				const backoffTime = Math.min(1000 * 2 ** retryCount, 10000);

				console.log(
					`Retrying page ${url} (attempt ${
						retryCount + 1
					} of ${MAX_RETRIES}) after ${backoffTime}ms backoff`,
				);

				// Close the page if it exists
				if (page) {
					try {
						await page.close();
					} catch (closeError) {
						console.warn(`Error closing page during retry: ${closeError}`);
					}
				}

				// Wait with exponential backoff before retrying
				await new Promise((resolve) => setTimeout(resolve, backoffTime));

				// Retry processing the page with the potentially reconnected browser
				return this.processPage(connectedBrowser, url, depth, retryCount + 1);
			}

			// Increment the pages failed count if we're not retrying
			if (this.crawlOperationId) {
				await crawlOperationsRepository.incrementField(
					this.crawlOperationId,
					"pagesFailed",
				);
			}
		} finally {
			// Close the page if it exists
			if (page) {
				try {
					await page.close();
				} catch (closeError) {
					console.warn(`Error closing page: ${closeError}`);
				}
			}
		}
	}

	/**
	 * Extract Open Graph metadata from a page
	 */
	private extractOpenGraphMetadata(
		$: cheerio.CheerioAPI,
	): Record<string, string> {
		if (!this.options.extractMetadata) {
			return {};
		}

		const metadata: Record<string, string> = {};

		// Extract Open Graph metadata
		$('meta[property^="og:"]').each((_, element) => {
			const property = $(element).attr("property");
			const content = $(element).attr("content");

			if (property && content) {
				metadata[property.replace("og:", "")] = content;
			}
		});

		// Extract Twitter card metadata
		$('meta[name^="twitter:"]').each((_, element) => {
			const name = $(element).attr("name");
			const content = $(element).attr("content");

			if (name && content) {
				metadata[name.replace("twitter:", "twitter_")] = content;
			}
		});

		// Extract basic metadata
		const title = $("title").text();
		const description = $('meta[name="description"]').attr("content");

		if (title && !metadata.title) {
			metadata.title = title;
		}

		if (description && !metadata.description) {
			metadata.description = description;
		}

		return metadata;
	}

	/**
	 * Extract the main content from a page
	 */
	private extractMainContent($: cheerio.CheerioAPI): string {
		// Remove script, style, and other non-content elements
		$("script, style, nav, footer, header, aside, iframe, noscript").remove();

		// Try to find the main content container
		// Common content selectors in order of priority
		const contentSelectors = [
			"article",
			"main",
			".content",
			"#content",
			".post-content",
			".entry-content",
			".article-content",
			".main-content",
			"[role='main']",
			"section",
		];

		// Score elements based on content density
		// biome-ignore lint/suspicious/noExplicitAny: Using any type for cheerio element
		const scoredElements: Array<{ element: any; score: number }> = [];

		$("body *").each((_, element) => {
			const $element = $(element);
			const text = $element.text().trim();
			const textLength = text.length;

			if (textLength < 100) return; // Skip elements with little text

			// Calculate text-to-HTML ratio as a simple content density metric
			const html = $.html(element);
			const htmlLength = html.length;
			const textDensity = textLength / htmlLength;

			// Calculate link density (lower is better for main content)
			const links = $element.find("a");
			const linkText = links.text().length;
			const linkDensity = linkText / textLength || 0;

			// Calculate heading density
			const headings = $element.find("h1, h2, h3, h4, h5, h6");
			const headingDensity = headings.length / (textLength / 500) || 0;

			// Calculate paragraph density
			const paragraphs = $element.find("p");
			const paragraphDensity = paragraphs.length / (textLength / 500) || 0;

			// Calculate final score
			const score =
				textDensity * 0.3 +
				textLength * 0.3 +
				paragraphDensity * 0.2 +
				headingDensity * 0.1 -
				linkDensity * 0.1;

			scoredElements.push({ element, score });
		});

		// Sort by score (highest first)
		scoredElements.sort((a, b) => b.score - a.score);

		// If we have scored elements, use the highest scoring one
		if (scoredElements.length > 0) {
			return $(scoredElements[0].element).text().trim();
		}

		let mainContent = "";

		// Try each selector until we find content
		for (const selector of contentSelectors) {
			const content = $(selector).text().trim();
			if (content.length > 200) {
				// Minimum content length threshold
				mainContent = content;
				break;
			}
		}

		// If no main content container found, use the body
		if (!mainContent) {
			// Remove common non-content elements
			$(
				".sidebar, .comments, .related, .advertisement, .ads, .nav, .menu, .social",
			).remove();
			mainContent = $("body").text().trim();
		}

		// Clean up the content
		return this.cleanContent(mainContent);
	}

	/**
	 * Clean up the content
	 */
	private cleanContent(content: string): string {
		// Remove extra whitespace
		let cleaned = content.replace(/\s+/g, " ").trim();

		// Remove common boilerplate text
		const boilerplatePatterns = [
			/cookie policy/gi,
			/privacy policy/gi,
			/terms of service/gi,
			/all rights reserved/gi,
			/copyright \d{4}/gi,
		];

		for (const pattern of boilerplatePatterns) {
			cleaned = cleaned.replace(pattern, "");
		}

		// Remove duplicate spaces after cleaning
		return cleaned.replace(/\s+/g, " ").trim();
	}

	/**
	 * Check if a URL should be crawled based on include/exclude patterns
	 */
	private shouldCrawlUrl(url: string): boolean {
		try {
			const parsedUrl = new URL(url);
			const pathname = parsedUrl.pathname;

			// Skip if the URL is not from the same domain
			if (parsedUrl.origin !== this.baseUrl) {
				return false;
			}

			// Log the URL and patterns for debugging
			console.log(`Checking URL: ${url}`);
			console.log(
				`Exclude patterns: ${JSON.stringify(this.options.excludePatterns)}`,
			);
			console.log(
				`Include patterns: ${JSON.stringify(this.options.includePatterns)}`,
			);

			// Check exclude patterns first - if any match, exclude the URL
			if (this.options.excludePatterns?.length) {
				for (const pattern of this.options.excludePatterns) {
					// Handle different pattern types
					if (pattern === "rss.xml" || pathname.endsWith("/rss.xml")) {
						// Special case for rss.xml which seems to be problematic
						console.log(`URL ${url} excluded - matches rss.xml pattern`);
						return false;
					}

					// File extension match (e.g., ".pdf")
					if (pattern.startsWith(".") && pathname.endsWith(pattern)) {
						console.log(
							`URL ${url} excluded - matches file extension: ${pattern}`,
						);
						return false;
					}

					// Exact path match
					if (pathname === pattern) {
						console.log(`URL ${url} excluded - exact path match: ${pattern}`);
						return false;
					}

					// Create a regex that matches the pattern at word boundaries
					// This ensures we match complete path segments or filenames
					const regex = new RegExp(
						`(^|/|\\.)${pattern.replace(/\./g, "\\.")}($|/|\\?)`,
						"i",
					);
					if (regex.test(pathname)) {
						console.log(`URL ${url} excluded - regex match: ${pattern}`);
						return false;
					}
				}
			}

			// If we have include patterns, check if the URL matches any
			const includePatterns = this.options.includePatterns || [];
			if (includePatterns.length > 0) {
				let isIncluded = false;

				for (const pattern of includePatterns) {
					// Exact path match
					if (pathname === pattern) {
						console.log(`URL ${url} included - exact path match: ${pattern}`);
						isIncluded = true;
						break;
					}

					// Path prefix match
					if (pathname.startsWith(pattern)) {
						console.log(`URL ${url} included - path prefix match: ${pattern}`);
						isIncluded = true;
						break;
					}

					// Create a regex that matches the pattern
					const regex = new RegExp(pattern.replace(/\./g, "\\."), "i");
					if (regex.test(pathname)) {
						console.log(`URL ${url} included - regex match: ${pattern}`);
						isIncluded = true;
						break;
					}
				}

				if (!isIncluded) {
					console.log(
						`URL ${url} skipped - doesn't match any include patterns`,
					);
					return false;
				}
			}

			console.log(`URL ${url} allowed for crawling`);
			return true;
		} catch (error) {
			console.error(`Error checking URL patterns for ${url}:`, error);
			return false;
		}
	}

	/**
	 * Extract links from a page
	 */
	private extractLinks($: cheerio.CheerioAPI): string[] {
		const links: string[] = [];

		// Find all links
		$("a").each((_, element) => {
			const href = $(element).attr("href");

			if (!href) {
				return;
			}

			try {
				// Resolve the URL
				const resolvedUrl = new URL(href, this.baseUrl).href;

				// Check if the URL should be crawled based on patterns
				if (!this.shouldCrawlUrl(resolvedUrl)) {
					return;
				}

				// Remove hash and query parameters
				const url = new URL(resolvedUrl);
				const pathname = url.pathname;

				// Additional check for common excluded files
				if (
					pathname.endsWith("/rss.xml") ||
					pathname.includes("rss.xml") ||
					pathname.endsWith(".pdf") ||
					pathname.endsWith(".jpg") ||
					pathname.endsWith(".png") ||
					pathname.endsWith(".gif") ||
					pathname.endsWith(".zip") ||
					pathname.endsWith(".doc") ||
					pathname.endsWith(".xls")
				) {
					console.log(`Skipping URL with excluded extension: ${resolvedUrl}`);
					return;
				}

				const cleanUrl = `${url.origin}${pathname}`;

				// Add to the list of links
				links.push(cleanUrl);
			} catch (error) {
				// Skip invalid URLs
			}
		});

		return links;
	}
}
