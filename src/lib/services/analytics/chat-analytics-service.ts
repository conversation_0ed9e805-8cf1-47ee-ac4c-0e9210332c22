import { db } from "@/lib/db";
import { sql } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

export interface ChatAnalyticsEvent {
	id?: string;
	websiteId: string;
	visitorId: string;
	conversationId: string;
	eventType:
		| "message_sent"
		| "message_received"
		| "conversation_started"
		| "error";
	metadata?: Record<string, unknown>;
	timestamp?: string;
}

export interface ChatPerformanceMetrics {
	responseTime: number; // in milliseconds
	tokensUsed?: number;
	ragUsed: boolean;
	ragResultCount?: number;
}

export class ChatAnalyticsService {
	/**
	 * Track a chat event
	 */
	async trackEvent(event: ChatAnalyticsEvent): Promise<void> {
		try {
			// Generate an ID if not provided
			const id = event.id || uuidv4();

			// Set timestamp if not provided
			const timestamp = event.timestamp || new Date().toISOString();

			// Insert the event into the database
			await db.execute(sql`
        INSERT INTO chat_analytics_events (
          id,
          website_id,
          visitor_id,
          conversation_id,
          event_type,
          metadata,
          timestamp
        ) VALUES (
          ${id},
          ${event.websiteId},
          ${event.visitorId},
          ${event.conversationId},
          ${event.eventType},
          ${JSON.stringify(event.metadata ?? {})},
          ${timestamp}
        )
      `);
		} catch (error) {
			console.error("Error tracking chat event:", error);
			// Don't throw, just log the error
		}
	}

	/**
	 * Track chat performance metrics
	 */
	async trackPerformance(
		websiteId: string,
		conversationId: string,
		metrics: ChatPerformanceMetrics,
	): Promise<void> {
		try {
			const id = uuidv4();
			const timestamp = new Date().toISOString();

			// Insert the performance metrics into the database
			await db.execute(sql`
        INSERT INTO chat_performance_metrics (
          id,
          website_id,
          conversation_id,
          response_time,
          tokens_used,
          rag_used,
          rag_result_count,
          timestamp
        ) VALUES (
          ${id},
          ${websiteId},
          ${conversationId},
          ${metrics.responseTime},
          ${metrics.tokensUsed || 0},
          ${metrics.ragUsed},
          ${metrics.ragResultCount || 0},
          ${timestamp}
        )
      `);
		} catch (error) {
			console.error("Error tracking chat performance:", error);
			// Don't throw, just log the error
		}
	}

	/**
	 * Get chat usage statistics for a website
	 */
	async getWebsiteStats(websiteId: string): Promise<{
		totalConversations: number;
		totalMessages: number;
		averageResponseTime: number;
		ragUsagePercentage: number;
	}> {
		try {
			// Get total conversations
			const { rows: conversationRows } = await db.execute(sql`
        SELECT COUNT(DISTINCT conversation_id) as total
        FROM chat_analytics_events
        WHERE website_id = ${websiteId}
        AND event_type = 'conversation_started'
      `);

			const getNumber = (val: unknown) => {
				if (typeof val === "number") return val;
				if (typeof val === "string") return Number.parseInt(val, 10);
				return 0;
			};
			const totalConversations = getNumber(conversationRows[0]?.total);

			// Get total messages
			const { rows: messageRows } = await db.execute(sql`
        SELECT COUNT(*) as total
        FROM chat_analytics_events
        WHERE website_id = ${websiteId}
        AND event_type IN ('message_sent', 'message_received')
      `);

			const totalMessages = getNumber(messageRows[0]?.total);

			// Get average response time
			const { rows: responseTimeRows } = await db.execute(sql`
        SELECT AVG(response_time) as average
        FROM chat_performance_metrics
        WHERE website_id = ${websiteId}
      `);

			const getFloat = (val: unknown) => {
				if (typeof val === "number") return val;
				if (typeof val === "string") return Number.parseFloat(val);
				return 0;
			};
			const averageResponseTime = getFloat(responseTimeRows[0]?.average);

			// Get RAG usage percentage
			const { rows: ragRows } = await db.execute(sql`
        SELECT 
          COUNT(*) as total,
          SUM(CASE WHEN rag_used = true THEN 1 ELSE 0 END) as rag_used
        FROM chat_performance_metrics
        WHERE website_id = ${websiteId}
      `);

			const total = getNumber(ragRows[0]?.total);
			const ragUsed = getNumber(ragRows[0]?.rag_used);
			const ragUsagePercentage = total > 0 ? (ragUsed / total) * 100 : 0;

			return {
				totalConversations,
				totalMessages,
				averageResponseTime,
				ragUsagePercentage,
			};
		} catch (error) {
			console.error("Error getting website stats:", error);
			return {
				totalConversations: 0,
				totalMessages: 0,
				averageResponseTime: 0,
				ragUsagePercentage: 0,
			};
		}
	}
}
