import { db } from "@/lib/db";
import {
	websitePagesRepository,
	websitesRepository,
} from "@/lib/db/repositories";
import type { WebsitePage } from "@/lib/db/schema/websitePages";
import type { Website } from "@/lib/db/schema/websites";
import { mistral } from "@ai-sdk/mistral";
import { PgVector } from "@mastra/pg";
import { MDocument, rerank } from "@mastra/rag";
import { embed, embedMany } from "ai";
import { sql } from "drizzle-orm";
import { chunkWithOptimalStrategy } from "./chunking-strategies";
import { RagMetricsService } from "./rag-metrics";
import { rebuildVectorIndex, shouldRebuildIndex } from "./rebuild-index";
import { VectorCache } from "./vector-cache";

export interface WebsiteRagOptions {
	chunkSize?: number;
	chunkOverlap?: number;
	embeddingModel?: string;
}

export class WebsiteRagService {
	private pgVector: PgVector;
	private options: WebsiteRagOptions;
	private indexName = "website_embeddings";
	private cache: VectorCache<
		Array<{
			text: string;
			pageId: string;
			url: string;
			title: string;
			similarity: number;
		}>
	>;
	private metricsService: RagMetricsService;

	constructor(options: WebsiteRagOptions = {}) {
		this.options = {
			chunkSize: options.chunkSize || 512,
			chunkOverlap: options.chunkOverlap || 50,
			embeddingModel: options.embeddingModel || "mistral-embed",
		};

		// Initialize PgVector with the database connection string
		const dbUrl = process.env.DATABASE_URL;
		if (!dbUrl) {
			throw new Error("DATABASE_URL environment variable is not set");
		}
		this.pgVector = new PgVector(dbUrl);

		// Initialize the cache with 100 entries and 15 minute TTL
		this.cache = new VectorCache(100, 15);

		// Initialize metrics service
		this.metricsService = RagMetricsService.getInstance();
	}

	/**
	 * Process a website page and store its embeddings
	 */
	// biome-ignore lint/suspicious/noExplicitAny: Using any type for metadata JSON
	async processPage(page: any): Promise<void> {
		console.log(`Processing page: ${page.url}`);
		const startTime = Date.now();

		try {
			// Use optimal chunking strategy based on content analysis
			const chunkingStartTime = Date.now();
			const chunks = await chunkWithOptimalStrategy(page.content);
			const chunkingLatency = Date.now() - chunkingStartTime;

			console.log(`Created ${chunks.length} chunks for page: ${page.url}`);

			if (chunks.length === 0) {
				console.warn(`No chunks created for page: ${page.url}`);
				return;
			}

			// Record chunking metrics
			this.metricsService.recordChunking(page.websiteId, {
				totalChunks: chunks.length,
				averageChunkSize:
					chunks.reduce((acc, chunk) => acc + chunk.text.length, 0) /
					chunks.length,
				latency: chunkingLatency,
			});

			// Generate embeddings for the chunks
			const embeddingStartTime = Date.now();
			const { embeddings } = await embedMany({
				model: mistral.textEmbeddingModel(
					this.options.embeddingModel ?? "mistral-embed",
				),
				values: chunks.map((chunk) => chunk.text),
			});
			const embeddingLatency = Date.now() - embeddingStartTime;

			// Record embedding metrics
			this.metricsService.recordEmbedding(page.websiteId, {
				latency: embeddingLatency,
				dimensions: embeddings[0]?.length || 0,
			});

			console.log(
				`Generated ${embeddings.length} embeddings for page: ${page.url}`,
			);

			// Delete existing embeddings for this page
			await this.deletePageEmbeddings(page.id);

			// Store the embeddings in the database
			await this.storeEmbeddings(page.websiteId, page.id, chunks, embeddings);

			console.log(`Successfully processed page: ${page.url}`);
		} catch (error) {
			console.error(`Error processing page ${page.url}:`, error);
			this.metricsService.recordError(page.websiteId, error as Error);
			throw error;
		}
	}

	/**
	 * Process all pages for a website
	 *
	 * @param website - The website to process
	 * @param options - Optional processing options
	 * @returns A promise that resolves when processing is complete
	 */
	async processWebsite(
		website: Website,
		options: {
			updateStatus?: boolean;
			checkRebuildIndex?: boolean;
		} = {},
	): Promise<void> {
		const { updateStatus = true, checkRebuildIndex = true } = options;

		console.log(`Processing website: ${website.name} (${website.url})`);

		try {
			// Update the website status to PROCESSING if requested
			if (updateStatus) {
				await websitesRepository.update(website.id, {
					status: "PROCESSING",
				});
			}

			// Get all pages for the website
			const pages = await websitePagesRepository.getByWebsiteId(website.id);

			console.log(`Found ${pages.length} pages for website: ${website.name}`);

			// Clear the cache for this website before processing
			this.clearCache(website.id);

			// Process each page
			for (const page of pages) {
				await this.processPage(page);
			}

			// Check if we should rebuild the vector index
			if (checkRebuildIndex && (await shouldRebuildIndex())) {
				console.log("Vector index needs rebuilding based on data changes");
				await rebuildVectorIndex();
			}

			// Update the website status to ACTIVE if requested
			if (updateStatus) {
				await websitesRepository.update(website.id, {
					status: "ACTIVE",
				});
			}

			console.log(`Successfully processed website: ${website.name}`);
		} catch (error) {
			console.error(`Error processing website ${website.name}:`, error);

			// Update the website status to ERROR if requested
			if (updateStatus) {
				await websitesRepository.update(website.id, {
					status: "ERROR",
				});
			}

			throw error;
		}
	}

	/**
	 * Store embeddings in the database
	 */
	private async storeEmbeddings(
		websiteId: string,
		pageId: string,
		chunks: { text: string; id?: string }[],
		embeddings: number[][],
	): Promise<void> {
		// Insert the embeddings into the database
		for (let i = 0; i < chunks.length; i++) {
			const chunk = chunks[i];
			const embedding = embeddings[i];

			// Log the embedding dimensions for debugging
			console.log(`Embedding dimensions: ${embedding.length}`);

			await db.execute(sql`
        INSERT INTO website_embeddings (
          website_id,
          page_id,
          chunk_text,
          embedding,
          metadata
        ) VALUES (
          ${websiteId},
          ${pageId},
          ${chunk.text},
          ${sql.raw(`'[${embedding.join(",")}]'::vector(1024)`)},
          ${JSON.stringify({
						text: chunk.text,
						pageId: pageId,
						websiteId: websiteId,
					})}
        )
      `);
		}
	}

	/**
	 * Delete embeddings for a page
	 */
	private async deletePageEmbeddings(pageId: string): Promise<void> {
		await db.execute(sql`
      DELETE FROM website_embeddings
      WHERE page_id = ${pageId}
    `);
	}

	/**
	 * Delete embeddings for a website
	 */
	async deleteWebsiteEmbeddings(websiteId: string): Promise<void> {
		await db.execute(sql`
      DELETE FROM website_embeddings
      WHERE website_id = ${websiteId}
    `);

		// Clear the cache for this website
		this.cache.clearForWebsite(websiteId);
	}

	/**
	 * Clear the cache for a specific website
	 */
	clearCache(websiteId: string): void {
		this.cache.clearForWebsite(websiteId);
	}

	/**
	 * Search for similar content
	 */
	async searchSimilarContent(
		websiteId: string,
		query: string,
		limit = 5,
		metadataFilters?: {
			[key: string]: {
				$eq?: string | number | boolean;
				$in?: Array<string | number | boolean>;
				$gt?: number;
				$lt?: number;
				$gte?: number;
				$lte?: number;
			};
		},
	): Promise<
		Array<{
			text: string;
			pageId: string;
			url: string;
			title: string;
			similarity: number;
		}>
	> {
		const startTime = Date.now();
		let cacheHit = false;

		try {
			// Check cache first if query is not too complex
			if (query.length < 100) {
				const cacheKey = metadataFilters
					? `${websiteId}:${query}:${limit}:${JSON.stringify(metadataFilters)}`
					: `${websiteId}:${query}:${limit}`;
				const cachedResults = this.cache.get(websiteId, cacheKey, limit);
				if (cachedResults) {
					console.log("Using cached results for query:", query);
					cacheHit = true;

					// Record metrics for cache hit
					this.metricsService.recordSearch(websiteId, {
						latency: Date.now() - startTime,
						resultsCount: cachedResults.length,
						averageSimilarity:
							cachedResults.reduce((acc, r) => acc + r.similarity, 0) /
							cachedResults.length,
						cacheHit: true,
					});

					return cachedResults;
				}
			}

			// Generate an embedding for the query
			const { embedding } = await embed({
				model: mistral.textEmbeddingModel(
					this.options.embeddingModel ?? "mistral-embed",
				),
				value: query,
			});

			// Log the embedding dimensions for debugging
			console.log(`Search embedding dimensions: ${embedding.length}`);

			// Build the SQL query with metadata filters and optimized search
			let sqlQuery = sql`
        WITH vector_search AS (
          SELECT
            we.chunk_text as text,
            we.page_id,
            wp.url,
            wp.title,
            1 - (we.embedding <-> ${sql.raw(
							`'[${embedding.join(",")}]'::vector(1024)`,
						)}) as similarity,
            we.metadata,
            ROW_NUMBER() OVER (
              PARTITION BY we.page_id 
              ORDER BY 1 - (we.embedding <-> ${sql.raw(
								`'[${embedding.join(",")}]'::vector(1024)`,
							)}) DESC
            ) as rank
          FROM
            website_embeddings we
          JOIN
            website_pages wp ON we.page_id = wp.id
          WHERE
            we.website_id = ${websiteId}
            AND we.embedding <-> ${sql.raw(
							`'[${embedding.join(",")}]'::vector(1024)`,
						)} < 0.8  -- Filter out low similarity results early
      `;

			// Add metadata filters if provided
			if (metadataFilters) {
				const filterConditions = Object.entries(metadataFilters).map(
					([key, conditions]) => {
						const conditionsList = Object.entries(conditions).map(
							([op, value]) => {
								switch (op) {
									case "$eq":
										return sql`we.metadata->>'${key}' = ${String(value)}`;
									case "$in":
										return sql`we.metadata->>'${key}' = ANY(${value})`;
									case "$gt":
										return sql`CAST(we.metadata->>'${key}' AS numeric) > ${value}`;
									case "$lt":
										return sql`CAST(we.metadata->>'${key}' AS numeric) < ${value}`;
									case "$gte":
										return sql`CAST(we.metadata->>'${key}' AS numeric) >= ${value}`;
									case "$lte":
										return sql`CAST(we.metadata->>'${key}' AS numeric) <= ${value}`;
									default:
										return sql`1=1`; // No condition
								}
							},
						);
						return sql`(${sql.join(conditionsList, sql` AND `)})`;
					},
				);

				if (filterConditions.length > 0) {
					sqlQuery = sql`${sqlQuery} AND ${sql.join(
						filterConditions,
						sql` AND `,
					)}`;
				}
			}

			// Close the CTE and add final filtering
			sqlQuery = sql`${sqlQuery}
        )
        SELECT 
          text,
          page_id,
          url,
          title,
          similarity,
          metadata
        FROM vector_search
        WHERE rank = 1  -- Get only the best match per page
        ORDER BY similarity DESC
        LIMIT ${limit}
      `;

			// Execute the query
			const { rows } = await db.execute(sqlQuery);

			// If no results found, return an empty array
			if (!rows || !Array.isArray(rows) || rows.length === 0) {
				return [];
			}

			// Map the results to the expected format
			const results = rows.map((row) => ({
				text: String(row.text),
				pageId: String(row.page_id),
				url: String(row.url),
				title: String(row.title),
				similarity: Number(row.similarity),
				metadata: row.metadata,
			}));

			// Cache the results for future queries
			if (query.length < 100) {
				const cacheKey = metadataFilters
					? `${websiteId}:${query}:${limit}:${JSON.stringify(metadataFilters)}`
					: `${websiteId}:${query}:${limit}`;
				this.cache.set(websiteId, cacheKey, limit, results);
			}

			// Record metrics for successful search
			this.metricsService.recordSearch(websiteId, {
				latency: Date.now() - startTime,
				resultsCount: results.length,
				averageSimilarity:
					results.reduce((acc, r) => acc + r.similarity, 0) / results.length,
				cacheHit: false,
			});

			return results;
		} catch (error) {
			console.error("Error searching for similar content:", error);
			this.metricsService.recordError(websiteId, error as Error);
			// Return an empty array instead of throwing to avoid breaking the tool invocation
			return [];
		}
	}
}
