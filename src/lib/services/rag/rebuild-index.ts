import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * Rebuilds the vector index for the website_embeddings table with optimized parameters
 * based on the current dataset size.
 *
 * @returns {Promise<void>}
 */
export async function rebuildVectorIndex(): Promise<void> {
	try {
		console.log("Starting vector index rebuild...");

		// Count the number of vectors in the table
		const countResult = await db.execute(
			sql`SELECT COUNT(*) as count FROM website_embeddings`,
		);

		const count = Number.parseInt(countResult.rows[0].count as string, 10);
		console.log(`Found ${count} vectors in website_embeddings table`);

		// Calculate optimal number of lists based on dataset size
		// Rule of thumb: sqrt(N) for smaller datasets, N/100 for larger ones
		let lists = 100; // Default

		if (count < 1000) {
			lists = 10; // Small dataset
		} else if (count < 10000) {
			lists = Math.ceil(Math.sqrt(count)); // Medium dataset
		} else if (count < 100000) {
			lists = Math.ceil(count / 100); // Large dataset
		} else {
			lists = Math.ceil(count / 200); // Very large dataset
		}

		console.log(`Calculated optimal lists parameter: ${lists}`);

		// Drop existing index if it exists
		await db.execute(
			sql`DROP INDEX IF EXISTS website_embeddings_embedding_idx`,
		);

		console.log("Dropped existing index");

		// Create new index with optimized parameters
		await db.execute(
			sql`CREATE INDEX website_embeddings_embedding_idx 
          ON website_embeddings 
          USING ivfflat (embedding vector_cosine_ops) 
          WITH (lists = ${lists})`,
		);

		console.log("Created new optimized index");

		// Analyze the table to update statistics
		await db.execute(sql`ANALYZE website_embeddings`);

		console.log("Vector index rebuild completed successfully");
	} catch (error) {
		console.error("Error rebuilding vector index:", error);
		throw error;
	}
}

/**
 * Checks if the vector index needs rebuilding based on the number of new vectors
 * added since the last rebuild.
 *
 * @returns {Promise<boolean>} True if index should be rebuilt
 */
export async function shouldRebuildIndex(): Promise<boolean> {
	try {
		// Get the total count
		const countResult = await db.execute(
			sql`SELECT COUNT(*) as count FROM website_embeddings`,
		);

		const totalCount = Number.parseInt(countResult.rows[0].count as string, 10);

		// Get the count of vectors added in the last day
		const recentCountResult = await db.execute(
			sql`SELECT COUNT(*) as count FROM website_embeddings 
          WHERE created_at > NOW() - INTERVAL '1 day'`,
		);

		const recentCount = Number.parseInt(
			recentCountResult.rows[0].count as string,
			10,
		);

		// Calculate the percentage of new vectors
		const percentNew = (recentCount / totalCount) * 100;

		// Recommend rebuilding if more than 20% new vectors or if total count is very small
		return percentNew > 20 || (totalCount < 1000 && recentCount > 100);
	} catch (error) {
		console.error("Error checking if index should be rebuilt:", error);
		return false;
	}
}
