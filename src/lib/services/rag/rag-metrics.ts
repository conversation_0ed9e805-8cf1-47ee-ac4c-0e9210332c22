import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

export interface RagMetrics {
	// Search metrics
	searchLatency: number;
	searchResultsCount: number;
	averageSimilarity: number;
	cacheHitRate: number;

	// Chunking metrics
	totalChunks: number;
	averageChunkSize: number;
	chunkingLatency: number;

	// Embedding metrics
	embeddingLatency: number;
	embeddingDimensions: number;

	// Error metrics
	errorCount: number;
	errorTypes: Record<string, number>;
}

export class RagMetricsService {
	private static instance: RagMetricsService;
	private metrics: Map<string, RagMetrics> = new Map();
	private readonly flushInterval = 60000; // Flush metrics every minute

	private constructor() {
		// Start periodic flushing of metrics
		setInterval(() => this.flushMetrics(), this.flushInterval);
	}

	public static getInstance(): RagMetricsService {
		if (!RagMetricsService.instance) {
			RagMetricsService.instance = new RagMetricsService();
		}
		return RagMetricsService.instance;
	}

	/**
	 * Record search metrics
	 */
	public recordSearch(
		websiteId: string,
		metrics: {
			latency: number;
			resultsCount: number;
			averageSimilarity: number;
			cacheHit: boolean;
		},
	): void {
		const currentMetrics = this.getOrCreateMetrics(websiteId);

		// Update search metrics
		currentMetrics.searchLatency = metrics.latency;
		currentMetrics.searchResultsCount = metrics.resultsCount;
		currentMetrics.averageSimilarity = metrics.averageSimilarity;

		// Update cache hit rate (rolling average)
		const currentCacheHitRate = currentMetrics.cacheHitRate || 0;
		currentMetrics.cacheHitRate =
			(currentCacheHitRate + (metrics.cacheHit ? 1 : 0)) / 2;

		this.metrics.set(websiteId, currentMetrics);
	}

	/**
	 * Record chunking metrics
	 */
	public recordChunking(
		websiteId: string,
		metrics: {
			totalChunks: number;
			averageChunkSize: number;
			latency: number;
		},
	): void {
		const currentMetrics = this.getOrCreateMetrics(websiteId);

		currentMetrics.totalChunks = metrics.totalChunks;
		currentMetrics.averageChunkSize = metrics.averageChunkSize;
		currentMetrics.chunkingLatency = metrics.latency;

		this.metrics.set(websiteId, currentMetrics);
	}

	/**
	 * Record embedding metrics
	 */
	public recordEmbedding(
		websiteId: string,
		metrics: {
			latency: number;
			dimensions: number;
		},
	): void {
		const currentMetrics = this.getOrCreateMetrics(websiteId);

		currentMetrics.embeddingLatency = metrics.latency;
		currentMetrics.embeddingDimensions = metrics.dimensions;

		this.metrics.set(websiteId, currentMetrics);
	}

	/**
	 * Record error metrics
	 */
	public recordError(websiteId: string, error: Error): void {
		const currentMetrics = this.getOrCreateMetrics(websiteId);

		currentMetrics.errorCount = (currentMetrics.errorCount || 0) + 1;
		currentMetrics.errorTypes[error.name] =
			(currentMetrics.errorTypes[error.name] || 0) + 1;

		this.metrics.set(websiteId, currentMetrics);
	}

	/**
	 * Get metrics for a specific website
	 */
	public getMetrics(websiteId: string): RagMetrics | undefined {
		return this.metrics.get(websiteId);
	}

	/**
	 * Get all metrics
	 */
	public getAllMetrics(): Map<string, RagMetrics> {
		return this.metrics;
	}

	/**
	 * Reset metrics for a specific website
	 */
	public resetMetrics(websiteId: string): void {
		this.metrics.delete(websiteId);
	}

	/**
	 * Reset all metrics
	 */
	public resetAllMetrics(): void {
		this.metrics.clear();
	}

	private getOrCreateMetrics(websiteId: string): RagMetrics {
		return (
			this.metrics.get(websiteId) || {
				searchLatency: 0,
				searchResultsCount: 0,
				averageSimilarity: 0,
				cacheHitRate: 0,
				totalChunks: 0,
				averageChunkSize: 0,
				chunkingLatency: 0,
				embeddingLatency: 0,
				embeddingDimensions: 0,
				errorCount: 0,
				errorTypes: {},
			}
		);
	}

	/**
	 * Flush metrics to the database
	 */
	private async flushMetrics(): Promise<void> {
		try {
			const timestamp = new Date();

			for (const [websiteId, metrics] of this.metrics.entries()) {
				await db.execute(sql`
          INSERT INTO rag_metrics (
            website_id,
            timestamp,
            search_latency,
            search_results_count,
            average_similarity,
            cache_hit_rate,
            total_chunks,
            average_chunk_size,
            chunking_latency,
            embedding_latency,
            embedding_dimensions,
            error_count,
            error_types
          ) VALUES (
            ${websiteId},
            ${timestamp},
            ${metrics.searchLatency},
            ${metrics.searchResultsCount},
            ${metrics.averageSimilarity},
            ${metrics.cacheHitRate},
            ${metrics.totalChunks},
            ${metrics.averageChunkSize},
            ${metrics.chunkingLatency},
            ${metrics.embeddingLatency},
            ${metrics.embeddingDimensions},
            ${metrics.errorCount},
            ${JSON.stringify(metrics.errorTypes)}
          )
        `);
			}

			// Clear metrics after successful flush
			this.metrics.clear();
		} catch (error) {
			console.error("Error flushing RAG metrics:", error);
		}
	}
}
