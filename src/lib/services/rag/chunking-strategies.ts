import { MDocument } from "@mastra/rag";

/**
 * Chunking strategies for different types of content
 */
export const ChunkingStrategies = {
	/**
	 * Default strategy for general content
	 * Uses recursive chunking with moderate size and overlap
	 */
	DEFAULT: {
		strategy: "recursive" as const,
		size: 512,
		overlap: 50,
	},

	/**
	 * Strategy for long-form content like articles or blog posts
	 * Uses larger chunks with more overlap to maintain context
	 */
	LONG_FORM: {
		strategy: "recursive" as const,
		size: 1024,
		overlap: 100,
	},

	/**
	 * Strategy for technical documentation
	 * Uses smaller chunks to capture specific details
	 */
	TECHNICAL: {
		strategy: "recursive" as const,
		size: 384,
		overlap: 75,
	},

	/**
	 * Strategy for FAQ or Q&A content
	 * Uses smaller chunks with minimal overlap
	 */
	FAQ: {
		strategy: "recursive" as const,
		size: 256,
		overlap: 25,
	},
};

/**
 * Detect the best chunking strategy based on content analysis
 *
 * @param content The text content to analyze
 * @returns The most appropriate chunking strategy
 */
export async function detectChunkingStrategy(
	content: string,
): Promise<(typeof ChunkingStrategies)[keyof typeof ChunkingStrategies]> {
	// Default to the DEFAULT strategy
	let strategy = ChunkingStrategies.DEFAULT;

	// Check content length
	if (content.length > 10000) {
		strategy = ChunkingStrategies.LONG_FORM;
	}

	// Check for technical content markers
	const technicalMarkers = [
		"function",
		"class",
		"method",
		"api",
		"code",
		"syntax",
		"parameter",
		"return value",
		"exception",
		"error",
		"implementation",
		"algorithm",
	];

	const hasTechnicalMarkers = technicalMarkers.some((marker) =>
		content.toLowerCase().includes(marker),
	);

	if (hasTechnicalMarkers) {
		strategy = ChunkingStrategies.TECHNICAL;
	}

	// Check for FAQ patterns
	const faqMarkers = [
		"q:",
		"q.",
		"question:",
		"frequently asked",
		"faq",
		"q&a",
		"how do i",
		"what is",
		"how can i",
		"why does",
	];

	const hasFaqMarkers = faqMarkers.some((marker) =>
		content.toLowerCase().includes(marker),
	);

	if (hasFaqMarkers) {
		strategy = ChunkingStrategies.FAQ;
	}

	return strategy;
}

interface ChunkingOptions {
	chunkSize?: number;
	chunkOverlap?: number;
	minChunkSize?: number;
	maxChunkSize?: number;
}

/**
 * Creates optimal chunks based on content analysis
 */
export async function chunkWithOptimalStrategy(
	content: string,
	options: ChunkingOptions = {},
): Promise<Array<{ text: string; id?: string }>> {
	const {
		chunkSize = 512,
		chunkOverlap = 50,
		minChunkSize = 100,
		maxChunkSize = 1024,
	} = options;

	// First, split content into sections based on headers
	const sections = splitIntoSections(content);

	// Process each section with appropriate chunking strategy
	const chunks: Array<{ text: string; id?: string }> = [];

	for (const section of sections) {
		// Determine the best chunking strategy for this section
		const strategy = determineChunkingStrategy(section);

		// Create a document from the section content
		const doc = MDocument.fromText(section.content);

		// Apply the appropriate chunking strategy
		const sectionChunks = await doc.chunk({
			size: strategy.chunkSize,
			overlap: strategy.chunkOverlap,
		});

		// Add metadata to chunks
		const enrichedChunks = sectionChunks.map((chunk: { text: string }) => ({
			text: chunk.text,
			id: `${section.type}-${Date.now()}-${Math.random()
				.toString(36)
				.substr(2, 9)}`,
		}));

		chunks.push(...enrichedChunks);
	}

	return chunks;
}

interface Section {
	type: "header" | "paragraph" | "list" | "code" | "table";
	content: string;
	level?: number;
}

/**
 * Splits content into logical sections
 */
function splitIntoSections(content: string): Section[] {
	const sections: Section[] = [];
	const lines = content.split("\n");

	let currentSection: Section | null = null;
	let currentContent: string[] = [];

	for (const line of lines) {
		// Check for headers
		const headerMatch = line.match(/^(#{1,6})\s+(.+)$/);
		if (headerMatch) {
			// Save previous section if exists
			if (currentSection) {
				currentSection.content = currentContent.join("\n");
				sections.push(currentSection);
			}

			// Start new header section
			currentSection = {
				type: "header",
				content: "",
				level: headerMatch[1].length,
			};
			currentContent = [line];
			continue;
		}

		// Check for list items
		if (line.match(/^[-*]\s+/)) {
			if (currentSection?.type !== "list") {
				if (currentSection) {
					currentSection.content = currentContent.join("\n");
					sections.push(currentSection);
				}
				currentSection = { type: "list", content: "" };
				currentContent = [];
			}
			currentContent.push(line);
			continue;
		}

		// Check for code blocks
		if (line.startsWith("```")) {
			if (currentSection?.type !== "code") {
				if (currentSection) {
					currentSection.content = currentContent.join("\n");
					sections.push(currentSection);
				}
				currentSection = { type: "code", content: "" };
				currentContent = [];
			}
			currentContent.push(line);
			continue;
		}

		// Check for tables
		if (line.includes("|")) {
			if (currentSection?.type !== "table") {
				if (currentSection) {
					currentSection.content = currentContent.join("\n");
					sections.push(currentSection);
				}
				currentSection = { type: "table", content: "" };
				currentContent = [];
			}
			currentContent.push(line);
			continue;
		}

		// Regular paragraph
		if (line.trim()) {
			if (currentSection?.type !== "paragraph") {
				if (currentSection) {
					currentSection.content = currentContent.join("\n");
					sections.push(currentSection);
				}
				currentSection = { type: "paragraph", content: "" };
				currentContent = [];
			}
			currentContent.push(line);
		}
	}

	// Add the last section
	if (currentSection) {
		currentSection.content = currentContent.join("\n");
		sections.push(currentSection);
	}

	return sections;
}

interface ChunkingStrategy {
	chunkSize: number;
	chunkOverlap: number;
}

/**
 * Determines the best chunking strategy based on content type
 */
function determineChunkingStrategy(section: Section): ChunkingStrategy {
	switch (section.type) {
		case "header":
			return {
				chunkSize: 256,
				chunkOverlap: 25,
			};
		case "list":
			return {
				chunkSize: 384,
				chunkOverlap: 50,
			};
		case "code":
			return {
				chunkSize: 768,
				chunkOverlap: 100,
			};
		case "table":
			return {
				chunkSize: 512,
				chunkOverlap: 75,
			};
		case "paragraph":
			return {
				chunkSize: 512,
				chunkOverlap: 50,
			};
	}
}
