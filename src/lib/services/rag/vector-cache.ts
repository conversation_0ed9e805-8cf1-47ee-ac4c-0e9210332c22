/**
 * A simple in-memory cache for vector search results to improve performance
 * for frequently accessed queries.
 */

interface CacheEntry<T> {
	data: T;
	timestamp: number;
}

export class VectorCache<T> {
	private cache: Map<string, CacheEntry<T>> = new Map();
	private maxEntries: number;
	private ttlMs: number;

	/**
	 * Create a new vector cache
	 *
	 * @param maxEntries Maximum number of entries to store in the cache (default: 100)
	 * @param ttlMinutes Time-to-live in minutes for cache entries (default: 30)
	 */
	constructor(maxEntries = 100, ttlMinutes = 30) {
		this.maxEntries = maxEntries;
		this.ttlMs = ttlMinutes * 60 * 1000;
	}

	/**
	 * Generate a cache key from the query parameters
	 *
	 * @param websiteId Website ID
	 * @param query Search query
	 * @param limit Result limit
	 * @returns Cache key
	 */
	private generateKey(websiteId: string, query: string, limit: number): string {
		// Normalize the query by trimming whitespace and converting to lowercase
		const normalizedQuery = query.trim().toLowerCase();
		return `${websiteId}:${normalizedQuery}:${limit}`;
	}

	/**
	 * Get a value from the cache
	 *
	 * @param websiteId Website ID
	 * @param query Search query
	 * @param limit Result limit
	 * @returns Cached data or null if not found or expired
	 */
	get(websiteId: string, query: string, limit: number): T | null {
		const key = this.generateKey(websiteId, query, limit);
		const entry = this.cache.get(key);

		if (!entry) {
			return null;
		}

		// Check if the entry has expired
		const now = Date.now();
		if (now - entry.timestamp > this.ttlMs) {
			this.cache.delete(key);
			return null;
		}

		return entry.data;
	}

	/**
	 * Store a value in the cache
	 *
	 * @param websiteId Website ID
	 * @param query Search query
	 * @param limit Result limit
	 * @param data Data to cache
	 */
	set(websiteId: string, query: string, limit: number, data: T): void {
		// Evict oldest entries if we've reached the maximum size
		if (this.cache.size >= this.maxEntries) {
			this.evictOldest();
		}

		const key = this.generateKey(websiteId, query, limit);
		this.cache.set(key, {
			data,
			timestamp: Date.now(),
		});
	}

	/**
	 * Clear the entire cache
	 */
	clear(): void {
		this.cache.clear();
	}

	/**
	 * Clear cache entries for a specific website
	 *
	 * @param websiteId Website ID
	 */
	clearForWebsite(websiteId: string): void {
		for (const key of this.cache.keys()) {
			if (key.startsWith(`${websiteId}:`)) {
				this.cache.delete(key);
			}
		}
	}

	/**
	 * Evict the oldest entry from the cache
	 */
	private evictOldest(): void {
		let oldestKey: string | null = null;
		let oldestTimestamp = Number.POSITIVE_INFINITY;

		for (const [key, entry] of this.cache.entries()) {
			if (entry.timestamp < oldestTimestamp) {
				oldestTimestamp = entry.timestamp;
				oldestKey = key;
			}
		}

		if (oldestKey) {
			this.cache.delete(oldestKey);
		}
	}

	/**
	 * Get the number of entries in the cache
	 */
	get size(): number {
		return this.cache.size;
	}
}
