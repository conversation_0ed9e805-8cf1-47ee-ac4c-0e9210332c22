import { db } from "@/lib/db";
import {
	messagesTable,
	plansTable,
	usersTable,
	websitePagesTable,
	websitesTable,
} from "@/lib/db/schema";
import { and, count, eq, sql } from "drizzle-orm";

/**
 * Service for checking and enforcing plan limits
 */
export class PlanLimitsService {
	/**
	 * Check if a user has reached their website limit
	 * @param userId The user's ID
	 * @returns An object with the limit check result and details
	 */
	async checkWebsiteLimit(userId: string): Promise<{
		hasReachedLimit: boolean;
		currentCount: number;
		limit: number;
	}> {
		// Get the user's plan
		const [user] = await db
			.select({
				planId: usersTable.planId,
			})
			.from(usersTable)
			.where(eq(usersTable.id, userId));

		if (!user || !user.planId) {
			// Default to free plan limits if no plan is found
			const [freePlan] = await db
				.select({
					websiteLimit: plansTable.websiteLimit,
				})
				.from(plansTable)
				.where(eq(plansTable.name, "Free"));

			const limit = freePlan?.websiteLimit || 1;

			// Count existing websites
			const [{ count: websiteCount }] = await db
				.select({ count: count() })
				.from(websitesTable)
				.where(eq(websitesTable.userId, userId));

			return {
				hasReachedLimit: Number(websiteCount) >= limit,
				currentCount: Number(websiteCount),
				limit,
			};
		}

		// Get the plan details
		const [plan] = await db
			.select({
				websiteLimit: plansTable.websiteLimit,
			})
			.from(plansTable)
			.where(eq(plansTable.id, user.planId));

		if (!plan) {
			// Default to free plan limits if plan not found
			return {
				hasReachedLimit: true,
				currentCount: 0,
				limit: 1,
			};
		}

		// Count existing websites
		const [{ count: websiteCount }] = await db
			.select({ count: count() })
			.from(websitesTable)
			.where(eq(websitesTable.userId, userId));

		return {
			hasReachedLimit: Number(websiteCount) >= plan.websiteLimit,
			currentCount: Number(websiteCount),
			limit: plan.websiteLimit,
		};
	}

	/**
	 * Check if a website has reached its page limit
	 * @param websiteId The website's ID
	 * @param userId The user's ID
	 * @returns An object with the limit check result and details
	 */
	async checkPagesLimit(
		websiteId: string,
		userId: string,
	): Promise<{
		hasReachedLimit: boolean;
		currentCount: number;
		limit: number;
	}> {
		// Get the user's plan
		const [user] = await db
			.select({
				planId: usersTable.planId,
			})
			.from(usersTable)
			.where(eq(usersTable.id, userId));

		if (!user || !user.planId) {
			// Default to free plan limits if no plan is found
			const [freePlan] = await db
				.select({
					pagesPerWebsiteLimit: plansTable.pagesPerWebsiteLimit,
				})
				.from(plansTable)
				.where(eq(plansTable.name, "Free"));

			const limit = freePlan?.pagesPerWebsiteLimit || 50;

			// Count existing pages
			const [{ count: pagesCount }] = await db
				.select({ count: count() })
				.from(websitePagesTable)
				.where(eq(websitePagesTable.websiteId, websiteId));

			return {
				hasReachedLimit: Number(pagesCount) >= limit,
				currentCount: Number(pagesCount),
				limit,
			};
		}

		// Get the plan details
		const [plan] = await db
			.select({
				pagesPerWebsiteLimit: plansTable.pagesPerWebsiteLimit,
			})
			.from(plansTable)
			.where(eq(plansTable.id, user.planId));

		if (!plan) {
			// Default to free plan limits if plan not found
			return {
				hasReachedLimit: true,
				currentCount: 0,
				limit: 50,
			};
		}

		// Count existing pages
		const [{ count: pagesCount }] = await db
			.select({ count: count() })
			.from(websitePagesTable)
			.where(eq(websitePagesTable.websiteId, websiteId));

		return {
			hasReachedLimit: Number(pagesCount) >= plan.pagesPerWebsiteLimit,
			currentCount: Number(pagesCount),
			limit: plan.pagesPerWebsiteLimit,
		};
	}

	/**
	 * Check if a user has reached their daily message limit
	 * @param userId The user's ID
	 * @returns An object with the limit check result and details
	 */
	async checkMessagesLimit(userId: string): Promise<{
		hasReachedLimit: boolean;
		currentCount: number;
		limit: number;
	}> {
		// Get the user's plan
		const [user] = await db
			.select({
				planId: usersTable.planId,
			})
			.from(usersTable)
			.where(eq(usersTable.id, userId));

		if (!user || !user.planId) {
			// Default to free plan limits if no plan is found
			const [freePlan] = await db
				.select({
					messagesPerDayLimit: plansTable.messagesPerDayLimit,
				})
				.from(plansTable)
				.where(eq(plansTable.name, "Free"));

			const limit = freePlan?.messagesPerDayLimit || 100;

			// Count today's messages across all websites owned by the user
			const [{ count: messagesCount }] = await db
				.select({ count: count() })
				.from(messagesTable)
				.innerJoin(
					sql`conversations`,
					sql`conversations.id = ${messagesTable.conversationId}`,
				)
				.innerJoin(
					sql`websites`,
					sql`websites.id = conversations.website_id AND websites.user_id = ${userId}`,
				)
				.where(
					sql`${messagesTable.createdAt} >= CURRENT_DATE AND ${messagesTable.createdAt} < CURRENT_DATE + INTERVAL '1 day'`,
				);

			return {
				hasReachedLimit: Number(messagesCount) >= limit,
				currentCount: Number(messagesCount),
				limit,
			};
		}

		// Get the plan details
		const [plan] = await db
			.select({
				messagesPerDayLimit: plansTable.messagesPerDayLimit,
			})
			.from(plansTable)
			.where(eq(plansTable.id, user.planId));

		if (!plan) {
			// Default to free plan limits if plan not found
			return {
				hasReachedLimit: true,
				currentCount: 0,
				limit: 100,
			};
		}

		// Count today's messages across all websites owned by the user
		const [{ count: messagesCount }] = await db
			.select({ count: count() })
			.from(messagesTable)
			.innerJoin(
				sql`conversations`,
				sql`conversations.id = ${messagesTable.conversationId}`,
			)
			.innerJoin(
				sql`websites`,
				sql`websites.id = conversations.website_id AND websites.user_id = ${userId}`,
			)
			.where(
				sql`${messagesTable.createdAt} >= CURRENT_DATE AND ${messagesTable.createdAt} < CURRENT_DATE + INTERVAL '1 day'`,
			);

		return {
			hasReachedLimit: Number(messagesCount) >= plan.messagesPerDayLimit,
			currentCount: Number(messagesCount),
			limit: plan.messagesPerDayLimit,
		};
	}
}
