ALTER TABLE "chat_configurations" DROP CONSTRAINT "chat_configurations_website_id_websites_id_fk";
--> statement-breakpoint
ALTER TABLE "conversations" DROP CONSTRAINT "conversations_website_id_websites_id_fk";
--> statement-breakpoint
ALTER TABLE "crawl_operations" DROP CONSTRAINT "crawl_operations_website_id_websites_id_fk";
--> statement-breakpoint
ALTER TABLE "messages" DROP CONSTRAINT "messages_conversation_id_conversations_id_fk";
--> statement-breakpoint
ALTER TABLE "website_pages" DROP CONSTRAINT "website_pages_website_id_websites_id_fk";
--> statement-breakpoint
ALTER TABLE "website_embeddings" DROP CONSTRAINT "website_embeddings_website_id_websites_id_fk";
--> statement-breakpoint
ALTER TABLE "website_embeddings" DROP CONSTRAINT "website_embeddings_page_id_website_pages_id_fk";
--> statement-breakpoint
ALTER TABLE "websites" ADD COLUMN "crawl_configuration" jsonb;--> statement-breakpoint
ALTER TABLE "chat_analytics_events" ADD CONSTRAINT "chat_analytics_events_website_id_websites_id_fk" FOREIGN KEY ("website_id") REFERENCES "public"."websites"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chat_performance_metrics" ADD CONSTRAINT "chat_performance_metrics_website_id_websites_id_fk" FOREIGN KEY ("website_id") REFERENCES "public"."websites"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chat_configurations" ADD CONSTRAINT "chat_configurations_website_id_websites_id_fk" FOREIGN KEY ("website_id") REFERENCES "public"."websites"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_website_id_websites_id_fk" FOREIGN KEY ("website_id") REFERENCES "public"."websites"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "crawl_operations" ADD CONSTRAINT "crawl_operations_website_id_websites_id_fk" FOREIGN KEY ("website_id") REFERENCES "public"."websites"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "messages" ADD CONSTRAINT "messages_conversation_id_conversations_id_fk" FOREIGN KEY ("conversation_id") REFERENCES "public"."conversations"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "website_pages" ADD CONSTRAINT "website_pages_website_id_websites_id_fk" FOREIGN KEY ("website_id") REFERENCES "public"."websites"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "website_embeddings" ADD CONSTRAINT "website_embeddings_website_id_websites_id_fk" FOREIGN KEY ("website_id") REFERENCES "public"."websites"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "website_embeddings" ADD CONSTRAINT "website_embeddings_page_id_website_pages_id_fk" FOREIGN KEY ("page_id") REFERENCES "public"."website_pages"("id") ON DELETE cascade ON UPDATE no action;