{"id": "32f83e4f-8498-46cd-bf85-77d791a0929d", "prevId": "f14c1a2d-0e83-4871-b2e0-25d416e1e705", "version": "7", "dialect": "postgresql", "tables": {"public.chat_analytics_events": {"name": "chat_analytics_events", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "website_id": {"name": "website_id", "type": "uuid", "primaryKey": false, "notNull": true}, "visitor_id": {"name": "visitor_id", "type": "text", "primaryKey": false, "notNull": true}, "conversation_id": {"name": "conversation_id", "type": "uuid", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "text", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "default": "'{}'"}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_performance_metrics": {"name": "chat_performance_metrics", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "website_id": {"name": "website_id", "type": "uuid", "primaryKey": false, "notNull": true}, "conversation_id": {"name": "conversation_id", "type": "uuid", "primaryKey": false, "notNull": true}, "response_time": {"name": "response_time", "type": "integer", "primaryKey": false, "notNull": true}, "tokens_used": {"name": "tokens_used", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "rag_used": {"name": "rag_used", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "rag_result_count": {"name": "rag_result_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_configurations": {"name": "chat_configurations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "website_id": {"name": "website_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "'Default Configuration'"}, "primary_color": {"name": "primary_color", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'#4F46E5'"}, "secondary_color": {"name": "secondary_color", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'#FFFFFF'"}, "header_text": {"name": "header_text", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "'Chat Assistant'"}, "welcome_message": {"name": "welcome_message", "type": "text", "primaryKey": false, "notNull": false, "default": "'Hi there! How can I help you today?'"}, "position": {"name": "position", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'BOTTOM_RIGHT'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_configurations_website_id_websites_id_fk": {"name": "chat_configurations_website_id_websites_id_fk", "tableFrom": "chat_configurations", "tableTo": "websites", "columnsFrom": ["website_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.conversations": {"name": "conversations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "website_id": {"name": "website_id", "type": "uuid", "primaryKey": false, "notNull": true}, "visitor_id": {"name": "visitor_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'active'"}, "ended_at": {"name": "ended_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "referring_url": {"name": "referring_url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "device_info": {"name": "device_info", "type": "jsonb", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": false}, "feedback": {"name": "feedback", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"conversations_website_id_websites_id_fk": {"name": "conversations_website_id_websites_id_fk", "tableFrom": "conversations", "tableTo": "websites", "columnsFrom": ["website_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "clerk_id": {"name": "clerk_id", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "plan_id": {"name": "plan_id", "type": "uuid", "primaryKey": false, "notNull": false}, "subscription_status": {"name": "subscription_status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'FREE'"}, "subscription_expires_at": {"name": "subscription_expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"users_plan_id_plans_id_fk": {"name": "users_plan_id_plans_id_fk", "tableFrom": "users", "tableTo": "plans", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_clerk_id_unique": {"name": "users_clerk_id_unique", "nullsNotDistinct": false, "columns": ["clerk_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.websites": {"name": "websites", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "last_crawled_at": {"name": "last_crawled_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "crawl_frequency": {"name": "crawl_frequency", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'WEEKLY'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'ACTIVE'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.messages": {"name": "messages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "conversation_id": {"name": "conversation_id", "type": "uuid", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "sources": {"name": "sources", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"messages_conversation_id_conversations_id_fk": {"name": "messages_conversation_id_conversations_id_fk", "tableFrom": "messages", "tableTo": "conversations", "columnsFrom": ["conversation_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.website_pages": {"name": "website_pages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "website_id": {"name": "website_id", "type": "uuid", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "last_crawled_at": {"name": "last_crawled_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"website_pages_website_id_websites_id_fk": {"name": "website_pages_website_id_websites_id_fk", "tableFrom": "website_pages", "tableTo": "websites", "columnsFrom": ["website_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.website_embeddings": {"name": "website_embeddings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "website_id": {"name": "website_id", "type": "uuid", "primaryKey": false, "notNull": true}, "page_id": {"name": "page_id", "type": "uuid", "primaryKey": false, "notNull": true}, "chunk_text": {"name": "chunk_text", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(1024)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"website_embeddings_website_id_websites_id_fk": {"name": "website_embeddings_website_id_websites_id_fk", "tableFrom": "website_embeddings", "tableTo": "websites", "columnsFrom": ["website_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "website_embeddings_page_id_website_pages_id_fk": {"name": "website_embeddings_page_id_website_pages_id_fk", "tableFrom": "website_embeddings", "tableTo": "website_pages", "columnsFrom": ["page_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.plans": {"name": "plans", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": true}, "website_limit": {"name": "website_limit", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "pages_per_website_limit": {"name": "pages_per_website_limit", "type": "integer", "primaryKey": false, "notNull": true, "default": 100}, "messages_per_day_limit": {"name": "messages_per_day_limit", "type": "integer", "primaryKey": false, "notNull": true, "default": 100}, "features": {"name": "features", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'ACTIVE'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}