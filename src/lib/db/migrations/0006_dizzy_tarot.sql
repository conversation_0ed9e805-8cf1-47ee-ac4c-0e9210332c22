CREATE TABLE "rag_metrics" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"website_id" uuid NOT NULL,
	"timestamp" timestamp with time zone NOT NULL,
	"search_latency" real NOT NULL,
	"search_results_count" integer NOT NULL,
	"average_similarity" real NOT NULL,
	"cache_hit_rate" real NOT NULL,
	"total_chunks" integer NOT NULL,
	"average_chunk_size" real NOT NULL,
	"chunking_latency" real NOT NULL,
	"embedding_latency" real NOT NULL,
	"embedding_dimensions" integer NOT NULL,
	"error_count" integer NOT NULL,
	"error_types" jsonb NOT NULL
);
