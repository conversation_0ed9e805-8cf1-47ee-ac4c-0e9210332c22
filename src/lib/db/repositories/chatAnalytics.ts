import { db } from "@/lib/db";
import {
	type NewChatAnalyticsEvent,
	type NewChatPerformanceMetric,
	chatAnalyticsEventsTable,
	chatPerformanceMetricsTable,
} from "@/lib/db/schema";
import { eq, sql } from "drizzle-orm";

export class ChatAnalyticsRepository {
	/**
	 * Create a new chat analytics event
	 */
	async createEvent(event: NewChatAnalyticsEvent): Promise<string> {
		console.log("🔍 [ANALYTICS] Creating event in repository:", event);
		try {
			const [result] = await db
				.insert(chatAnalyticsEventsTable)
				.values(event)
				.returning({ id: chatAnalyticsEventsTable.id });

			console.log("🔍 [ANALYTICS] Event created with ID:", result.id);
			return result.id;
		} catch (error) {
			console.error("🔍 [ANALYTICS] Error creating event:", error);
			throw error;
		}
	}

	/**
	 * Create a new chat performance metric
	 */
	async createPerformanceMetric(
		metric: NewChatPerformanceMetric,
	): Promise<string> {
		console.log(
			"🔍 [ANALYTICS] Creating performance metric in repository:",
			metric,
		);
		try {
			const [result] = await db
				.insert(chatPerformanceMetricsTable)
				.values(metric)
				.returning({ id: chatPerformanceMetricsTable.id });

			console.log(
				"🔍 [ANALYTICS] Performance metric created with ID:",
				result.id,
			);
			return result.id;
		} catch (error) {
			console.error("🔍 [ANALYTICS] Error creating performance metric:", error);
			throw error;
		}
	}

	/**
	 * Get chat usage statistics for a website
	 */
	async getWebsiteStats(websiteId: string): Promise<{
		totalConversations: number;
		totalMessages: number;
		averageResponseTime: number;
		ragUsagePercentage: number;
		widgetLoads: { count: number; avgLoadDuration: number };
		widgetEngagements: { count: number; avgDuration: number };
		errors: { count: number; recent: string[] };
		widgetFID: { avg: number };
		widgetTTI: { avg: number };
		webVitals: {
			cls: { avg: number };
			lcp: { avg: number };
			fcp: { avg: number };
			ttfb: { avg: number };
			fid: { avg: number };
		};
		networkInfo: {
			avgDownlink: number;
			avg4gPercentage: number;
			avg3gPercentage: number;
			avgRtt: number;
		};
	}> {
		// Get total conversations
		const [conversationResult] = await db
			.select({
				total: sql<number>`COUNT(DISTINCT ${chatAnalyticsEventsTable.conversationId})`,
			})
			.from(chatAnalyticsEventsTable)
			.where(
				eq(chatAnalyticsEventsTable.websiteId, websiteId) &&
					eq(chatAnalyticsEventsTable.eventType, "conversation_started"),
			);

		const totalConversations = conversationResult?.total || 0;

		// Get total messages
		const [messageResult] = await db
			.select({
				total: sql<number>`COUNT(*)`,
			})
			.from(chatAnalyticsEventsTable)
			.where(
				eq(chatAnalyticsEventsTable.websiteId, websiteId) &&
					sql`${chatAnalyticsEventsTable.eventType} IN ('message_sent', 'message_received')`,
			);

		const totalMessages = messageResult?.total || 0;

		// Get average response time
		const [responseTimeResult] = await db
			.select({
				average: sql<number>`AVG(${chatPerformanceMetricsTable.responseTime})`,
			})
			.from(chatPerformanceMetricsTable)
			.where(eq(chatPerformanceMetricsTable.websiteId, websiteId));

		const averageResponseTime = responseTimeResult?.average || 0;

		// Get RAG usage percentage
		const [ragResult] = await db
			.select({
				total: sql<number>`COUNT(*)`,
				ragUsed: sql<number>`SUM(CASE WHEN ${chatPerformanceMetricsTable.ragUsed} = true THEN 1 ELSE 0 END)`,
			})
			.from(chatPerformanceMetricsTable)
			.where(eq(chatPerformanceMetricsTable.websiteId, websiteId));

		const total = ragResult?.total || 0;
		const ragUsed = ragResult?.ragUsed || 0;
		const ragUsagePercentage = total > 0 ? (ragUsed / total) * 100 : 0;

		// Widget Loads
		const [widgetLoadResult] = await db
			.select({
				count: sql<number>`COUNT(*)`,
				avgLoadDuration: sql<number>`AVG((metadata::jsonb->>'loadDuration')::float)`,
			})
			.from(chatAnalyticsEventsTable)
			.where(
				eq(chatAnalyticsEventsTable.websiteId, websiteId) &&
					eq(chatAnalyticsEventsTable.eventType, "widget_loaded"),
			);
		const widgetLoads = {
			count: widgetLoadResult?.count || 0,
			avgLoadDuration: widgetLoadResult?.avgLoadDuration || 0,
		};

		// Widget Engagements
		const [widgetEngagementResult] = await db
			.select({
				count: sql<number>`COUNT(*)`,
				avgDuration: sql<number>`AVG((metadata::jsonb->>'duration')::float)`,
			})
			.from(chatAnalyticsEventsTable)
			.where(
				eq(chatAnalyticsEventsTable.websiteId, websiteId) &&
					eq(chatAnalyticsEventsTable.eventType, "widget_engagement"),
			);
		const widgetEngagements = {
			count: widgetEngagementResult?.count || 0,
			avgDuration: widgetEngagementResult?.avgDuration || 0,
		};

		// Errors
		const [errorCountResult] = await db
			.select({ count: sql<number>`COUNT(*)` })
			.from(chatAnalyticsEventsTable)
			.where(
				eq(chatAnalyticsEventsTable.websiteId, websiteId) &&
					eq(chatAnalyticsEventsTable.eventType, "error"),
			);
		const errorCount = errorCountResult?.count || 0;
		const recentErrorsRows = await db
			.select({ metadata: chatAnalyticsEventsTable.metadata })
			.from(chatAnalyticsEventsTable)
			.where(
				eq(chatAnalyticsEventsTable.websiteId, websiteId) &&
					eq(chatAnalyticsEventsTable.eventType, "error"),
			)
			.orderBy(sql`timestamp DESC`)
			.limit(5);
		const recentErrors = recentErrorsRows.map((row) => {
			try {
				const meta = JSON.parse(row.metadata || "{}");
				return meta.message || JSON.stringify(meta);
			} catch {
				return row.metadata;
			}
		});

		// Widget FID
		const [fidResult] = await db
			.select({ avg: sql<number>`AVG((metadata::jsonb->>'fid')::float)` })
			.from(chatAnalyticsEventsTable)
			.where(
				eq(chatAnalyticsEventsTable.websiteId, websiteId) &&
					eq(chatAnalyticsEventsTable.eventType, "widget_fid"),
			);
		const widgetFID = { avg: fidResult?.avg || 0 };

		// Widget TTI
		const [ttiResult] = await db
			.select({ avg: sql<number>`AVG((metadata::jsonb->>'value')::float)` })
			.from(chatAnalyticsEventsTable)
			.where(
				eq(chatAnalyticsEventsTable.websiteId, websiteId) &&
					eq(chatAnalyticsEventsTable.eventType, "widget_tti"),
			);
		const widgetTTI = { avg: ttiResult?.avg || 0 };

		// Web Vitals - CLS
		const [clsResult] = await db
			.select({ avg: sql<number>`AVG((metadata::jsonb->>'value')::float)` })
			.from(chatAnalyticsEventsTable)
			.where(
				eq(chatAnalyticsEventsTable.websiteId, websiteId) &&
					eq(chatAnalyticsEventsTable.eventType, "widget_cls"),
			);

		// Web Vitals - LCP
		const [lcpResult] = await db
			.select({ avg: sql<number>`AVG((metadata::jsonb->>'value')::float)` })
			.from(chatAnalyticsEventsTable)
			.where(
				eq(chatAnalyticsEventsTable.websiteId, websiteId) &&
					eq(chatAnalyticsEventsTable.eventType, "widget_lcp"),
			);

		// Web Vitals - FCP
		const [fcpResult] = await db
			.select({ avg: sql<number>`AVG((metadata::jsonb->>'value')::float)` })
			.from(chatAnalyticsEventsTable)
			.where(
				eq(chatAnalyticsEventsTable.websiteId, websiteId) &&
					eq(chatAnalyticsEventsTable.eventType, "widget_fcp"),
			);

		// Web Vitals - TTFB
		const [ttfbResult] = await db
			.select({ avg: sql<number>`AVG((metadata::jsonb->>'value')::float)` })
			.from(chatAnalyticsEventsTable)
			.where(
				eq(chatAnalyticsEventsTable.websiteId, websiteId) &&
					eq(chatAnalyticsEventsTable.eventType, "widget_ttfb"),
			);

		// Network Information
		const networkRows = await db
			.select({
				downlink: sql<number>`(metadata::jsonb->>'downlink')::float`,
				effectiveType: sql<string>`metadata::jsonb->>'effectiveType'`,
				rtt: sql<number>`(metadata::jsonb->>'rtt')::float`,
			})
			.from(chatAnalyticsEventsTable)
			.where(
				eq(chatAnalyticsEventsTable.websiteId, websiteId) &&
					eq(chatAnalyticsEventsTable.eventType, "widget_network"),
			);

		// Calculate network stats
		let totalDownlink = 0;
		let totalRtt = 0;
		let count4g = 0;
		let count3g = 0;
		const totalNetworkEntries = networkRows.length;

		for (const row of networkRows) {
			if (row.downlink) totalDownlink += row.downlink;
			if (row.rtt) totalRtt += row.rtt;
			if (row.effectiveType === "4g") count4g++;
			if (row.effectiveType === "3g") count3g++;
		}

		const avgDownlink =
			totalNetworkEntries > 0 ? totalDownlink / totalNetworkEntries : 0;
		const avgRtt = totalNetworkEntries > 0 ? totalRtt / totalNetworkEntries : 0;
		const avg4gPercentage =
			totalNetworkEntries > 0 ? (count4g / totalNetworkEntries) * 100 : 0;
		const avg3gPercentage =
			totalNetworkEntries > 0 ? (count3g / totalNetworkEntries) * 100 : 0;

		return {
			totalConversations,
			totalMessages,
			averageResponseTime,
			ragUsagePercentage,
			widgetLoads,
			widgetEngagements,
			errors: { count: errorCount, recent: recentErrors },
			widgetFID,
			widgetTTI,
			webVitals: {
				cls: { avg: clsResult?.avg || 0 },
				lcp: { avg: lcpResult?.avg || 0 },
				fcp: { avg: fcpResult?.avg || 0 },
				ttfb: { avg: ttfbResult?.avg || 0 },
				fid: { avg: widgetFID.avg || 0 },
			},
			networkInfo: {
				avgDownlink,
				avg4gPercentage,
				avg3gPercentage,
				avgRtt,
			},
		};
	}
}
