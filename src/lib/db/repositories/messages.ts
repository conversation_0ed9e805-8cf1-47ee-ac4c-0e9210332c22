import { count, desc, eq } from "drizzle-orm";
import { db } from "../index";
import { messagesTable } from "../schema/messages";
import type { Message, NewMessage } from "../schema/messages";
export class MessagesRepository {
	/**
	 * Create a new message
	 */
	async create(message: NewMessage): Promise<Message> {
		const [newMessage] = await db
			.insert(messagesTable)
			.values(message)
			.returning();

		return newMessage as Message;
	}

	/**
	 * Get a message by ID
	 */
	async getById(id: string): Promise<Message | undefined> {
		const [message] = await db
			.select()
			.from(messagesTable)
			.where(eq(messagesTable.id, id));

		return message as Message | undefined;
	}

	/**
	 * Get messages by conversation ID
	 */
	async getByConversationId(
		conversationId: string,
		limit = 50,
		offset = 0,
	): Promise<Message[]> {
		const messages = await db
			.select()
			.from(messagesTable)
			.where(eq(messagesTable.conversationId, conversationId))
			.limit(limit)
			.offset(offset)
			.orderBy(desc(messagesTable.createdAt));

		return messages as Message[];
	}

	/**
	 * Update a message
	 */
	async update(id: string, message: Partial<NewMessage>): Promise<Message> {
		const [updatedMessage] = await db
			.update(messagesTable)
			.set({
				...message,
			})
			.where(eq(messagesTable.id, id))
			.returning();

		return updatedMessage as Message;
	}

	/**
	 * Delete a message
	 */
	async delete(id: string): Promise<void> {
		await db.delete(messagesTable).where(eq(messagesTable.id, id));
	}

	/**
	 * Count messages by conversation ID
	 */
	async countByConversationId(conversationId: string): Promise<number> {
		const result = await db
			.select({ count: count() })
			.from(messagesTable)
			.where(eq(messagesTable.conversationId, conversationId));

		return Number(result[0]?.count || 0);
	}
}
