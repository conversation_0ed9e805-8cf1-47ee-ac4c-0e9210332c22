import { and, eq } from "drizzle-orm";
import { db } from "../index";
import { chatConfigurationsTable } from "../schema/chatConfigurations";
import type {
	ChatConfiguration,
	NewChatConfiguration,
} from "../schema/chatConfigurations";

export class ChatConfigurationsRepository {
	/**
	 * Create a new chat configuration
	 */
	async create(
		chatConfiguration: NewChatConfiguration,
	): Promise<ChatConfiguration> {
		const [newChatConfiguration] = await db
			.insert(chatConfigurationsTable)
			.values(chatConfiguration)
			.returning();

		return newChatConfiguration as ChatConfiguration;
	}

	/**
	 * Get a chat configuration by ID
	 */
	async getById(id: string): Promise<ChatConfiguration | undefined> {
		const [chatConfiguration] = await db
			.select()
			.from(chatConfigurationsTable)
			.where(eq(chatConfigurationsTable.id, id));

		return chatConfiguration as ChatConfiguration | undefined;
	}

	/**
	 * Get a chat configuration by website ID
	 * Returns the active configuration if there are multiple
	 */
	async getByWebsiteId(
		websiteId: string,
	): Promise<ChatConfiguration | undefined> {
		const [chatConfiguration] = await db
			.select()
			.from(chatConfigurationsTable)
			.where(
				and(
					eq(chatConfigurationsTable.websiteId, websiteId),
					eq(chatConfigurationsTable.isActive, true),
				),
			);

		return chatConfiguration as ChatConfiguration | undefined;
	}

	/**
	 * Get all chat configurations for a website
	 */
	async getAllByWebsiteId(websiteId: string): Promise<ChatConfiguration[]> {
		const chatConfigurations = await db
			.select()
			.from(chatConfigurationsTable)
			.where(eq(chatConfigurationsTable.websiteId, websiteId));

		return chatConfigurations as ChatConfiguration[];
	}

	/**
	 * Update a chat configuration
	 */
	async update(
		id: string,
		chatConfiguration: Partial<NewChatConfiguration>,
	): Promise<ChatConfiguration> {
		console.log("Updating chat configuration:", id);
		console.log("Update data:", JSON.stringify(chatConfiguration));

		try {
			const [updatedChatConfiguration] = await db
				.update(chatConfigurationsTable)
				.set({
					...chatConfiguration,
					updatedAt: new Date().toISOString(),
				})
				.where(eq(chatConfigurationsTable.id, id))
				.returning();

			console.log("Updated chat configuration:", updatedChatConfiguration);
			return updatedChatConfiguration as ChatConfiguration;
		} catch (error) {
			console.error("Error updating chat configuration:", error);
			throw error;
		}
	}

	/**
	 * Delete a chat configuration
	 */
	async delete(id: string): Promise<void> {
		await db
			.delete(chatConfigurationsTable)
			.where(eq(chatConfigurationsTable.id, id));
	}

	/**
	 * Create a default chat configuration for a website if one doesn't exist
	 */
	async createDefaultIfNotExists(
		websiteId: string,
	): Promise<ChatConfiguration> {
		// Check if a configuration already exists
		const existing = await this.getByWebsiteId(websiteId);
		if (existing) {
			return existing;
		}

		// Create a default configuration
		const defaultConfig: NewChatConfiguration = {
			websiteId,
			name: "Default Configuration",
			primaryColor: "#4F46E5",
			secondaryColor: "#FFFFFF",
			headerText: "Bubl Assistant",
			welcomeMessage: "Hi there! How can I help you today?",
			position: "BOTTOM_RIGHT",
			isActive: true,
			allowedDomains: [],
		};

		return this.create(defaultConfig);
	}
}
