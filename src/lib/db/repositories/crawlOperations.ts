import { count, desc, eq, sql } from "drizzle-orm";
import { db } from "../index";
import { crawlOperationsTable } from "../schema/crawlOperations";
import type {
	CrawlOperation,
	NewCrawlOperation,
} from "../schema/crawlOperations";

export class CrawlOperationsRepository {
	/**
	 * Create a new crawl operation
	 */
	async create(operation: NewCrawlOperation): Promise<CrawlOperation> {
		const [newOperation] = await db
			.insert(crawlOperationsTable)
			// biome-ignore lint/suspicious/noExplicitAny: Using any type for configuration JSON
			.values(operation as any)
			.returning();

		// biome-ignore lint/suspicious/noExplicitAny: Using any type for configuration JSON
		return newOperation as any;
	}

	/**
	 * Get a crawl operation by ID
	 */
	async getById(id: string): Promise<CrawlOperation | undefined> {
		const [operation] = await db
			.select()
			.from(crawlOperationsTable)
			.where(eq(crawlOperationsTable.id, id));

		// biome-ignore lint/suspicious/noExplicitAny: Using any type for configuration JSO<PERSON>
		return operation as any;
	}

	/**
	 * Get crawl operations by website ID with pagination
	 */
	async getByWebsiteId(
		websiteId: string,
		page = 1,
		pageSize = 10,
	): Promise<{ operations: CrawlOperation[]; total: number }> {
		// Calculate offset
		const offset = (page - 1) * pageSize;

		// Get operations with pagination
		const operations = await db
			.select()
			.from(crawlOperationsTable)
			.where(eq(crawlOperationsTable.websiteId, websiteId))
			.limit(pageSize)
			.offset(offset)
			.orderBy(desc(crawlOperationsTable.createdAt));

		// Get total count for pagination
		const [{ count: totalCount }] = await db
			.select({ count: count() })
			.from(crawlOperationsTable)
			.where(eq(crawlOperationsTable.websiteId, websiteId));

		return {
			operations: operations as CrawlOperation[],
			total: Number(totalCount),
		};
	}

	/**
	 * Update a crawl operation
	 */
	async update(
		id: string,
		operation: Partial<NewCrawlOperation>,
	): Promise<CrawlOperation> {
		const [updatedOperation] = await db
			.update(crawlOperationsTable)
			.set({
				...operation,
				updatedAt: new Date().toISOString(),
			})
			.where(eq(crawlOperationsTable.id, id))
			.returning();

		// biome-ignore lint/suspicious/noExplicitAny: Using any type for configuration JSON
		return updatedOperation as any;
	}

	/**
	 * Delete a crawl operation
	 */
	async delete(id: string): Promise<void> {
		await db
			.delete(crawlOperationsTable)
			.where(eq(crawlOperationsTable.id, id));
	}

	/**
	 * Increment a numeric field
	 */
	async incrementField(
		id: string,
		field: "pagesProcessed" | "pagesSucceeded" | "pagesFailed",
	): Promise<void> {
		// Create a type-safe mapping of field names to their corresponding table columns
		// Using a more specific type for the field parameter ensures type safety
		type IntegerColumn = { name: string };

		const fieldMap: Record<string, IntegerColumn> = {
			pagesProcessed: crawlOperationsTable.pagesProcessed,
			pagesSucceeded: crawlOperationsTable.pagesSucceeded,
			pagesFailed: crawlOperationsTable.pagesFailed,
		};

		// Use the mapped column for the update
		await db
			.update(crawlOperationsTable)
			.set({
				[field]: sql`${fieldMap[field]} + 1`,
				updatedAt: new Date().toISOString(),
			})
			.where(eq(crawlOperationsTable.id, id));
	}
}

export const crawlOperationsRepository = new CrawlOperationsRepository();
