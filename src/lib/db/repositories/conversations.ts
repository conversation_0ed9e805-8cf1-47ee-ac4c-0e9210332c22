import { count, desc, eq } from "drizzle-orm";
import { db } from "../index";
import { conversationsTable } from "../schema/conversations";
import type { Conversation, NewConversation } from "../schema/conversations";
export class ConversationsRepository {
	/**
	 * Create a new conversation
	 */
	async create(conversation: NewConversation): Promise<Conversation> {
		const [newConversation] = await db
			.insert(conversationsTable)
			.values(conversation)
			.returning();

		return newConversation as Conversation;
	}

	/**
	 * Get a conversation by ID
	 */
	async getById(id: string): Promise<Conversation | undefined> {
		const [conversation] = await db
			.select()
			.from(conversationsTable)
			.where(eq(conversationsTable.id, id));

		return conversation as Conversation | undefined;
	}

	/**
	 * Get conversations by website ID
	 */
	async getByWebsiteId(websiteId: string, limit = 10, offset = 0) {
		const conversations = await db
			.select()
			.from(conversationsTable)
			.where(eq(conversationsTable.websiteId, websiteId))
			.limit(limit)
			.offset(offset)
			.orderBy(desc(conversationsTable.createdAt));

		return conversations;
	}

	/**
	 * Get conversations by visitor ID
	 */
	async getByVisitorId(
		visitorId: string,
		limit = 10,
		offset = 0,
	): Promise<Conversation[]> {
		const conversations = await db
			.select()
			.from(conversationsTable)
			.where(eq(conversationsTable.visitorId, visitorId))
			.limit(limit)
			.offset(offset)
			.orderBy(desc(conversationsTable.createdAt));

		return conversations as Conversation[];
	}

	/**
	 * Update a conversation
	 */
	async update(
		id: string,
		conversation: Partial<NewConversation>,
	): Promise<Conversation> {
		const [updatedConversation] = await db
			.update(conversationsTable)
			.set({
				...conversation,
				updatedAt: new Date().toISOString(),
			})
			.where(eq(conversationsTable.id, id))
			.returning();

		return updatedConversation as Conversation;
	}

	/**
	 * Delete a conversation
	 */
	async delete(id: string): Promise<void> {
		await db.delete(conversationsTable).where(eq(conversationsTable.id, id));
	}

	/**
	 * List conversations with pagination
	 */
	async list(limit = 10, offset = 0) {
		const conversations = await db
			.select()
			.from(conversationsTable)
			.limit(limit)
			.offset(offset)
			.orderBy(desc(conversationsTable.createdAt));

		return conversations;
	}

	/**
	 * Count conversations by website ID
	 */
	async countByWebsiteId(websiteId: string): Promise<number> {
		const result = await db
			.select({ count: count() })
			.from(conversationsTable)
			.where(eq(conversationsTable.websiteId, websiteId));

		return Number(result[0]?.count || 0);
	}
}
