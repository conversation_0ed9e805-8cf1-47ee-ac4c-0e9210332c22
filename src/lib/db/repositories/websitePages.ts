import { count, desc, eq } from "drizzle-orm";
import { db } from "../index";
import { websitePagesTable } from "../schema/websitePages";
import type { NewWebsitePage, WebsitePage } from "../schema/websitePages";

export class WebsitePagesRepository {
	/**
	 * Create a new website page
	 */
	async create(page: NewWebsitePage): Promise<WebsitePage> {
		const [newPage] = await db
			.insert(websitePagesTable)
			// biome-ignore lint/suspicious/noExplicitAny: Using any type for metadata JSON
			.values(page as any)
			.returning();

		// biome-ignore lint/suspicious/noExplicitAny: Using any type for metadata JSON
		return newPage as any;
	}

	/**
	 * Get a website page by ID
	 */
	async getById(id: string): Promise<WebsitePage | undefined> {
		const [page] = await db
			.select()
			.from(websitePagesTable)
			.where(eq(websitePagesTable.id, id));

		// biome-ignore lint/suspicious/noExplicitAny: Using any type for metadata JSON
		return page as any;
	}

	/**
	 * Get website pages by website ID
	 */
	async getByWebsiteId(websiteId: string, limit = 100, offset = 0) {
		const pages = await db
			.select()
			.from(websitePagesTable)
			.where(eq(websitePagesTable.websiteId, websiteId))
			.limit(limit)
			.offset(offset)
			.orderBy(desc(websitePagesTable.lastCrawledAt));

		return pages;
	}

	/**
	 * Update a website page
	 */
	async update(
		id: string,
		page: Partial<NewWebsitePage>,
	): Promise<WebsitePage> {
		const [updatedPage] = await db
			.update(websitePagesTable)
			.set({
				...page,
				updatedAt: new Date().toISOString(),
			})
			.where(eq(websitePagesTable.id, id))
			.returning();

		// biome-ignore lint/suspicious/noExplicitAny: Using any type for metadata JSON
		return updatedPage as any;
	}

	/**
	 * Delete a website page
	 */
	async delete(id: string): Promise<void> {
		await db.delete(websitePagesTable).where(eq(websitePagesTable.id, id));
	}

	/**
	 * Get a website page by URL
	 */
	async getByUrl(
		websiteId: string,
		url: string,
	): Promise<WebsitePage | undefined> {
		const [page] = await db
			.select()
			.from(websitePagesTable)
			.where(
				eq(websitePagesTable.websiteId, websiteId) &&
					eq(websitePagesTable.url, url),
			);

		// biome-ignore lint/suspicious/noExplicitAny: Using any type for metadata JSON
		return page as any;
	}

	/**
	 * Delete all pages for a website
	 */
	async deleteByWebsiteId(websiteId: string): Promise<void> {
		await db
			.delete(websitePagesTable)
			.where(eq(websitePagesTable.websiteId, websiteId));
	}

	/**
	 * Count pages by website ID
	 */
	async countByWebsiteId(websiteId: string): Promise<number> {
		const result = await db
			.select({ count: count() })
			.from(websitePagesTable)
			.where(eq(websitePagesTable.websiteId, websiteId));

		return Number(result[0]?.count || 0);
	}
}
