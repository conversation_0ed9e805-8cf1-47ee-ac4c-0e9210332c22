import { and, desc, eq } from "drizzle-orm";
import { db } from "../index";
import type { NewUser, User } from "../schema/users";
import { usersTable } from "../schema/users";

export class UsersRepository {
	/**
	 * Create a new user
	 */
	async create(user: NewUser): Promise<User> {
		const [newUser] = await db.insert(usersTable).values(user).returning();

		return newUser as User;
	}

	/**
	 * Get a user by ID
	 */
	async getById(id: string): Promise<User | undefined> {
		const [user] = await db
			.select()
			.from(usersTable)
			.where(eq(usersTable.id, id));

		return user as User | undefined;
	}

	/**
	 * Get a user by Clerk ID
	 */
	async getByClerkId(clerkId: string): Promise<User | undefined> {
		const [user] = await db
			.select()
			.from(usersTable)
			.where(eq(usersTable.clerkId, clerkId));

		return user as User | undefined;
	}

	/**
	 * Update a user
	 */
	async update(id: string, user: Partial<NewUser>): Promise<User> {
		const [updatedUser] = await db
			.update(usersTable)
			.set({
				...user,
				updatedAt: new Date().toISOString(),
			})
			.where(eq(usersTable.id, id))
			.returning();

		return updatedUser as User;
	}

	/**
	 * Delete a user
	 */
	async delete(id: string): Promise<void> {
		await db.delete(usersTable).where(eq(usersTable.id, id));
	}

	/**
	 * List users with pagination
	 */
	async list(limit = 10, offset = 0): Promise<User[]> {
		const users = await db
			.select()
			.from(usersTable)
			.limit(limit)
			.offset(offset)
			.orderBy(desc(usersTable.createdAt));

		return users as User[];
	}
}
