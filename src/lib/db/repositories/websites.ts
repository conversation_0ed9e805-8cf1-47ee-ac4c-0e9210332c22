import type { CrawlConfig } from "@/components/dashboard/CrawlConfigurationForm";
import { desc, eq } from "drizzle-orm";
import { db } from "../index";
import { websitesTable } from "../schema/websites";
import type { NewWebsite, Website } from "../schema/websites";
import type { WebsiteStatus } from "../schema/websites";

export class WebsitesRepository {
	/**
	 * Create a new website
	 */
	async create(website: NewWebsite): Promise<Website> {
		const [newWebsite] = await db
			.insert(websitesTable)
			.values(website)
			.returning();

		return newWebsite as Website;
	}

	/**
	 * Get a website by ID
	 */
	async getById(id: string): Promise<Website | undefined> {
		const [website] = await db
			.select()
			.from(websitesTable)
			.where(eq(websitesTable.id, id));

		return website as Website | undefined;
	}

	/**
	 * Get websites by organization ID
	 */

	/**
	 * Update a website
	 */
	async update(id: string, website: Partial<NewWebsite>): Promise<Website> {
		const [updatedWebsite] = await db
			.update(websitesTable)
			.set({
				...website,
				updatedAt: new Date().toISOString(),
			})
			.where(eq(websitesTable.id, id))
			.returning();

		return updatedWebsite as Website;
	}

	/**
	 * Delete a website
	 */
	async delete(id: string): Promise<void> {
		await db.delete(websitesTable).where(eq(websitesTable.id, id));
	}

	/**
	 * Save crawl configuration for a website
	 */
	async saveCrawlConfiguration(
		id: string,
		configuration: CrawlConfig,
	): Promise<Website> {
		const [updatedWebsite] = await db
			.update(websitesTable)
			.set({
				// biome-ignore lint/suspicious/noExplicitAny: Using any type for JSON data
				crawlConfiguration: configuration as any,
				updatedAt: new Date().toISOString(),
			})
			.where(eq(websitesTable.id, id))
			.returning();

		return updatedWebsite as Website;
	}

	/**
	 * Get crawl configuration for a website
	 */
	async getCrawlConfiguration(id: string): Promise<CrawlConfig | null> {
		const website = await this.getById(id);
		return (website?.crawlConfiguration ?? null) as CrawlConfig | null;
	}

	/**
	 * List websites with pagination
	 */
	async list(limit = 10, offset = 0): Promise<Website[]> {
		const websites = await db
			.select()
			.from(websitesTable)
			.limit(limit)
			.offset(offset)
			.orderBy(desc(websitesTable.createdAt));

		return websites as Website[];
	}

	/**
	 * Get websites by crawl frequency
	 */
	async getWebsitesByCrawlFrequency(frequency: string): Promise<Website[]> {
		const websites = await db
			.select()
			.from(websitesTable)
			.where(eq(websitesTable.crawlFrequency, frequency))
			.orderBy(desc(websitesTable.createdAt));

		return websites as Website[];
	}

	/**
	 * Update website status
	 */
	async updateStatus(id: string, status: WebsiteStatus): Promise<Website> {
		const [updatedWebsite] = await db
			.update(websitesTable)
			.set({
				status,
				updatedAt: new Date().toISOString(),
			})
			.where(eq(websitesTable.id, id))
			.returning();

		return updatedWebsite as Website;
	}

	/**
	 * Get all active websites
	 */
	async getAllActive(): Promise<Website[]> {
		const websites = await db
			.select()
			.from(websitesTable)
			.where(eq(websitesTable.status, "ACTIVE"))
			.orderBy(desc(websitesTable.createdAt));

		return websites as Website[];
	}
}
