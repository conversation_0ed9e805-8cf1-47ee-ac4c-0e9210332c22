export * from "./users";
export * from "./websites";
export * from "./conversations";
export * from "./messages";
export * from "./websitePages";
export * from "./chatConfigurations";
export * from "./chatAnalytics";
export * from "./crawlOperations";

import { ChatAnalyticsRepository } from "./chatAnalytics";
import { ChatConfigurationsRepository } from "./chatConfigurations";
import { ConversationsRepository } from "./conversations";
import { CrawlOperationsRepository } from "./crawlOperations";
import { MessagesRepository } from "./messages";
// Create singleton instances of each repository
import { UsersRepository } from "./users";
import { WebsitePagesRepository } from "./websitePages";
import { WebsitesRepository } from "./websites";

export const usersRepository = new UsersRepository();
export const websitesRepository = new WebsitesRepository();
export const conversationsRepository = new ConversationsRepository();
export const messagesRepository = new MessagesRepository();
export const websitePagesRepository = new WebsitePagesRepository();
export const chatConfigurationsRepository = new ChatConfigurationsRepository();
export const chatAnalyticsRepository = new ChatAnalyticsRepository();
export const crawlOperationsRepository = new CrawlOperationsRepository();
