import { sql } from "drizzle-orm";
import { customType } from "drizzle-orm/pg-core";

// Define a custom pgvector type for Drizzle ORM
export const pgVector = customType<{
	data: number[];
	driverData: string;
	config: { dimensions: number };
}>({
	dataType(config) {
		return `vector(${config?.dimensions ?? 1024})`;
	},
	toDriver(value: number[]): string {
		return `[${value.join(",")}]`;
	},
	fromDriver(value: string): number[] {
		// Remove brackets and split by comma
		return value
			.substring(1, value.length - 1)
			.split(",")
			.map(Number);
	},
});
