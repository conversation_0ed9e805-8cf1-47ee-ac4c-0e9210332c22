import { integer, pgTable, varchar } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import type { z } from "zod";
import { createdAt, id, updatedAt } from "../schema-helper";

// Define the subscription plans table
export const plansTable = pgTable("plans", {
	id,
	name: varchar("name", { length: 100 }).notNull(),
	description: varchar("description", { length: 500 }),
	price: integer("price").notNull(), // Price in cents
	websiteLimit: integer("website_limit").notNull().default(1),
	pagesPerWebsiteLimit: integer("pages_per_website_limit")
		.notNull()
		.default(100),
	messagesPerDayLimit: integer("messages_per_day_limit").notNull().default(100),
	features: varchar("features", { length: 1000 }),
	isActive: varchar("is_active", { length: 50 }).default("ACTIVE"),
	createdAt,
	updatedAt,
});

// Create Zod schemas for validation
export const PlanSchema = createSelectSchema(plansTable);
export const InsertPlanSchema = createInsertSchema(plansTable);
export const NewPlanSchema = InsertPlanSchema.omit({
	id: true,
	createdAt: true,
	updatedAt: true,
});

// Types for the plan
export type Plan = z.infer<typeof PlanSchema>;
export type NewPlan = z.infer<typeof NewPlanSchema>;
