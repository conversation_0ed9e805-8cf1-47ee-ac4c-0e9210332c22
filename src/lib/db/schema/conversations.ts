import { relations } from "drizzle-orm";
import {
	integer,
	jsonb,
	pgTable,
	text,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import type { z } from "zod";
import { createdAt, id, updatedAt } from "../schema-helper";
import { messagesTable } from "./messages";
import { websitesTable } from "./websites";

export const conversationsTable = pgTable("conversations", {
	id,
	websiteId: uuid("website_id")
		.references(() => websitesTable.id, { onDelete: "cascade" })
		.notNull(),
	visitorId: varchar("visitor_id", { length: 255 }).notNull(),
	status: varchar("status", { length: 50 }).notNull().default("active"),
	endedAt: timestamp("ended_at", { withTimezone: true, mode: "string" }),
	referringUrl: varchar("referring_url", { length: 2048 }),
	deviceInfo: jsonb("device_info"),
	rating: integer("rating"),
	feedback: text("feedback"),
	createdAt,
	updatedAt,
});

// Create Zod schemas for validation
export const ConversationSchema = createSelectSchema(conversationsTable);
export const InsertConversationSchema = createInsertSchema(conversationsTable);
export const NewConversationSchema = InsertConversationSchema.partial({
	id: true,
});

// Types for the conversation
export type Conversation = z.infer<typeof ConversationSchema>;
export type NewConversation = z.infer<typeof NewConversationSchema>;
