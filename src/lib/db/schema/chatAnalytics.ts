import {
	boolean,
	integer,
	pgTable,
	text,
	timestamp,
	uuid,
} from "drizzle-orm/pg-core";
import { id } from "../schema-helper";
import { websitesTable } from "./websites";

/**
 * Table for storing chat analytics events
 */
export const chatAnalyticsEventsTable = pgTable("chat_analytics_events", {
	id: id,
	websiteId: uuid("website_id")
		.references(() => websitesTable.id, { onDelete: "cascade" })
		.notNull(),
	visitorId: text("visitor_id").notNull(),
	conversationId: uuid("conversation_id").notNull(),
	eventType: text("event_type").notNull(),
	metadata: text("metadata").default("{}"),
	timestamp: timestamp("timestamp", { mode: "string" }).defaultNow().notNull(),
});

/**
 * Table for storing chat performance metrics
 */
export const chatPerformanceMetricsTable = pgTable("chat_performance_metrics", {
	id: id,
	websiteId: uuid("website_id")
		.references(() => websitesTable.id, { onDelete: "cascade" })
		.notNull(),
	conversationId: uuid("conversation_id").notNull(),
	responseTime: integer("response_time").notNull(), // in milliseconds
	tokensUsed: integer("tokens_used").default(0),
	ragUsed: boolean("rag_used").default(false),
	ragResultCount: integer("rag_result_count").default(0),
	timestamp: timestamp("timestamp", { mode: "string" }).defaultNow().notNull(),
});

// Types
export type ChatAnalyticsEvent = typeof chatAnalyticsEventsTable.$inferSelect;
export type NewChatAnalyticsEvent =
	typeof chatAnalyticsEventsTable.$inferInsert;

export type ChatPerformanceMetric =
	typeof chatPerformanceMetricsTable.$inferSelect;
export type NewChatPerformanceMetric =
	typeof chatPerformanceMetricsTable.$inferInsert;
