import {
	jsonb,
	pgTable,
	text,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { createdAt, id, updatedAt } from "../schema-helper";
import { plansTable } from "./plans";
// We'll import this when we implement user-specific conversations
// import { conversationsTable } from "./conversations";

// Define the users table that integrates with Clerk
export const usersTable = pgTable("users", {
	id,
	clerkId: text("clerk_id").notNull().unique(), // Store the Clerk user ID for reference
	email: text("email").notNull(),
	username: text("username"),
	firstName: text("first_name"),
	lastName: text("last_name"),
	imageUrl: text("image_url"),
	planId: uuid("plan_id").references(() => plansTable.id),
	subscriptionStatus: varchar("subscription_status", { length: 50 }).default(
		"FREE",
	),
	subscriptionExpiresAt: timestamp("subscription_expires_at", {
		withTimezone: true,
		mode: "string",
	}),
	preferences: jsonb("preferences").default({}),
	createdAt,
	updatedAt,
});

// Relations will be defined in a separate file to avoid circular dependencies

// Create Zod schemas for validation
export const UserSchema = createSelectSchema(usersTable);
export const InsertUserSchema = createInsertSchema(usersTable, {
	preferences: z.record(z.unknown()).optional().default({}),
});
export const NewUserSchema = InsertUserSchema.omit({
	id: true,
	createdAt: true,
	updatedAt: true,
});

// Types for the user
export type User = z.infer<typeof UserSchema>;
export type NewUser = z.infer<typeof NewUserSchema>;
