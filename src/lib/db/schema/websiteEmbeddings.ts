import { jsonb, pgTable, text, uuid, vector } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import type { z } from "zod";
import { createdAt, id, updatedAt } from "../schema-helper";
import { websitePagesTable } from "./websitePages";
import { websitesTable } from "./websites";

export const websiteEmbeddingsTable = pgTable("website_embeddings", {
	id,
	websiteId: uuid("website_id")
		.references(() => websitesTable.id, { onDelete: "cascade" })
		.notNull(),
	pageId: uuid("page_id")
		.references(() => websitePagesTable.id, { onDelete: "cascade" })
		.notNull(),
	chunkText: text("chunk_text").notNull(),
	// Using pgvector type with 1024 dimensions (Mistral's default)
	embedding: vector("embedding", { dimensions: 1024 }),
	metadata: jsonb("metadata").default({}).notNull(),
	createdAt,
	updatedAt,
});

// Schemas for validation
export const insertWebsiteEmbeddingSchema = createInsertSchema(
	websiteEmbeddingsTable,
);
export const selectWebsiteEmbeddingSchema = createSelectSchema(
	websiteEmbeddingsTable,
);

// Types
export type WebsiteEmbedding = z.infer<typeof selectWebsiteEmbeddingSchema>;
export type NewWebsiteEmbedding = z.infer<typeof insertWebsiteEmbeddingSchema>;
