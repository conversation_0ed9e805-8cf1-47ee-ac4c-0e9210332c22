import {
	jsonb,
	pgTable,
	text,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import type { z } from "zod";
import { createdAt, id, updatedAt } from "../schema-helper";
import { websitesTable } from "./websites";

export const websitePagesTable = pgTable("website_pages", {
	id,
	websiteId: uuid("website_id")
		.references(() => websitesTable.id, { onDelete: "cascade" })
		.notNull(),
	url: varchar("url", { length: 2048 }).notNull(),
	title: varchar("title", { length: 512 }).notNull(),
	content: text("content").notNull(),
	metadata: jsonb("metadata").default({}).notNull(),
	lastCrawledAt: timestamp("last_crawled_at", {
		withTimezone: true,
		mode: "string",
	}).notNull(),
	createdAt,
	updatedAt,
});

// Create Zod schemas for validation
export const WebsitePageSchema = createSelectSchema(websitePagesTable);
export const InsertWebsitePageSchema = createInsertSchema(websitePagesTable);
export const NewWebsitePageSchema = InsertWebsitePageSchema.omit({
	id: true,
});

// Types for the website page
export type WebsitePage = z.infer<typeof WebsitePageSchema>;
export type NewWebsitePage = z.infer<typeof NewWebsitePageSchema>;
