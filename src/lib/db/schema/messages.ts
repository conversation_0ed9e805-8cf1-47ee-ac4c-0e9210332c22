import { jsonb, pgTable, text, uuid, varchar } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import type { z } from "zod";
import { createdAt, id } from "../schema-helper";
import { conversationsTable } from "./conversations";

export const messagesTable = pgTable("messages", {
	id,
	conversationId: uuid("conversation_id")
		.references(() => conversationsTable.id, { onDelete: "cascade" })
		.notNull(),
	content: text("content").notNull(),
	role: varchar("role", { length: 50 }).notNull(),
	sources: jsonb("sources"),
	createdAt,
});

// Create Zod schemas for validation
export const MessageSchema = createSelectSchema(messagesTable);
export const InsertMessageSchema = createInsertSchema(messagesTable);
export const NewMessageSchema = InsertMessageSchema.omit({
	id: true,
});

// Types for the message
export type Message = z.infer<typeof MessageSchema>;
export type NewMessage = z.infer<typeof NewMessageSchema>;
