import {
	boolean,
	pgTable,
	text as pgText,
	text,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import type { z } from "zod";
import { createdAt, id, updatedAt } from "../schema-helper";
import { websitesTable } from "./websites";

export const chatConfigurationsTable = pgTable("chat_configurations", {
	id,
	websiteId: uuid("website_id")
		.references(() => websitesTable.id, { onDelete: "cascade" })
		.notNull(),
	name: varchar("name", { length: 255 }).default("Default Configuration"),
	primaryColor: varchar("primary_color", { length: 50 }).default("#4F46E5"),
	secondaryColor: varchar("secondary_color", { length: 50 }).default("#FFFFFF"),
	headerText: varchar("header_text", { length: 255 }).default("Chat Assistant"),
	welcomeMessage: text("welcome_message").default(
		"Hi there! How can I help you today?",
	),
	position: varchar("position", { length: 50 }).default("BOTTOM_RIGHT"),
	isActive: boolean("is_active").default(true),
	allowedDomains: pgText("allowed_domains").array().default([]),
	createdAt,
	updatedAt,
});

// Create Zod schemas for validation
export const ChatConfigurationSchema = createSelectSchema(
	chatConfigurationsTable,
);
export const InsertChatConfigurationSchema = createInsertSchema(
	chatConfigurationsTable,
);
export const NewChatConfigurationSchema = InsertChatConfigurationSchema.omit({
	id: true,
});

// Types for the chat configuration
export type ChatConfiguration = z.infer<typeof ChatConfigurationSchema>;
export type NewChatConfiguration = z.infer<typeof NewChatConfigurationSchema>;
