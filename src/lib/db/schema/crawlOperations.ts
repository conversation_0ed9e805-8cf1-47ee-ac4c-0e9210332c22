import {
	integer,
	jsonb,
	pgTable,
	text,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import type { z } from "zod";
import { createdAt, id, updatedAt } from "../schema-helper";
import { websitesTable } from "./websites";

// Define CrawlStatus enum
export enum CrawlStatus {
	PENDING = "PENDING",
	RUNNING = "RUNNING",
	COMPLETED = "COMPLETED",
	FAILED = "FAILED",
}

export const crawlOperationsTable = pgTable("crawl_operations", {
	id,
	websiteId: uuid("website_id")
		.references(() => websitesTable.id, { onDelete: "cascade" })
		.notNull(),
	status: varchar("status", { length: 50 }).notNull().default("PENDING"), // PENDING, RUNNING, COMPLETED, FAILED
	startedAt: timestamp("started_at", {
		withTimezone: true,
		mode: "string",
	}),
	completedAt: timestamp("completed_at", {
		withTimezone: true,
		mode: "string",
	}),
	pagesProcessed: integer("pages_processed").default(0),
	pagesSucceeded: integer("pages_succeeded").default(0),
	pagesFailed: integer("pages_failed").default(0),
	error: text("error"),
	configuration: jsonb("configuration"), // Store the crawl configuration
	createdAt,
	updatedAt,
});

// Create Zod schemas for validation
export const CrawlOperationSchema = createSelectSchema(crawlOperationsTable);
export const InsertCrawlOperationSchema =
	createInsertSchema(crawlOperationsTable);
export const NewCrawlOperationSchema = InsertCrawlOperationSchema.omit({
	id: true,
	createdAt: true,
	updatedAt: true,
});

// Types for the crawl operation
export type CrawlOperation = z.infer<typeof CrawlOperationSchema>;
export type NewCrawlOperation = z.infer<typeof NewCrawlOperationSchema>;
