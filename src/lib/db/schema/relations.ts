import { relations } from "drizzle-orm";
import { plansTable } from "./plans";
import { usersTable } from "./users";
import { websitesTable } from "./websites";

// Define user relations
export const usersRelations = relations(usersTable, ({ many, one }) => ({
	websites: many(websitesTable),
	plan: one(plansTable, {
		fields: [usersTable.planId],
		references: [plansTable.id],
	}),
}));

// Define website relations
export const websitesRelations = relations(websitesTable, ({ one }) => ({
	user: one(usersTable, {
		fields: [websitesTable.userId],
		references: [usersTable.id],
	}),
}));

// Define plan relations
export const plansRelations = relations(plansTable, ({ many }) => ({
	users: many(usersTable),
}));
