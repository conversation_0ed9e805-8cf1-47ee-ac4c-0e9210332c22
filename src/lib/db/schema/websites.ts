import { jsonb, pgTable, timestamp, uuid, varchar } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import type { z } from "zod";
import { createdAt, id, updatedAt } from "../schema-helper";

// Define WebsiteStatus enum
export enum WebsiteStatus {
	ACTIVE = "ACTIVE",
	CRAWLING = "CRAWLING",
	ERROR = "ERROR",
	INACTIVE = "INACTIVE",
}

export const websitesTable = pgTable("websites", {
	id,
	userId: uuid("user_id"),
	url: varchar("url", { length: 255 }).notNull(),
	name: varchar("name", { length: 255 }).notNull(),
	lastCrawledAt: timestamp("last_crawled_at", {
		withTimezone: true,
		mode: "string",
	}),
	crawlFrequency: varchar("crawl_frequency", { length: 50 }).default("WEEKLY"),
	status: varchar("status", { length: 50 }).default("ACTIVE"),
	crawlConfiguration: jsonb("crawl_configuration"), // Store the last used crawl configuration
	createdAt,
	updatedAt,
});

// Relations will be defined in a separate file to avoid circular dependencies

// Create Zod schemas for validation
export const WebsiteSchema = createSelectSchema(websitesTable);
export const InsertWebsiteSchema = createInsertSchema(websitesTable);
export const NewWebsiteSchema = InsertWebsiteSchema.omit({
	id: true,
	createdAt: true,
	updatedAt: true,
});

// Types for the website
export type Website = z.infer<typeof WebsiteSchema>;
export type NewWebsite = z.infer<typeof NewWebsiteSchema>;
