import {
	integer,
	jsonb,
	pgTable,
	real,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";

export const ragMetrics = pgTable("rag_metrics", {
	id: uuid("id").primaryKey().defaultRandom(),
	websiteId: uuid("website_id").notNull(),
	timestamp: timestamp("timestamp", { withTimezone: true }).notNull(),
	searchLatency: real("search_latency").notNull(),
	searchResultsCount: integer("search_results_count").notNull(),
	averageSimilarity: real("average_similarity").notNull(),
	cacheHitRate: real("cache_hit_rate").notNull(),
	totalChunks: integer("total_chunks").notNull(),
	averageChunkSize: real("average_chunk_size").notNull(),
	chunkingLatency: real("chunking_latency").notNull(),
	embeddingLatency: real("embedding_latency").notNull(),
	embeddingDimensions: integer("embedding_dimensions").notNull(),
	errorCount: integer("error_count").notNull(),
	errorTypes: jsonb("error_types").notNull(),
});
