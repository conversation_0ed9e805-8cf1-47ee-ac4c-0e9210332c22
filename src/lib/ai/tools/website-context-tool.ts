import { db } from "@/lib/db";
import { websitesTable } from "@/lib/db/schema";
import { WebsiteRagService } from "@/lib/services/rag/website-rag-service";
import { RuntimeContext } from "@mastra/core/di";
import { createTool } from "@mastra/core/tools";
import { eq } from "drizzle-orm";
import { z } from "zod";

/**
 * Tool for retrieving website context information using MastrAI's RAG capabilities
 */
export const websiteContextTool = createTool({
	id: "get-website-context",
	description:
		"Get context information about a website. Use this tool to retrieve information about the website the user is asking about.",
	inputSchema: z.object({
		websiteId: z
			.string()
			.describe(
				"The ID of the website to get context for. This can be omitted if provided in the runtime context.",
			)
			.optional(),
		query: z
			.string()
			.describe(
				"The query to search for in the website content. If not provided, the user's message will be used.",
			)
			.optional(),
	}),
	outputSchema: z.object({
		websiteInfo: z.object({
			id: z.string(),
			name: z.string(),
			url: z.string(),
		}),
		content: z
			.array(
				z.object({
					title: z.string(),
					text: z.string(),
					url: z.string().optional(),
					similarity: z.number().optional(),
				}),
			)
			.optional(),
	}),
	execute: async ({ context, runtimeContext }) => {
		try {
			// Get websiteId from either the context or the runtimeContext
			const websiteId = context.websiteId || runtimeContext?.get("websiteId");

			if (!websiteId) {
				return {
					websiteInfo: {
						id: "unknown",
						name: "Unknown Website",
						url: "unknown",
					},
					content: [
						{
							title: "Error",
							text: "Website ID is required but was not provided",
						},
					],
				};
			}

			// Get website information
			const website = await db
				.select({
					id: websitesTable.id,
					name: websitesTable.name,
					url: websitesTable.url,
				})
				.from(websitesTable)
				.where(eq(websitesTable.id, websiteId))
				.then((results) => results[0]);

			if (!website) {
				return {
					websiteInfo: {
						id: websiteId,
						name: "Unknown Website",
						url: "unknown",
					},
					content: [
						{
							title: "Error",
							text: `Website with ID ${websiteId} not found`,
						},
					],
				};
			}

			// Get the query from the context or the user's message
			const query = context.query || runtimeContext?.get("userMessage") || "";

			// If we have a query, use vector search to find relevant content
			let content: Array<{
				title: string;
				text: string;
				url?: string;
				similarity?: number;
			}> = [];

			if (query) {
				try {
					// Create an instance of the WebsiteRagService
					const ragService = new WebsiteRagService();

					// Find similar content using vector search
					const similarContent = await ragService.searchSimilarContent(
						websiteId,
						query,
						5, // Limit to 5 results
					);

					// Format the content for the output
					content = similarContent.map((item) => ({
						title: item.title || "Untitled",
						text: item.text,
						url: item.url,
						similarity: item.similarity,
					}));
				} catch (error) {
					console.error("Error searching for similar content:", error);
					// Don't throw, just return a message about the error
					content = [
						{
							title: "Search Error",
							text: "Failed to search website content. Please try again or rephrase your query.",
							url: website.url,
						},
					];
				}
			}

			// If no content was found, return a message
			if (content.length === 0) {
				content = [
					{
						title: "No Content Found",
						text: query
							? `No relevant content was found for "${query}" on ${website.name}. Please try a different query or check if the website has been crawled.`
							: `Welcome to ${website.name}! How can I help you today?`,
						url: website.url,
					},
				];
			}

			return {
				websiteInfo: {
					id: website.id,
					name: website.name,
					url: website.url,
				},
				content,
			};
		} catch (error) {
			console.error("Error retrieving website context:", error);
			// Return a valid response even in case of error
			return {
				websiteInfo: {
					id: "error",
					name: "Error",
					url: "unknown",
				},
				content: [
					{
						title: "System Error",
						text: "Sorry, I encountered an error while retrieving website information. Please try again in a moment.",
					},
				],
			};
		}
	},
});
