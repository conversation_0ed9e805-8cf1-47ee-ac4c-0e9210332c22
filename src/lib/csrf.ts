import { createHash, randomBytes } from "node:crypto";
import { env } from "@/env";
import { getCookie, setSecureCookie } from "@/lib/cookies";

// CSRF token cookie name
const CSRF_COOKIE_NAME = "csrf_token";
// CSRF token header name
const CSRF_HEADER_NAME = "X-CSRF-Token";
// CSRF token expiration time (1 hour)
const CSRF_TOKEN_EXPIRATION = 60 * 60 * 1000; // 1 hour in milliseconds

// In-memory token store as a fallback when cookies don't work
// This is a temporary solution until we implement a proper session store
// Key: userId or sessionId, Value: { token: string, expires: number }
const tokenStore = new Map<string, { token: string; expires: number }>();

/**
 * Generate a CSRF token
 * We'll try both cookie-based and in-memory approaches for maximum compatibility
 * @param userId Optional user ID for the in-memory store
 * @returns The generated CSRF token
 */
export async function generateCsrfToken(userId?: string): Promise<string> {
	console.log(`[CSRF] Generating token in environment: ${env.NODE_ENV}`);
	console.log(`[CSRF] CSRF_SECRET is ${env.CSRF_SECRET ? "set" : "not set"}`);

	// Generate a random token
	const token = randomBytes(32).toString("hex");

	// Create a hash of the token
	const hash = createHash("sha256").update(token).digest("hex");

	try {
		// Try to store the hash in a cookie first
		const cookieOptions = {
			maxAge: CSRF_TOKEN_EXPIRATION / 1000, // Convert to seconds
			// In production, we need to ensure cookies work across subdomains if applicable
			// and are sent with cross-origin requests
			...(env.NODE_ENV === "production" && {
				// Use 'none' for cross-origin requests in production
				sameSite: "none" as const,
				// Must be secure when sameSite is 'none'
				secure: true,
			}),
		};

		console.log("[CSRF] Setting cookie with options:", cookieOptions);
		await setSecureCookie(CSRF_COOKIE_NAME, hash, cookieOptions);

		// Verify the cookie was set
		const storedHash = await getCookie(CSRF_COOKIE_NAME);
		console.log(
			`[CSRF] Cookie verification: ${
				storedHash ? "Cookie set successfully" : "Cookie not set"
			}`,
		);
	} catch (error) {
		console.error("[CSRF] Error setting cookie:", error);
	}

	// As a fallback, also store the token in memory
	// This ensures CSRF protection works even if cookies fail
	if (userId) {
		tokenStore.set(userId, {
			token: hash,
			expires: Date.now() + CSRF_TOKEN_EXPIRATION,
		});
		console.log(`[CSRF] Stored token in memory for user ${userId}`);
	} else {
		console.warn("[CSRF] No userId provided for in-memory token storage");
	}

	return token;
}

/**
 * Validate a CSRF token
 * We'll check both cookie-based and in-memory approaches
 * @param token The CSRF token to validate
 * @param userId Optional user ID for the in-memory store
 * @returns Whether the token is valid
 */
export async function validateCsrfToken(
	token: string,
	userId?: string,
): Promise<boolean> {
	if (!token) {
		console.warn("[CSRF] No token provided for validation");
		return false;
	}

	// Hash the provided token
	const hash = createHash("sha256").update(token).digest("hex");

	// Try to validate against the cookie first
	try {
		const storedHash = await getCookie(CSRF_COOKIE_NAME);
		if (storedHash && hash === storedHash) {
			console.log("[CSRF] Token validated via cookie");
			return true;
		}
	} catch (error) {
		console.error("[CSRF] Error getting cookie:", error);
	}

	// If cookie validation fails or there's no cookie, try the in-memory store
	if (userId) {
		const storedData = tokenStore.get(userId);
		if (
			storedData &&
			storedData.expires > Date.now() &&
			hash === storedData.token
		) {
			console.log(
				`[CSRF] Token validated via in-memory store for user ${userId}`,
			);
			return true;
		}
	}

	// If we get here, the token is invalid or expired
	console.warn("[CSRF] Invalid or expired token");
	return false;
}

/**
 * Get the CSRF token header name
 * @returns The CSRF token header name
 */
export function getCsrfHeaderName(): string {
	return CSRF_HEADER_NAME;
}

/**
 * Get the CSRF token from the request headers
 * @param headers The request headers
 * @returns The CSRF token or null if not found
 */
export function getCsrfTokenFromHeaders(headers: Headers): string | null {
	return headers.get(CSRF_HEADER_NAME);
}
