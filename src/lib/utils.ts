import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

/**
 * Generate a stable ID that works on both server and client
 * Avoids using crypto.randomUUID() which can cause hydration errors
 *
 * @param prefix Optional prefix for the ID
 * @returns A stable ID string
 */
export function generateStableId(prefix = "id"): string {
	return `${prefix}-${Date.now()}-${Math.random()
		.toString(36)
		.substring(2, 10)}`;
}
