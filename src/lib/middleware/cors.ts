import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

/**
 * CORS middleware for API routes
 * Adds the necessary headers to allow cross-origin requests
 */
export function corsMiddleware(
	request: NextRequest,
	response: NextResponse,
	options: {
		allowedOrigins?: string[];
		allowedMethods?: string[];
		allowedHeaders?: string[];
		exposedHeaders?: string[];
		maxAge?: number;
		credentials?: boolean;
	} = {},
): NextResponse {
	// Default options
	const {
		allowedOrigins = ["*"],
		allowedMethods = [
			"GET",
			"POST",
			"PUT",
			"DELETE",
			"OPTIONS",
			"PATCH",
			"HEAD",
		],
		allowedHeaders = ["Content-Type", "Authorization"],
		exposedHeaders = [],
		maxAge = 86400, // 24 hours
		credentials = true,
	} = options;

	// Get the origin from the request
	const origin = request.headers.get("origin") || "";
	console.log(`[CORS Middleware] Request origin: ${origin || "none"}`);
	console.log("[CORS Middleware] Allowed origins:", allowedOrigins);

	// Check if wildcard is allowed
	const allowWildcard = allowedOrigins.includes("*");

	// Check if the specific origin is allowed
	let isAllowedOrigin = allowWildcard || allowedOrigins.includes(origin);

	// If not directly allowed, check for domain/subdomain matching
	if (!isAllowedOrigin && origin && !allowWildcard) {
		try {
			const originDomain = new URL(origin).hostname;

			isAllowedOrigin = allowedOrigins.some((allowedOrigin) => {
				try {
					if (!allowedOrigin) return false;

					// Handle case where allowedOrigin might not be a valid URL
					let allowedDomainHost: string;
					try {
						// Try to parse as URL first
						allowedDomainHost = new URL(allowedOrigin).hostname;
					} catch (e) {
						// If it's not a valid URL, use it as is (assuming it's just a domain)
						allowedDomainHost = allowedOrigin;
					}

					// Check if domain matches exactly or is a subdomain
					const isAllowed =
						originDomain === allowedDomainHost ||
						originDomain.endsWith(`.${allowedDomainHost}`);

					console.log(
						`[CORS Middleware] Checking ${originDomain} against ${allowedDomainHost}: ${
							isAllowed ? "allowed" : "not allowed"
						}`,
					);
					return isAllowed;
				} catch (error) {
					console.error("[CORS Middleware] Error checking domain:", error);
					return false;
				}
			});
		} catch (error) {
			console.error("[CORS Middleware] Error parsing origin:", error);
		}
	}

	// Set CORS headers
	const headers = new Headers(response.headers);

	// Set Access-Control-Allow-Origin
	if (isAllowedOrigin) {
		console.log(
			`[CORS Middleware] Setting Access-Control-Allow-Origin: ${
				allowWildcard ? "*" : origin
			}`,
		);
		headers.set("Access-Control-Allow-Origin", allowWildcard ? "*" : origin);
	} else {
		console.log("[CORS Middleware] Origin not allowed, using fallback");
		// Even if not allowed, we need to set the header for the browser to receive the error response
		headers.set("Access-Control-Allow-Origin", origin);
	}

	// Set Access-Control-Allow-Credentials if needed
	if (credentials) {
		headers.set("Access-Control-Allow-Credentials", "true");
	}

	// Set Access-Control-Allow-Methods
	headers.set("Access-Control-Allow-Methods", allowedMethods.join(", "));

	// Set Access-Control-Allow-Headers
	headers.set("Access-Control-Allow-Headers", allowedHeaders.join(", "));

	// Set Access-Control-Expose-Headers if needed
	if (exposedHeaders.length > 0) {
		headers.set("Access-Control-Expose-Headers", exposedHeaders.join(", "));
	}

	// Set Access-Control-Max-Age
	headers.set("Access-Control-Max-Age", maxAge.toString());

	// Create a new response with the CORS headers
	return NextResponse.json(response.json(), {
		status: response.status,
		statusText: response.statusText,
		headers,
	});
}

/**
 * Handle CORS preflight requests
 */
export function handleCorsPreflightRequest(
	request: NextRequest,
	options: {
		allowedOrigins?: string[];
		allowedMethods?: string[];
		allowedHeaders?: string[];
		maxAge?: number;
		credentials?: boolean;
	} = {},
): NextResponse {
	// Default options
	const {
		allowedOrigins = ["*"],
		allowedMethods = [
			"GET",
			"POST",
			"PUT",
			"DELETE",
			"OPTIONS",
			"PATCH",
			"HEAD",
		],
		allowedHeaders = ["Content-Type", "Authorization"],
		maxAge = 86400, // 24 hours
		credentials = true,
	} = options;

	// Get the origin from the request
	const origin = request.headers.get("origin") || "";
	console.log(`[CORS Preflight] Request origin: ${origin || "none"}`);
	console.log("[CORS Preflight] Allowed origins:", allowedOrigins);

	// Check if wildcard is allowed
	const allowWildcard = allowedOrigins.includes("*");

	// Check if the specific origin is allowed
	let isAllowedOrigin = allowWildcard || allowedOrigins.includes(origin);

	// If not directly allowed, check for domain/subdomain matching
	if (!isAllowedOrigin && origin && !allowWildcard) {
		try {
			const originDomain = new URL(origin).hostname;

			isAllowedOrigin = allowedOrigins.some((allowedOrigin) => {
				try {
					if (!allowedOrigin) return false;

					// Handle case where allowedOrigin might not be a valid URL
					let allowedDomainHost: string;
					try {
						// Try to parse as URL first
						allowedDomainHost = new URL(allowedOrigin).hostname;
					} catch (e) {
						// If it's not a valid URL, use it as is (assuming it's just a domain)
						allowedDomainHost = allowedOrigin;
					}

					// Check if domain matches exactly or is a subdomain
					const isAllowed =
						originDomain === allowedDomainHost ||
						originDomain.endsWith(`.${allowedDomainHost}`);

					console.log(
						`[CORS Preflight] Checking ${originDomain} against ${allowedDomainHost}: ${
							isAllowed ? "allowed" : "not allowed"
						}`,
					);
					return isAllowed;
				} catch (error) {
					console.error("[CORS Preflight] Error checking domain:", error);
					return false;
				}
			});
		} catch (error) {
			console.error("[CORS Preflight] Error parsing origin:", error);
		}
	}

	// Create headers for the response
	const headers = new Headers();

	// Set Access-Control-Allow-Origin
	// For preflight requests, we always need to respond with the origin
	// to allow the browser to process the response
	console.log(
		`[CORS Preflight] Setting Access-Control-Allow-Origin: ${origin}`,
	);
	headers.set("Access-Control-Allow-Origin", origin);

	// Set Access-Control-Allow-Credentials if needed
	if (credentials) {
		headers.set("Access-Control-Allow-Credentials", "true");
	}

	// Set Access-Control-Allow-Methods
	headers.set("Access-Control-Allow-Methods", allowedMethods.join(", "));

	// Set Access-Control-Allow-Headers
	headers.set("Access-Control-Allow-Headers", allowedHeaders.join(", "));

	// Set Access-Control-Max-Age
	headers.set("Access-Control-Max-Age", maxAge.toString());

	// Return a 204 No Content response with the CORS headers
	return new NextResponse(null, {
		status: 204,
		headers,
	});
}
