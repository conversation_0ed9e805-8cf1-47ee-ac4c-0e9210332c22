import { NextResponse } from "next/server";
import { ZodError, type ZodSchema } from "zod";

/**
 * Middleware to validate request data against a Zod schema
 * @param schema The Zod schema to validate against
 * @param data The data to validate
 * @returns The validated data or an error response
 */
export function validateRequest<T>(
	schema: ZodSchema<T>,
	data: unknown,
): { success: true; data: T } | NextResponse {
	try {
		// Parse the data with the schema
		const validatedData = schema.parse(data);

		// Return the validated data
		return { success: true, data: validatedData };
	} catch (error) {
		// If the error is a ZodError, return a 400 response with the validation errors
		if (error instanceof ZodError) {
			return NextResponse.json(
				{
					error: "Validation error",
					details: error.flatten(),
				},
				{ status: 400 },
			);
		}

		// For other errors, return a 500 response
		console.error("Validation error:", error);
		return NextResponse.json(
			{ error: "Internal server error" },
			{ status: 500 },
		);
	}
}

/**
 * Middleware to validate request body against a Zod schema
 * @param schema The Zod schema to validate against
 * @param request The request to validate
 * @returns The validated data or an error response
 */
export async function validateRequestBody<T>(
	schema: ZodSchema<T>,
	request: Request,
): Promise<{ success: true; data: T } | NextResponse> {
	try {
		// Parse the request body as JSON
		const body = await request.json();

		// Validate the body
		return validateRequest(schema, body);
	} catch (error) {
		// If the error is from parsing JSON, return a 400 response
		if (error instanceof SyntaxError) {
			return NextResponse.json(
				{ error: "Invalid JSON in request body" },
				{ status: 400 },
			);
		}

		// For other errors, return a 500 response
		console.error("Request body validation error:", error);
		return NextResponse.json(
			{ error: "Internal server error" },
			{ status: 500 },
		);
	}
}

/**
 * Middleware to validate request query parameters against a Zod schema
 * @param schema The Zod schema to validate against
 * @param request The request to validate
 * @returns The validated data or an error response
 */
export function validateRequestQuery<T>(
	schema: ZodSchema<T>,
	request: Request,
): { success: true; data: T } | NextResponse {
	try {
		// Get the URL from the request
		const url = new URL(request.url);

		// Convert the query parameters to an object
		const queryParams: Record<string, string> = {};
		url.searchParams.forEach((value, key) => {
			queryParams[key] = value;
		});

		// Validate the query parameters
		return validateRequest(schema, queryParams);
	} catch (error) {
		// For errors, return a 500 response
		console.error("Request query validation error:", error);
		return NextResponse.json(
			{ error: "Internal server error" },
			{ status: 500 },
		);
	}
}
