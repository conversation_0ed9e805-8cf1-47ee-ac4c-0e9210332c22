import { env } from "@/env";
import type { NextResponse } from "next/server";

/**
 * Apply security headers to a response
 * @param response The response to apply headers to
 * @returns The response with headers applied
 */
export function applySecurityHeaders(response: NextResponse): NextResponse {
	// Content Security Policy
	// Customize this based on your application's needs
	response.headers.set(
		"Content-Security-Policy",
		[
			// Default fallback
			"default-src 'self'",
			// Scripts
			"script-src 'self' 'unsafe-inline' https://js.clerk.dev https://cdn.jsdelivr.net",
			// Styles
			"style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
			// Images
			"img-src 'self' data: https://images.clerk.dev https://*.googleusercontent.com",
			// Fonts
			"font-src 'self' https://fonts.gstatic.com",
			// Connect (for API calls, WebSockets)
			"connect-src 'self' https://api.clerk.dev https://*.clerk.accounts.dev wss://ws-ap2.pusher.com",
			// Frame sources (for embedded content)
			"frame-src 'self'",
			// Media sources
			"media-src 'self'",
		].join("; "),
	);

	// HTTP Strict Transport Security
	// Ensures the browser only uses HTTPS for future requests
	response.headers.set(
		"Strict-Transport-Security",
		"max-age=********; includeSubDomains; preload",
	);

	// X-Frame-Options
	// Controls whether the page can be embedded in frames
	response.headers.set("X-Frame-Options", "SAMEORIGIN");

	// X-Content-Type-Options
	// Prevents MIME type sniffing
	response.headers.set("X-Content-Type-Options", "nosniff");

	// Referrer Policy
	// Controls how much referrer information is sent
	response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

	// Permissions Policy
	// Controls which browser features can be used
	response.headers.set(
		"Permissions-Policy",
		"geolocation=(), microphone=(), camera=(), payment=()",
	);

	// Cross-Origin Resource Policy
	// Controls which websites can include this resource
	response.headers.set("Cross-Origin-Resource-Policy", "same-origin");

	// X-XSS-Protection
	// Enables XSS filtering in browsers that support it
	response.headers.set("X-XSS-Protection", "1; mode=block");

	// Cache Control
	// For API routes, prevent caching of responses
	if (response.headers.get("Content-Type")?.includes("application/json")) {
		response.headers.set(
			"Cache-Control",
			"no-store, no-cache, must-revalidate, proxy-revalidate",
		);
		response.headers.set("Pragma", "no-cache");
		response.headers.set("Expires", "0");
	}

	return response;
}
