import { WebsiteRagService } from "@/lib/services/rag/website-rag-service";

/**
 * Search for similar content in a website using vector search
 *
 * @param websiteId - The ID of the website to search
 * @param query - The search query
 * @param limit - Maximum number of results to return (default: 5)
 * @returns Array of search results with content, url, title, and similarity score
 */
export async function searchSimilarContent(
	websiteId: string,
	query: string,
	limit = 5,
): Promise<
	Array<{
		content: string;
		url: string;
		title: string;
		similarity: number;
	}>
> {
	try {
		// Create an instance of the WebsiteRagService
		const ragService = new WebsiteRagService();

		// Search for similar content
		const results = await ragService.searchSimilarContent(
			websiteId,
			query,
			limit,
		);

		// Map the results to the expected format
		return results.map((result) => ({
			content: result.text,
			url: result.url,
			title: result.title,
			similarity: result.similarity,
		}));
	} catch (error) {
		console.error("Error in searchSimilarContent:", error);
		// Return an empty array instead of throwing to avoid breaking the API
		return [];
	}
}
