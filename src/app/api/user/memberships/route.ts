import type {
	ClerkOrganizationMembership,
	ClerkUserWithMemberships,
} from "@/types/clerk";
import { createClerkClient } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

// Initialize the Clerk client with the secret key from environment variables
const clerkClient = createClerkClient({
	secretKey: process.env.CLERK_SECRET_KEY,
});

export async function GET(request: Request) {
	try {
		const url = new URL(request.url);
		const userId = url.searchParams.get("userId");

		if (!userId) {
			return NextResponse.json(
				{ error: "Missing userId parameter" },
				{ status: 400 },
			);
		}

		let organizationMemberships: ClerkOrganizationMembership[] = [];

		try {
			const user = await clerkClient.users.getUser(userId);
			// Cast to unknown first, then to our custom type
			organizationMemberships =
				(user as unknown as ClerkUserWithMemberships).organizationMemberships ||
				[];
		} catch (clerkError) {
			console.warn("Could not get Clerk organization memberships:", clerkError);
		}

		return NextResponse.json({
			hasOrganization: organizationMemberships.length > 0,
			organizations: organizationMemberships.map((membership) => ({
				id: membership.organization.id,
				name: membership.organization.name,
				role: membership.role,
			})),
		});
	} catch (error) {
		console.error("Error in GET /api/user/memberships:", error);
		return NextResponse.json(
			{ error: "Internal server error" },
			{ status: 500 },
		);
	}
}
