import { env } from "@/env";
import { db } from "@/lib/db";
import { websitesTable } from "@/lib/db/schema";
import type {
	ClerkOrganizationMembership,
	ClerkUserWithMemberships,
} from "@/types/clerk";
import { auth } from "@clerk/nextjs/server";
import { createClerkClient } from "@clerk/nextjs/server";
import { and, eq, or } from "drizzle-orm";
import { NextResponse } from "next/server";

// Initialize the Clerk client with the secret key from environment variables
const clerkClient = createClerkClient({
	secretKey: env.CLERK_SECRET_KEY,
});

export async function GET() {
	try {
		// Get the user from Clerk
		const { userId } = await auth();

		// Check if the user is authenticated
		if (!userId) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Check if the organization has a website
		const websites = await db.select().from(websitesTable);

		const hasWebsite = websites.length > 0;

		// Return the setup status
		return NextResponse.json({
			setupComplete: hasWebsite,
			step: hasWebsite ? "complete" : "website",
		});
	} catch (error) {
		console.error("Error in setup status API route:", error);
		return NextResponse.json(
			{ error: "Failed to check setup status" },
			{ status: 500 },
		);
	}
}
