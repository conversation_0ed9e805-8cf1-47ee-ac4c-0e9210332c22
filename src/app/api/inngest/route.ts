import { inngest } from "@/inngest/client";
import {
	crawlWebsite,
	optimizeAllRag,
	optimizeRag,
	scheduledCrawl,
	scheduledRagOptimization,
} from "@/inngest/functions";
import { serve } from "inngest/next";

/**
 * This API route serves the Inngest functions
 * It handles incoming requests from Inngest and executes the appropriate function
 */
export const { GET, POST, PUT } = serve({
	client: inngest,
	functions: [
		crawlWebsite,
		scheduledCrawl,
		optimizeRag,
		optimizeAllRag,
		scheduledRagOptimization,
	],
});
