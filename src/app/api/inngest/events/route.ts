import { inngest } from "@/inngest/client";
import { auth } from "@clerk/nextjs/server";
import { type NextRequest, NextResponse } from "next/server";

/**
 * API route to manually send events to Inngest
 * This allows triggering Inngest functions directly from the UI
 */
export async function POST(request: NextRequest) {
	const { userId } = await auth();

	// Check if the user is authenticated
	if (!userId) {
		return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
	}

	try {
		// Get the event data from the request body
		const body = await request.json();

		// Validate the event data
		if (!body.name) {
			return NextResponse.json(
				{ error: "Missing event name" },
				{ status: 400 },
			);
		}

		// Send the event to Inngest
		await inngest.send({
			name: body.name,
			data: body.data || {},
		});

		return NextResponse.json({
			message: `Event ${body.name} sent successfully`,
		});
	} catch (error) {
		console.error("Error sending Inngest event:", error);
		return NextResponse.json(
			{ error: "Failed to send event" },
			{ status: 500 },
		);
	}
}
