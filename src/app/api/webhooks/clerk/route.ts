import { env } from "@/env";
import { db } from "@/lib/db";
import { NewUserSchema, usersTable } from "@/lib/db/schema/users";
import type { WebhookEvent } from "@clerk/nextjs/server";
import { eq } from "drizzle-orm";
import { headers } from "next/headers";
import { Webhook } from "svix";

export async function POST(req: Request) {
	// Get the headers
	const headersList = await headers();
	const svixId = headersList.get("svix-id");
	const svixTimestamp = headersList.get("svix-timestamp");
	const svixSignature = headersList.get("svix-signature");

	// If there are no headers, return error
	if (!svixId || !svixTimestamp || !svixSignature) {
		console.error("Missing Svix headers");
		return new Response("Error occurred -- missing svix headers", {
			status: 400,
		});
	}

	// Get the body
	let payload: unknown;
	try {
		payload = await req.json();
	} catch (error) {
		console.error("Error parsing request body:", error);
		return new Response("Invalid JSON payload", { status: 400 });
	}

	const body = JSON.stringify(payload);

	// Verify webhook secret exists
	if (!env.CLERK_WEBHOOK_SECRET) {
		console.error("CLERK_WEBHOOK_SECRET is not set");
		return new Response("Webhook secret not configured", { status: 500 });
	}

	// Create a new Svix instance with your webhook secret
	const wh = new Webhook(env.CLERK_WEBHOOK_SECRET);

	let evt: WebhookEvent;

	// Verify the payload with the headers
	try {
		evt = wh.verify(body, {
			"svix-id": svixId,
			"svix-timestamp": svixTimestamp,
			"svix-signature": svixSignature,
		}) as WebhookEvent;
	} catch (err) {
		console.error("Error verifying webhook:", err);
		return new Response("Error verifying webhook", { status: 400 });
	}

	// Handle the webhook event based on the type
	const eventType = evt.type;
	console.log(`Processing webhook event: ${eventType}`);

	if (eventType === "user.created") {
		const { id, email_addresses, first_name, last_name, image_url, username } =
			evt.data;

		// Get primary email
		const email = email_addresses?.[0]?.email_address;
		if (!email) {
			console.error("No email found in user data:", evt.data);
			return new Response("No email address found for user", { status: 400 });
		}

		try {
			// Validate user data with our Zod schema
			const parseResult = NewUserSchema.safeParse({
				clerkId: id,
				email,
				username: username || undefined,
				firstName: first_name || undefined,
				lastName: last_name || undefined,
				imageUrl: image_url || undefined,
			});
			if (!parseResult.success) {
				return new Response(
					JSON.stringify({
						error: "Invalid request body",
						details: parseResult.error.flatten(),
					}),
					{
						status: 400,
						headers: { "Content-Type": "application/json" },
					},
				);
			}
			const newUserData = parseResult.data;

			// Create user in our database
			const [createdUser] = await db
				.insert(usersTable)
				.values(newUserData)
				.returning();

			console.log("User created:", createdUser);
			return new Response(
				JSON.stringify({ success: true, user: createdUser }),
				{
					status: 201,
					headers: { "Content-Type": "application/json" },
				},
			);
		} catch (error) {
			console.error("Error creating user:", error);
			return new Response(
				`Error creating user: ${
					error instanceof Error ? error.message : String(error)
				}`,
				{
					status: 500,
				},
			);
		}
	}

	if (eventType === "user.updated") {
		const { id, email_addresses, first_name, last_name, image_url, username } =
			evt.data;

		try {
			// Check if user exists
			const existingUser = await db.query.usersTable.findFirst({
				where: eq(usersTable.clerkId, id as string),
			});

			if (!existingUser) {
				console.error(`User with clerkId ${id} not found`);
				return new Response("User not found", { status: 404 });
			}

			// Update user
			await db
				.update(usersTable)
				.set({
					email: email_addresses?.[0]?.email_address || existingUser.email,
					username: username || existingUser.username,
					firstName: first_name || existingUser.firstName,
					lastName: last_name || existingUser.lastName,
					imageUrl: image_url || existingUser.imageUrl,
					updatedAt: new Date().toISOString(),
				})
				.where(eq(usersTable.clerkId, id as string));

			console.log(`User ${id} updated successfully`);
			return new Response(JSON.stringify({ success: true }), {
				status: 200,
				headers: { "Content-Type": "application/json" },
			});
		} catch (error) {
			console.error("Error updating user:", error);
			return new Response(
				`Error updating user: ${
					error instanceof Error ? error.message : String(error)
				}`,
				{
					status: 500,
				},
			);
		}
	}

	if (eventType === "user.deleted") {
		const { id } = evt.data;

		try {
			// Delete user by clerkId
			const result = await db
				.delete(usersTable)
				.where(eq(usersTable.clerkId, id as string));

			console.log(`User ${id} deleted successfully:`, result);
			return new Response(JSON.stringify({ success: true }), {
				status: 200,
				headers: { "Content-Type": "application/json" },
			});
		} catch (error) {
			console.error("Error deleting user:", error);
			return new Response(
				`Error deleting user: ${
					error instanceof Error ? error.message : String(error)
				}`,
				{
					status: 500,
				},
			);
		}
	}

	// For any other event types
	return new Response(JSON.stringify({ received: true }), {
		status: 200,
		headers: { "Content-Type": "application/json" },
	});
}
