import { env } from "@/env";
import { generateCsrfToken } from "@/lib/csrf";
import { applySecurityHeaders } from "@/lib/middleware/securityHeaders";
import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

/**
 * API route to generate a CSRF token
 * This is used by the useCsrfToken hook to get a token for forms
 * Now supports both cookie-based and in-memory token storage
 */
export async function GET() {
	try {
		console.log("[CSRF API] Generating new CSRF token");

		// Get the user ID from Clerk for in-memory token storage
		const { userId } = await auth();

		// Generate a CSRF token and store its hash
		// If userId is available, also store in memory as a fallback
		const token = await generateCsrfToken(userId || undefined);

		// Create the response
		const response = NextResponse.json({
			token,
			// Include user ID and environment info for debugging
			userId: userId || null,
			env: env.NODE_ENV,
			timestamp: new Date().toISOString(),
		});

		// Set Cache-Control headers to prevent caching
		response.headers.set(
			"Cache-Control",
			"no-store, no-cache, must-revalidate, proxy-revalidate",
		);
		response.headers.set("Pragma", "no-cache");
		response.headers.set("Expires", "0");

		// Set CORS headers to ensure the response can be read from any origin
		response.headers.set("Access-Control-Allow-Origin", "*");
		response.headers.set("Access-Control-Allow-Credentials", "true");

		// Apply security headers
		return applySecurityHeaders(response);
	} catch (error) {
		console.error("[CSRF API] Error generating CSRF token:", error);

		// Return an error response
		const response = NextResponse.json(
			{
				error: "Failed to generate CSRF token",
				message: error instanceof Error ? error.message : "Unknown error",
				env: env.NODE_ENV,
				timestamp: new Date().toISOString(),
			},
			{ status: 500 },
		);

		// Apply security headers
		return applySecurityHeaders(response);
	}
}
