import {
	chatConfigurationsRepository,
	websitesRepository,
} from "@/lib/db/repositories";
import {
	corsMiddleware,
	handleCorsPreflightRequest,
} from "@/lib/middleware/cors";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export const dynamic = "force-dynamic";

/**
 * Handle OPTIONS requests for CORS preflight
 */
export function OPTIONS(request: NextRequest) {
	return handleCorsPreflightRequest(request);
}

/**
 * Public API route to get chat configuration for a website
 * This route does not require authentication
 */
export async function GET(
	request: NextRequest,
	{
		params,
	}: {
		params: Promise<{ websiteId: string }>;
	},
) {
	try {
		const { websiteId } = await params;

		// Get the website by ID
		const website = await websitesRepository.getById(websiteId);

		if (!website) {
			const errorResponse = NextResponse.json(
				{ error: "Website not found" },
				{ status: 404 },
			);
			return corsMiddleware(request, errorResponse);
		}

		// Get the chat configuration
		const chatConfig =
			await chatConfigurationsRepository.createDefaultIfNotExists(websiteId);

		// Check if the domain is allowed
		const origin = request.headers.get("origin");
		console.log(
			`[Widget Config API] Request origin: ${
				origin || "none"
			} for website ${websiteId}`,
		);

		// If there's no origin, allow the request (likely a direct API call)
		if (!origin) {
			console.log(
				"[Widget Config API] No origin provided, allowing the request",
			);
		}
		// If there's an origin and we have allowed domains configured
		else if (
			chatConfig.allowedDomains &&
			Array.isArray(chatConfig.allowedDomains) &&
			chatConfig.allowedDomains.length > 0
		) {
			try {
				// Extract domain from origin (e.g., https://example.com -> example.com)
				const originDomain = new URL(origin).hostname;
				console.log(
					`[Widget Config API] Checking if domain ${originDomain} is allowed`,
				);
				console.log(
					"[Widget Config API] Allowed domains:",
					chatConfig.allowedDomains,
				);

				// Check if the domain or any of its parent domains are in the allowed list
				const isDomainAllowed = chatConfig.allowedDomains.some(
					(allowedDomain) => {
						try {
							if (!allowedDomain) return false;

							// Handle case where allowedDomain might not be a valid URL
							let allowedDomainHost: string;
							try {
								// Try to parse as URL first
								allowedDomainHost = new URL(allowedDomain).hostname;
							} catch (e) {
								// If it's not a valid URL, use it as is (assuming it's just a domain)
								allowedDomainHost = allowedDomain;
							}

							// Check if domain matches exactly or is a subdomain
							const isAllowed =
								originDomain === allowedDomainHost ||
								originDomain.endsWith(`.${allowedDomainHost}`);

							console.log(
								`[Widget Config API] Checking ${originDomain} against ${allowedDomainHost}: ${
									isAllowed ? "allowed" : "not allowed"
								}`,
							);
							return isAllowed;
						} catch (error) {
							console.error(
								`[Widget Config API] Error checking domain ${allowedDomain}:`,
								error,
							);
							return false;
						}
					},
				);

				if (!isDomainAllowed) {
					console.log(
						`[Widget Config API] Domain ${originDomain} not allowed for website ${websiteId}`,
					);
					const errorResponse = NextResponse.json(
						{
							error: "Domain not allowed",
							message: `The domain ${originDomain} is not in the allowed domains list for this chat widget. Please contact the widget owner to add this domain.`,
						},
						{ status: 403 },
					);
					return corsMiddleware(request, errorResponse, {
						allowedOrigins: [origin], // Return the actual origin for proper CORS error handling
					});
				}
				console.log(
					`[Widget Config API] Domain ${originDomain} is allowed for website ${websiteId}`,
				);
			} catch (error) {
				console.error("[Widget Config API] Error in domain validation:", error);
				// If there's an error in validation, we'll allow the request to proceed
				// This prevents blocking legitimate requests due to validation errors
			}
		} else {
			// If no allowed domains are configured, allow all domains
			console.log(
				`[Widget Config API] No domain restrictions for website ${websiteId}, allowing all domains`,
			);
		}

		// Convert position from BOTTOM_RIGHT to bottom-right format
		const position = chatConfig.position?.toLowerCase().replace("_", "-");

		// Return the chat configuration with CORS headers
		const response = NextResponse.json({
			websiteId,
			primaryColor: chatConfig.primaryColor,
			secondaryColor: chatConfig.secondaryColor,
			position,
			welcomeMessage: chatConfig.welcomeMessage,
			headerText: chatConfig.headerText,
		});

		// Use the allowed domains for CORS if available, otherwise allow all origins
		console.log("[Widget Config API] Setting up CORS options for response");

		let corsOptions = {
			allowedOrigins: ["*"],
			allowedMethods: [
				"GET",
				"POST",
				"PUT",
				"DELETE",
				"OPTIONS",
				"PATCH",
				"HEAD",
			],
			allowedHeaders: ["Content-Type", "Authorization"],
			credentials: true,
		};

		// If we have allowed domains and an origin
		if (
			chatConfig.allowedDomains &&
			Array.isArray(chatConfig.allowedDomains) &&
			chatConfig.allowedDomains.length > 0 &&
			origin
		) {
			try {
				// Extract domain from origin
				const originDomain = new URL(origin).hostname;

				// Check if the domain is in the allowed list
				const isDomainAllowed = chatConfig.allowedDomains.some(
					(allowedDomain) => {
						try {
							if (!allowedDomain) return false;

							// Handle case where allowedDomain might not be a valid URL
							let allowedDomainHost: string;
							try {
								allowedDomainHost = new URL(allowedDomain).hostname;
							} catch (e) {
								allowedDomainHost = allowedDomain;
							}

							return (
								originDomain === allowedDomainHost ||
								originDomain.endsWith(`.${allowedDomainHost}`)
							);
						} catch (error) {
							return false;
						}
					},
				);

				if (isDomainAllowed) {
					console.log(
						`[Widget Config API] Using specific origin for CORS: ${origin}`,
					);
					corsOptions = {
						allowedOrigins: [origin],
						allowedMethods: [
							"GET",
							"POST",
							"PUT",
							"DELETE",
							"OPTIONS",
							"PATCH",
							"HEAD",
						],
						allowedHeaders: ["Content-Type", "Authorization"],
						credentials: true,
					};
				} else {
					console.log(
						"[Widget Config API] Using wildcard for CORS as fallback",
					);
				}
			} catch (error) {
				console.error(
					"[Widget Config API] Error setting up CORS options:",
					error,
				);
			}
		} else {
			console.log(
				"[Widget Config API] Using wildcard for CORS (no restrictions)",
			);
		}

		return corsMiddleware(request, response, corsOptions);
	} catch (error) {
		console.error("Error fetching chat configuration for widget:", error);
		const errorResponse = NextResponse.json(
			{ error: "Failed to fetch chat configuration" },
			{ status: 500 },
		);
		return corsMiddleware(request, errorResponse);
	}
}
