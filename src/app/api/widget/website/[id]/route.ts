import { websitesRepository } from "@/lib/db/repositories";
import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

/**
 * Public API route to get basic website information for the widget
 * This route does not require authentication
 */
export async function GET(
	_request: Request,
	{
		params,
	}: {
		params: Promise<{ id: string }>;
	},
) {
	try {
		const { id } = await params;

		// Get the website by ID
		const website = await websitesRepository.getById(id);

		if (!website) {
			return NextResponse.json({ error: "Website not found" }, { status: 404 });
		}

		// Return only the necessary information for the widget
		return NextResponse.json({
			id: website.id,
			name: website.name,
			url: website.url,
		});
	} catch (error) {
		console.error("Error fetching website for widget:", error);
		return NextResponse.json(
			{ error: "Failed to fetch website information" },
			{ status: 500 },
		);
	}
}
