import { chatAnalyticsRepository } from "@/lib/db/repositories";
import {
	corsMiddleware,
	handleCorsPreflightRequest,
} from "@/lib/middleware/cors";
import {
	AnalyticsEvent,
	analyticsEventSchema,
} from "@/lib/validation/analytics";
import { type NextRequest, NextResponse } from "next/server";
import { v4 as uuidv4 } from "uuid";

export function OPTIONS(request: NextRequest) {
	return handleCorsPreflightRequest(request);
}

export async function POST(request: NextRequest) {
	console.log("🔍 [ANALYTICS] track-event API called");
	try {
		const data = await request.json();
		const parseResult = analyticsEventSchema.safeParse(data);
		if (!parseResult.success) {
			return NextResponse.json(
				{ error: "Invalid request body", details: parseResult.error.flatten() },
				{ status: 400 },
			);
		}
		const { websiteId, visitorId, conversationId, eventType, metadata } =
			parseResult.data;

		console.log("🔍 [ANALYTICS] Event data:", {
			websiteId,
			visitorId,
			conversationId,
			eventType,
		});

		if (!websiteId || !visitorId || !conversationId || !eventType) {
			console.log("🔍 [ANALYTICS] Missing required fields");
			return NextResponse.json(
				{ error: "Missing required fields" },
				{ status: 400 },
			);
		}

		// Create a new event
		try {
			// Convert conversationId to UUID if it's not already
			let validConversationId = conversationId;
			if (
				!conversationId.match(
					/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
				)
			) {
				console.log("🔍 [ANALYTICS] Converting conversationId to UUID");
				validConversationId = uuidv4();
			}

			await chatAnalyticsRepository.createEvent({
				id: uuidv4(),
				websiteId,
				visitorId,
				conversationId: validConversationId,
				eventType,
				metadata: metadata ? JSON.stringify(metadata) : "{}",
				timestamp: new Date().toISOString(),
			});

			console.log(`🔍 [ANALYTICS] Successfully created ${eventType} event`);
		} catch (error) {
			console.error("🔍 [ANALYTICS] Error in createEvent:", error);
			// Continue execution even if there's an error
		}

		const response = NextResponse.json({ success: true });
		return corsMiddleware(request, response);
	} catch (error) {
		console.error("Error tracking event:", error);
		const errorResponse = NextResponse.json(
			{ error: "Failed to track event" },
			{ status: 500 },
		);
		return corsMiddleware(request, errorResponse);
	}
}
