import { chatAnalyticsRepository } from "@/lib/db/repositories";
import { type NextRequest, NextResponse } from "next/server";
import { v4 as uuidv4 } from "uuid";

export async function POST(request: NextRequest) {
	console.log("🔍 [ANALYTICS] track-performance API called");
	try {
		const data = await request.json();
		const { websiteId, conversationId, responseTime, ragUsed, ragResultCount } =
			data;

		console.log("🔍 [ANALYTICS] Performance data:", {
			websiteId,
			conversationId,
			responseTime,
			ragUsed,
			ragResultCount,
		});

		if (!websiteId || !conversationId) {
			console.log("🔍 [ANALYTICS] Missing required fields");
			return NextResponse.json(
				{ error: "Missing required fields" },
				{ status: 400 },
			);
		}

		// Create a new performance metric
		try {
			// Convert conversationId to UUID if it's not already
			let validConversationId = conversationId;
			if (
				!conversationId.match(
					/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
				)
			) {
				console.log("🔍 [ANALYTICS] Converting conversationId to UUID");
				validConversationId = uuidv4();
			}

			await chatAnalyticsRepository.createPerformanceMetric({
				id: uuidv4(),
				websiteId,
				conversationId: validConversationId,
				responseTime,
				tokensUsed: 0, // We don't have this information yet
				ragUsed: ragUsed || false,
				ragResultCount: ragResultCount || 0,
				timestamp: new Date().toISOString(),
			});

			console.log("🔍 [ANALYTICS] Successfully created performance metric");
		} catch (error) {
			console.error("🔍 [ANALYTICS] Error in createPerformanceMetric:", error);
			// Continue execution even if there's an error
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Error tracking performance:", error);
		return NextResponse.json(
			{ error: "Failed to track performance" },
			{ status: 500 },
		);
	}
}
