import type { Plan } from "@/hooks/useUserPlan";
import { db } from "@/lib/db";
import { plansTable, usersTable } from "@/lib/db/schema";
import { planLimitsService } from "@/lib/services/plan-limits";
import { auth } from "@clerk/nextjs/server";
import { eq, sql } from "drizzle-orm";
import { withErrorHandling } from "../../../../utils/api";
import { ApiError } from "../../../../utils/api";

interface DBPlan {
	id: string;
	name: string;
	description: string | null;
	price: number;
	websiteLimit: number;
	pagesPerWebsiteLimit: number;
	messagesPerDayLimit: number;
	features: string | null;
	isActive: string | null;
	createdAt: string;
	updatedAt: string;
}

export const dynamic = "force-dynamic";

export async function GET() {
	return withErrorHandling(async () => {
		const session = await auth();
		const clerkUserId = session.userId;

		if (!clerkUserId) {
			throw new ApiError("Unauthorized", 401);
		}

		// Get the user from the database
		const [user] = await db
			.select()
			.from(usersTable)
			.where(eq(usersTable.clerkId, clerkUserId));

		if (!user) {
			throw new ApiError("User not found", 404);
		}

		// Get the user's plan
		let plan: DBPlan | undefined;
		if (user.planId) {
			[plan] = await db
				.select()
				.from(plansTable)
				.where(eq(plansTable.id, user.planId));
		}

		// If no plan is found, get the free plan
		if (!plan) {
			[plan] = await db
				.select()
				.from(plansTable)
				.where(eq(plansTable.name, "Free"));
		}

		// If still no plan is found, use default limits
		const websiteLimit = plan?.websiteLimit || 1;
		const pagesPerWebsiteLimit = plan?.pagesPerWebsiteLimit || 50;
		const messagesPerDayLimit = plan?.messagesPerDayLimit || 100;

		// Get website count
		const websiteLimitCheck = await planLimitsService.checkWebsiteLimit(
			user.id,
		);

		// Get page count (use the first website for simplicity)
		let pagesCount = 0;
		const pagesLimit = pagesPerWebsiteLimit;

		// Get websites for this user using raw SQL
		const websitesResult = await db.execute(
			sql`SELECT id FROM websites WHERE user_id = ${user.id} LIMIT 1`,
		);

		// If we have at least one website, get its page count
		if (websitesResult?.rows && websitesResult.rows.length > 0) {
			const firstWebsiteId = websitesResult.rows[0].id;

			// Count pages for this website using raw SQL
			const pagesResult = await db.execute(
				sql`SELECT COUNT(*) as count FROM website_pages WHERE website_id = ${firstWebsiteId}`,
			);

			if (pagesResult?.rows && pagesResult.rows.length > 0) {
				pagesCount = Number(pagesResult.rows[0].count);
			}
		}

		// Get message count
		const messageLimitCheck = await planLimitsService.checkMessagesLimit(
			user.id,
		);

		return {
			websites: {
				count: websiteLimitCheck.currentCount,
				limit: websiteLimitCheck.limit,
			},
			pages: {
				count: pagesCount,
				limit: pagesLimit,
			},
			messages: {
				count: messageLimitCheck.currentCount,
				limit: messageLimitCheck.limit,
			},
		};
	});
}
