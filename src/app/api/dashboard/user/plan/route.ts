import { db } from "@/lib/db";
import type { Plan } from "@/lib/db/schema";
import { plansTable, usersTable } from "@/lib/db/schema";
import { auth } from "@clerk/nextjs/server";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { withErrorHandling } from "../../../utils/api";
import { ApiError } from "../../../utils/api";

export const dynamic = "force-dynamic";

// Schema for updating user plan
const UpdateUserPlanSchema = z.object({
	planId: z.string().uuid(),
});

export async function GET() {
	return withErrorHandling(async () => {
		const session = await auth();
		const clerkUserId = session.userId;

		if (!clerkUserId) {
			throw new ApiError("Unauthorized", 401);
		}

		// Get the user from the database
		const [user] = await db
			.select()
			.from(usersTable)
			.where(eq(usersTable.clerkId, clerkUserId));

		if (!user) {
			throw new ApiError("User not found", 404);
		}

		// Get the user's plan
		let plan: Plan | undefined;
		if (user.planId) {
			[plan] = await db
				.select()
				.from(plansTable)
				.where(eq(plansTable.id, user.planId));
		}

		// If no plan is found, get the free plan
		if (!plan) {
			[plan] = await db
				.select()
				.from(plansTable)
				.where(eq(plansTable.name, "Free"));
		}

		// If still no plan is found, return a default plan
		if (!plan) {
			plan = {
				id: "free",
				name: "Free",
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
				description: "Basic plan for getting started",
				price: 0,
				websiteLimit: 1,
				pagesPerWebsiteLimit: 50,
				messagesPerDayLimit: 100,
				features: "Basic chat widget, Website crawling, Standard response time",
				isActive: "ACTIVE",
			};
		}

		return {
			...plan,
			subscriptionStatus: user.subscriptionStatus || "FREE",
			subscriptionExpiresAt: user.subscriptionExpiresAt,
		};
	});
}

export async function PATCH(request: Request) {
	return withErrorHandling(async () => {
		const session = await auth();
		const clerkUserId = session.userId;

		if (!clerkUserId) {
			throw new ApiError("Unauthorized", 401);
		}

		// Get the user from the database
		const [user] = await db
			.select()
			.from(usersTable)
			.where(eq(usersTable.clerkId, clerkUserId));

		if (!user) {
			throw new ApiError("User not found", 404);
		}

		// Parse and validate the request body
		const body = await request.json();
		const validatedData = UpdateUserPlanSchema.parse(body);

		// Check if the plan exists
		const [plan] = await db
			.select()
			.from(plansTable)
			.where(eq(plansTable.id, validatedData.planId));

		if (!plan) {
			throw new ApiError("Plan not found", 404);
		}

		// Update the user's plan
		await db
			.update(usersTable)
			.set({
				planId: validatedData.planId,
				subscriptionStatus: plan.price > 0 ? "ACTIVE" : "FREE",
				subscriptionExpiresAt:
					plan.price > 0
						? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
						: null,
				updatedAt: new Date().toISOString(),
			})
			.where(eq(usersTable.id, user.id));

		return { success: true };
	});
}
