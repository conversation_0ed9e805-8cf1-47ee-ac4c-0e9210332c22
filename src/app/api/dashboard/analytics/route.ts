import { db } from "@/lib/db";
import {
	conversationsTable,
	messagesTable,
	websitesTable,
} from "@/lib/db/schema";
import { auth } from "@clerk/nextjs/server";
import { and, count, sql } from "drizzle-orm";
import { withErrorHandling } from "../../utils/api";
import { ApiError } from "../../utils/api";

export const dynamic = "force-dynamic";

export async function GET(request: Request) {
	return withErrorHandling(async () => {
		const session = await auth();
		const userId = session.userId;
		const orgId = session.orgId;

		if (!userId || !orgId) {
			throw new ApiError("Unauthorized", 401);
		}

		const url = new URL(request.url);
		const period = url.searchParams.get("period") || "week";
		let interval: string;
		let compareInterval: string;

		switch (period) {
			case "day":
				interval = "24 hours";
				compareInterval = "48 hours";
				break;
			case "week":
				interval = "7 days";
				compareInterval = "14 days";
				break;
			case "month":
				interval = "30 days";
				compareInterval = "60 days";
				break;
			default:
				throw new ApiError("Invalid period", 400);
		}

		// Get current period metrics
		const currentMetrics = await db
			.select({
				conversations: count(conversationsTable.id),
				messages: count(messagesTable.id),
				activeUsers: sql<string>`COUNT(DISTINCT ${conversationsTable.visitorId})`,
			})
			.from(websitesTable)
			.leftJoin(
				conversationsTable,
				sql`${websitesTable.id} = ${conversationsTable.websiteId} AND ${conversationsTable.createdAt} >= NOW() - INTERVAL '${interval}'`,
			)
			.leftJoin(
				messagesTable,
				sql`${conversationsTable.id} = ${messagesTable.conversationId}`,
			);

		// Get previous period metrics for trend calculation
		const previousMetrics = await db
			.select({
				conversations: count(conversationsTable.id),
				messages: count(messagesTable.id),
				activeUsers: sql<string>`COUNT(DISTINCT ${conversationsTable.visitorId})`,
			})
			.from(websitesTable)
			.leftJoin(
				conversationsTable,
				sql`${websitesTable.id} = ${conversationsTable.websiteId} AND ${conversationsTable.createdAt} >= NOW() - INTERVAL '${compareInterval}' AND ${conversationsTable.createdAt} < NOW() - INTERVAL '${interval}'`,
			)
			.leftJoin(
				messagesTable,
				sql`${conversationsTable.id} = ${messagesTable.conversationId}`,
			);

		const current = currentMetrics[0] || {
			conversations: 0,
			messages: 0,
			activeUsers: "0",
		};
		const previous = previousMetrics[0] || {
			conversations: 0,
			messages: 0,
			activeUsers: "0",
		};

		// Calculate trends (percentage change)
		const calculateTrend = (current: number, previous: number) => {
			if (previous === 0) return current > 0 ? 100 : 0;
			return ((current - previous) / previous) * 100;
		};

		return {
			period,
			conversations: {
				total: Number(current.conversations),
				trend: calculateTrend(
					Number(current.conversations),
					Number(previous.conversations),
				),
			},
			messages: {
				total: Number(current.messages),
				trend: calculateTrend(
					Number(current.messages),
					Number(previous.messages),
				),
			},
			engagement: {
				activeUsers: Number(current.activeUsers),
				trend: calculateTrend(
					Number(current.activeUsers),
					Number(previous.activeUsers),
				),
			},
		};
	});
}
