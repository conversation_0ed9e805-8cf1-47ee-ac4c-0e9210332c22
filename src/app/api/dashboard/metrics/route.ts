import { db } from "@/lib/db";
import {
	conversationsTable,
	messagesTable,
	websitesTable,
} from "@/lib/db/schema";
import { auth } from "@clerk/nextjs/server";
import { and, count, sql } from "drizzle-orm";
import { withErrorHandling } from "../../utils/api";
import { ApiError } from "../../utils/api";

export const dynamic = "force-dynamic";

export async function GET() {
	return withErrorHandling(async () => {
		const session = await auth();
		const userId = session.userId;
		const orgId = session.orgId;

		if (!userId || !orgId) {
			throw new ApiError("Unauthorized", 401);
		}

		// Get total active websites
		const activeWebsites = await db
			.select({ count: count() })
			.from(websitesTable);

		// Get conversation metrics
		const conversationMetrics = await db
			.select({
				totalConversations: count(),
			})
			.from(conversationsTable)
			.innerJoin(
				websitesTable,
				sql`${websitesTable.id} = ${conversationsTable.websiteId}`,
			);

		// Get total messages
		const messageCount = await db
			.select({ count: count() })
			.from(messagesTable)
			.innerJoin(
				conversationsTable,
				sql`${conversationsTable.id} = ${messagesTable.conversationId}`,
			)
			.innerJoin(
				websitesTable,
				sql`${websitesTable.id} = ${conversationsTable.websiteId}`,
			);

		// Get active conversations (status = 'active')
		const activeConversations = await db
			.select({ count: count() })
			.from(conversationsTable)
			.innerJoin(
				websitesTable,
				sql`${websitesTable.id} = ${conversationsTable.websiteId}`,
			)
			.where(and(sql`${conversationsTable.status} = 'active'`));

		return {
			totalConversations: conversationMetrics[0]?.totalConversations ?? 0,
			totalMessages: messageCount[0]?.count ?? 0,
			averageResponseTime: 0, // This metric needs to be calculated differently or stored separately
			userSatisfactionRate: 0, // This metric needs to be calculated differently or stored separately
			activeWebsites: activeWebsites[0]?.count ?? 0,
			activeConversations: activeConversations[0]?.count ?? 0,
			lastUpdated: new Date().toISOString(),
		};
	});
}
