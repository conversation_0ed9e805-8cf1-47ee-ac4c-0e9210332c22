import { db } from "@/lib/db";
import { plansTable } from "@/lib/db/schema";
import { auth } from "@clerk/nextjs/server";
import { eq } from "drizzle-orm";
import { withErrorHandling } from "../../utils/api";
import { ApiError } from "../../utils/api";

export const dynamic = "force-dynamic";

export async function GET() {
	return withErrorHandling(async () => {
		const session = await auth();
		const clerkUserId = session.userId;

		if (!clerkUserId) {
			throw new ApiError("Unauthorized", 401);
		}

		// Get all active plans
		const plans = await db
			.select()
			.from(plansTable)
			.where(eq(plansTable.isActive, "ACTIVE"));

		return plans;
	});
}
