import { inngest } from "@/inngest/client";
import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

/**
 * API route to manually trigger the scheduled crawl check
 * This is useful for testing and for manually running the scheduled crawl
 * outside of its normal schedule
 */
export async function POST() {
	const { userId } = await auth();

	// Check if the user is authenticated
	if (!userId) {
		return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
	}

	try {
		// Send event to trigger scheduled crawl
		await inngest.send({
			name: "crawler/schedule.trigger",
			data: {},
		});

		return NextResponse.json({
			message: "Scheduled crawl triggered successfully",
		});
	} catch (error) {
		console.error("Error triggering scheduled crawl:", error);
		return NextResponse.json(
			{ error: "Failed to trigger scheduled crawl" },
			{ status: 500 },
		);
	}
}
