import { db } from "@/lib/db";
import { usersTable, websitesTable } from "@/lib/db/schema";
import { planLimitsService } from "@/lib/services/plan-limits";
import { WebsiteCreate, websiteCreateSchema } from "@/lib/validation/website";
import { auth } from "@clerk/nextjs/server";
import { eq } from "drizzle-orm";
import { desc } from "drizzle-orm";
import { websiteSchema } from "../../types/dashboard";
import { withErrorHandling } from "../../utils/api";
import { ApiError } from "../../utils/api";

export const dynamic = "force-dynamic";

export async function GET() {
	return withErrorHandling(async () => {
		const session = await auth();
		const clerkUserId = session.userId;

		if (!clerkUserId) {
			throw new ApiError("Unauthorized", 401);
		}

		// Get the user from the database
		const [user] = await db
			.select()
			.from(usersTable)
			.where(eq(usersTable.clerkId, clerkUserId));

		if (!user) {
			throw new ApiError("User not found", 404);
		}

		// Get all websites for this user
		const websites = await db
			.select()
			.from(websitesTable)
			.where(eq(websitesTable.userId, user.id))
			.orderBy(desc(websitesTable.createdAt));

		return websites;
	});
}

export async function POST(request: Request) {
	return withErrorHandling(async () => {
		const session = await auth();
		const clerkUserId = session.userId;

		if (!clerkUserId) {
			throw new ApiError("Unauthorized", 401);
		}

		// Get the user from the database
		const [user] = await db
			.select()
			.from(usersTable)
			.where(eq(usersTable.clerkId, clerkUserId));

		if (!user) {
			throw new ApiError("User not found", 404);
		}

		// Check if the user has reached their website limit
		const websiteLimitCheck = await planLimitsService.checkWebsiteLimit(
			user.id,
		);

		if (websiteLimitCheck.hasReachedLimit) {
			throw new ApiError(
				`You have reached your limit of ${websiteLimitCheck.limit} websites. Please upgrade your plan to add more websites.`,
				403,
			);
		}

		const body = await request.json();
		const parseResult = websiteCreateSchema.safeParse(body);
		if (!parseResult.success) {
			throw new ApiError("Invalid request body", 400);
		}
		const validatedData = parseResult.data;

		// Create new website associated with the user
		const [website] = await db
			.insert(websitesTable)
			.values({
				userId: user.id,
				name: validatedData.name,
				url: validatedData.url,
			})
			.returning();

		return website;
	});
}
