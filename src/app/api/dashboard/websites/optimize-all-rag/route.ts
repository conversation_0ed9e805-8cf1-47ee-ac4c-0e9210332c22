import { inngest } from "@/inngest/client";
import { auth } from "@clerk/nextjs/server";
import { type NextRequest, NextResponse } from "next/server";

/**
 * API route to manually trigger RAG optimization for all websites
 *
 * @param request - The incoming request
 * @returns A JSON response indicating success or failure
 */
export async function POST(request: NextRequest) {
	const { userId } = await auth();

	// Check if the user is authenticated
	if (!userId) {
		return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
	}

	try {
		// Get the request body to check for force flag
		const body = await request.json().catch(() => ({}));
		const force = body.force === true;

		// Send event to trigger RAG optimization for all websites
		await inngest.send({
			name: "rag/all.optimize",
			data: {
				force,
			},
		});

		return NextResponse.json({
			message: "RAG optimization for all websites triggered successfully",
		});
	} catch (error) {
		console.error("Error triggering RAG optimization for all websites:", error);
		return NextResponse.json(
			{ error: "Failed to trigger RAG optimization for all websites" },
			{ status: 500 },
		);
	}
}
