import { db } from "@/lib/db";
import {
	conversationsTable,
	messagesTable,
	usersTable,
	websitesTable,
} from "@/lib/db/schema";
import { auth } from "@clerk/nextjs/server";
import { and, count, eq, sql } from "drizzle-orm";
import { websiteUpdateSchema } from "../../../types/dashboard";
import { withErrorHandling } from "../../../utils/api";
import { ApiError } from "../../../utils/api";

export const dynamic = "force-dynamic";

export async function GET(
	_request: Request,
	{
		params,
	}: {
		params: Promise<{ id: string }>;
	},
) {
	return withErrorHandling(async () => {
		const session = await auth();
		const clerkUserId = session.userId;
		const { id } = await params;

		if (!clerkUserId) {
			throw new ApiError("Unauthorized", 401);
		}

		// Get the user from the database
		const [user] = await db
			.select()
			.from(usersTable)
			.where(eq(usersTable.clerkId, clerkUserId));

		if (!user) {
			throw new ApiError("User not found", 404);
		}

		// Get website details with metrics
		const [website] = await db
			.select({
				id: websitesTable.id,
				userId: websitesTable.userId,
				name: websitesTable.name,
				url: websitesTable.url,
				createdAt: websitesTable.createdAt,
				updatedAt: websitesTable.updatedAt,
				conversationCount: count(conversationsTable.id),
				messageCount: count(messagesTable.id),
				activeConversations: count(
					sql`CASE WHEN ${conversationsTable.status} = 'active' THEN 1 END`,
				),
			})
			.from(websitesTable)
			.leftJoin(
				conversationsTable,
				sql`${websitesTable.id} = ${conversationsTable.websiteId}`,
			)
			.leftJoin(
				messagesTable,
				sql`${conversationsTable.id} = ${messagesTable.conversationId}`,
			)
			.where(sql`${websitesTable.id} = ${id}`)
			.groupBy(
				websitesTable.id,
				websitesTable.userId,
				websitesTable.name,
				websitesTable.url,
				websitesTable.createdAt,
				websitesTable.updatedAt,
			);

		if (!website) {
			throw new ApiError("Website not found", 404);
		}

		// Check if the website belongs to the user
		if (website.userId !== user.id) {
			throw new ApiError(
				"You don't have permission to access this website",
				403,
			);
		}

		return {
			...website,
			conversationCount: Number(website.conversationCount),
			messageCount: Number(website.messageCount),
			activeConversations: Number(website.activeConversations),
		};
	});
}

export async function PUT(
	request: Request,
	{
		params,
	}: {
		params: Promise<{ id: string }>;
	},
) {
	return withErrorHandling(async () => {
		const session = await auth();
		const clerkUserId = session.userId;
		const { id } = await params;

		if (!clerkUserId) {
			throw new ApiError("Unauthorized", 401);
		}

		// Get the user from the database
		const [user] = await db
			.select()
			.from(usersTable)
			.where(eq(usersTable.clerkId, clerkUserId));

		if (!user) {
			throw new ApiError("User not found", 404);
		}

		const body = await request.json();
		const validatedData = websiteUpdateSchema.parse(body);

		// Check if website exists and belongs to the user
		const [existingWebsite] = await db
			.select()
			.from(websitesTable)
			.where(
				and(
					sql`${websitesTable.id} = ${id}`,
					eq(websitesTable.userId, user.id),
				),
			)
			.limit(1);

		if (!existingWebsite) {
			throw new ApiError(
				"Website not found or you don't have permission to update it",
				404,
			);
		}

		// If URL is being updated, check for duplicates
		if (validatedData.url) {
			const duplicateUrl = await db
				.select()
				.from(websitesTable)
				.where(
					and(
						sql`${websitesTable.url} = ${validatedData.url}`,
						sql`${websitesTable.id} != ${id}`,
					),
				)
				.limit(1);

			if (duplicateUrl.length > 0) {
				throw new ApiError("Website with this URL already exists", 400);
			}
		}

		// Update website
		const [website] = await db
			.update(websitesTable)
			.set({
				...validatedData,
				updatedAt: new Date().toISOString(),
			})
			.where(sql`${websitesTable.id} = ${id}`)
			.returning();

		return website;
	});
}

export async function DELETE(
	_request: Request,
	{
		params,
	}: {
		params: Promise<{ id: string }>;
	},
) {
	return withErrorHandling(async () => {
		const session = await auth();
		const clerkUserId = session.userId;
		const { id } = await params;

		if (!clerkUserId) {
			throw new ApiError("Unauthorized", 401);
		}

		// Get the user from the database
		const [user] = await db
			.select()
			.from(usersTable)
			.where(eq(usersTable.clerkId, clerkUserId));

		if (!user) {
			throw new ApiError("User not found", 404);
		}

		// Check if website exists and belongs to the user
		const [existingWebsite] = await db
			.select()
			.from(websitesTable)
			.where(
				and(
					sql`${websitesTable.id} = ${id}`,
					eq(websitesTable.userId, user.id),
				),
			)
			.limit(1);

		if (!existingWebsite) {
			throw new ApiError(
				"Website not found or you don't have permission to delete it",
				404,
			);
		}

		// Delete the website - all related records will be automatically deleted due to ON DELETE CASCADE
		console.log(`Deleting website ${id}...`);
		const [website] = await db
			.delete(websitesTable)
			.where(sql`${websitesTable.id} = ${id}`)
			.returning();

		return website;
	});
}
