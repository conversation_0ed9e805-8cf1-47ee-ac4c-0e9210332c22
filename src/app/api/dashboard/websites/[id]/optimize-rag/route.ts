import { inngest } from "@/inngest/client";
import { websitesRepository } from "@/lib/db/repositories";
import { auth } from "@clerk/nextjs/server";
import { type NextRequest, NextResponse } from "next/server";

/**
 * API route to manually trigger RAG optimization for a specific website
 *
 * @param request - The incoming request
 * @param params - The route parameters, including the website ID
 * @returns A JSON response indicating success or failure
 */
export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> },
) {
	const { userId } = await auth();
	const { id } = await params;

	// Check if the user is authenticated
	if (!userId) {
		return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
	}

	try {
		// Check if the website exists and belongs to the user
		const website = await websitesRepository.getById(id);
		if (!website) {
			return NextResponse.json({ error: "Website not found" }, { status: 404 });
		}

		// Check if the website belongs to the user
		if (website.userId !== userId) {
			return NextResponse.json(
				{ error: "You don't have permission to optimize this website" },
				{ status: 403 },
			);
		}

		// Get the request body to check for force flag
		const body = await request.json().catch(() => ({}));
		const force = body.force === true;

		// Send event to trigger RAG optimization
		await inngest.send({
			name: "rag/website.optimize",
			data: {
				websiteId: id,
				force,
			},
		});

		return NextResponse.json({
			message: "RAG optimization triggered successfully",
		});
	} catch (error) {
		console.error("Error triggering RAG optimization:", error);
		return NextResponse.json(
			{ error: "Failed to trigger RAG optimization" },
			{ status: 500 },
		);
	}
}
