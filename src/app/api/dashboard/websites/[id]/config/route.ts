import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

import { db } from "@/lib/db";
import { websitesRepository } from "@/lib/db/repositories";
import { usersTable } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export async function GET(
	req: Request,
	{
		params,
	}: {
		params: Promise<{ id: string }>;
	},
) {
	try {
		// Get the website ID from the URL
		const { id } = await params;

		// Get the user from Clerk
		const { userId } = await auth();

		// Check if the user is authenticated
		if (!userId) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Get the website
		const website = await websitesRepository.getById(id);

		// Check if the website exists
		if (!website) {
			return NextResponse.json({ error: "Website not found" }, { status: 404 });
		}

		// Get the user from the database
		const [user] = await db
			.select()
			.from(usersTable)
			.where(eq(usersTable.clerkId, userId));

		if (!user) {
			return NextResponse.json({ error: "User not found" }, { status: 404 });
		}

		// Check if the website belongs to the user
		if (website.userId !== user.id) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Get the crawl configuration
		const crawlConfiguration =
			await websitesRepository.getCrawlConfiguration(id);

		// Return the website with its crawl configuration
		return NextResponse.json({
			id: website.id,
			name: website.name,
			url: website.url,
			crawlFrequency: website.crawlFrequency,
			lastCrawledAt: website.lastCrawledAt,
			status: website.status,
			crawlConfiguration,
		});
	} catch (error) {
		console.error("Error in website config API route:", error);
		return NextResponse.json(
			{ error: "Failed to get website configuration" },
			{ status: 500 },
		);
	}
}
