import { db } from "@/lib/db";
import {
	conversationsRepository,
	messagesRepository,
	websitesRepository,
} from "@/lib/db/repositories";
import { usersTable } from "@/lib/db/schema";
import { auth } from "@clerk/nextjs/server";
import { eq } from "drizzle-orm";
import { type NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> },
) {
	try {
		const id = (await params).id;
		console.log(
			`[Conversations API] Fetching conversations for website ID: ${id}`,
		);

		// Get the website
		const website = await websitesRepository.getById(id);
		if (!website) {
			console.log(`[Conversations API] Website not found: ${id}`);
			return NextResponse.json({ error: "Website not found" }, { status: 404 });
		}

		// Check authentication only if not in development mode
		if (process.env.NODE_ENV !== "development") {
			const session = await auth();
			const clerkUserId = session.userId;

			if (!clerkUserId) {
				console.log("[Conversations API] Unauthorized: No user ID");
				return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
			}

			// Get the user from the database using Clerk ID
			const [user] = await db
				.select()
				.from(usersTable)
				.where(eq(usersTable.clerkId, clerkUserId));

			if (!user) {
				console.log(
					`[Conversations API] User not found for Clerk ID: ${clerkUserId}`,
				);
				return NextResponse.json({ error: "User not found" }, { status: 404 });
			}

			// Check if the website belongs to the user
			if (website.userId !== user.id) {
				console.log(
					`[Conversations API] Unauthorized: Website ${id} does not belong to user ${user.id}`,
				);
				return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
			}
		}

		// Get pagination parameters
		const searchParams = request.nextUrl.searchParams;
		const page = Number.parseInt(searchParams.get("page") || "1");
		const pageSize = Number.parseInt(searchParams.get("pageSize") || "10");
		const includeMessages = searchParams.get("includeMessages") === "true";

		// Calculate offset
		const offset = (page - 1) * pageSize;

		// Get conversations with pagination
		console.log(
			`[Conversations API] Fetching conversations with pageSize=${pageSize}, offset=${offset}`,
		);
		const conversations = await conversationsRepository.getByWebsiteId(
			id,
			pageSize,
			offset,
		);
		console.log(
			`[Conversations API] Found ${conversations.length} conversations`,
		);

		// Get total count for pagination
		const totalCount = await conversationsRepository.countByWebsiteId(id);
		console.log(`[Conversations API] Total conversation count: ${totalCount}`);

		const pageCount = Math.ceil(totalCount / pageSize);
		console.log(`[Conversations API] Page count: ${pageCount}`);

		// If includeMessages is true, fetch messages for each conversation
		if (includeMessages && conversations.length > 0) {
			console.log(
				`[Conversations API] Including messages for ${conversations.length} conversations`,
			);
			const conversationsWithMessages = await Promise.all(
				conversations.map(async (conversation) => {
					const messages = await messagesRepository.getByConversationId(
						conversation.id,
					);
					console.log(
						`[Conversations API] Found ${messages.length} messages for conversation ${conversation.id}`,
					);
					return {
						...conversation,
						messages,
					};
				}),
			);

			const response = {
				conversations: conversationsWithMessages,
				pagination: {
					page,
					pageSize,
					total: totalCount,
					pageCount,
				},
			};
			console.log(
				`[Conversations API] Returning response with ${conversationsWithMessages.length} conversations`,
			);
			return NextResponse.json(response);
		}

		const response = {
			conversations,
			pagination: {
				page,
				pageSize,
				total: totalCount,
				pageCount,
			},
		};
		console.log(
			`[Conversations API] Returning response with ${conversations.length} conversations`,
		);
		return NextResponse.json(response);
	} catch (error) {
		console.error("Error fetching website conversations:", error);
		// Log more detailed error information
		if (error instanceof Error) {
			console.error(`Error name: ${error.name}, message: ${error.message}`);
			console.error(`Stack trace: ${error.stack}`);
		}

		return NextResponse.json(
			{
				error: "Failed to fetch website conversations",
				details: error instanceof Error ? error.message : String(error),
			},
			{ status: 500 },
		);
	}
}
