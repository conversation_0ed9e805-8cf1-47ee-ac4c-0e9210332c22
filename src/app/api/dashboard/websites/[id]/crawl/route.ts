import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { z } from "zod";

import { inngest } from "@/inngest/client";
import { db } from "@/lib/db";
import {
	crawlOperationsRepository,
	websitesRepository,
} from "@/lib/db/repositories";
import { usersTable } from "@/lib/db/schema";
import { WebsiteStatus } from "@/lib/db/schema/websites";
import { planLimitsService } from "@/lib/services/plan-limits";
import { CrawlRequest, crawlRequestSchema } from "@/lib/validation/crawl";
import { eq } from "drizzle-orm";

// Define the schema for the request body
const CrawlRequestSchema = z.object({
	maxPages: z.number().optional(),
	maxDepth: z.number().optional(),
	includePatterns: z.array(z.string()).optional(),
	excludePatterns: z.array(z.string()).optional(),
	generateEmbeddings: z.boolean().optional(),
	crawlFrequency: z.enum(["DAILY", "WEEKLY", "MONTHLY", "MANUAL"]).optional(),
	respectRobotsTxt: z.boolean().optional(),
	crawlDelay: z.number().optional(),
	incrementalCrawl: z.boolean().optional(),
});

export async function POST(
	req: Request,
	{
		params,
	}: {
		params: Promise<{ id: string }>;
	},
) {
	try {
		// Get the website ID from the URL
		const { id } = await params;

		// Get the user from Clerk
		const { userId } = await auth();

		// Check if the user is authenticated
		if (!userId) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Parse the request body
		const body = await req.json();
		const parseResult = crawlRequestSchema.safeParse(body);
		if (!parseResult.success) {
			return NextResponse.json(
				{ error: "Invalid request body", details: parseResult.error.flatten() },
				{ status: 400 },
			);
		}
		const validatedData = parseResult.data;

		// Get the website
		const website = await websitesRepository.getById(id);

		// Check if the website exists
		if (!website) {
			return NextResponse.json({ error: "Website not found" }, { status: 404 });
		}

		// Check if the website is already being crawled
		if (website.status === WebsiteStatus.CRAWLING) {
			return NextResponse.json(
				{ error: "Website is already being crawled" },
				{ status: 400 },
			);
		}

		// Get the user from the database
		const [user] = await db
			.select()
			.from(usersTable)
			.where(eq(usersTable.clerkId, userId));

		if (!user) {
			return NextResponse.json({ error: "User not found" }, { status: 404 });
		}

		// Check if the website has reached its page limit
		const pageLimitCheck = await planLimitsService.checkPagesLimit(id, user.id);

		if (pageLimitCheck.hasReachedLimit) {
			return NextResponse.json(
				{
					error: `You have reached your limit of ${pageLimitCheck.limit} pages for this website. Please upgrade your plan to crawl more pages.`,
					currentCount: pageLimitCheck.currentCount,
					limit: pageLimitCheck.limit,
				},
				{ status: 403 },
			);
		}

		// Update the website with crawl frequency if provided
		if (validatedData.crawlFrequency) {
			await websitesRepository.update(id, {
				crawlFrequency: validatedData.crawlFrequency,
			});
		}

		// Get default configuration values
		const defaultConfig = {
			maxPages: 100,
			maxDepth: 3,
			includePatterns: [],
			excludePatterns: [
				"/wp-admin",
				"/wp-login",
				"/login",
				"/admin",
				"/logout",
				"/cart",
				"/checkout",
				".pdf",
				".jpg",
				".png",
				".gif",
				".zip",
				".doc",
				".xls",
			],
			generateEmbeddings: true,
			crawlFrequency: "WEEKLY",
			respectRobotsTxt: true,
			crawlDelay: 1000,
			incrementalCrawl: true,
		};

		// Merge default values with validated data to ensure all required fields are present
		const completeConfig = {
			...defaultConfig,
			...validatedData,
		};

		// Save the crawl configuration for future use
		await websitesRepository.saveCrawlConfiguration(id, completeConfig);

		// Create a new crawl operation
		const operation = await crawlOperationsRepository.create({
			websiteId: id,
			status: "PENDING",
			// biome-ignore lint/suspicious/noExplicitAny: Using any type for configuration JSON
			configuration: completeConfig as any,
		});

		// Update website status to CRAWLING
		await websitesRepository.updateStatus(id, WebsiteStatus.CRAWLING);

		// Trigger the crawl process using Inngest
		await inngest.send({
			name: "crawler/website.crawl",
			data: {
				websiteId: id,
				configuration: completeConfig,
				operationId: operation.id, // Pass the operation ID to avoid creating a duplicate
			},
		});

		// Return a success response
		return NextResponse.json({
			message: "Crawling started",
			websiteId: id,
			operationId: operation.id,
		});
	} catch (error) {
		console.error("Error in crawl API route:", error);
		return NextResponse.json(
			{ error: "Failed to start crawling" },
			{ status: 500 },
		);
	}
}
