import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

import { db } from "@/lib/db";
import {
	crawlOperationsRepository,
	websitesRepository,
} from "@/lib/db/repositories";
import { usersTable } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export async function GET(
	req: Request,
	{
		params,
	}: {
		params: Promise<{ id: string }>;
	},
) {
	try {
		const { id } = await params;
		const { userId } = await auth();
		const url = new URL(req.url);

		// Get pagination parameters from query string
		const page = Number.parseInt(url.searchParams.get("page") || "1", 10);
		const pageSize = Number.parseInt(
			url.searchParams.get("pageSize") || "10",
			10,
		);

		if (!userId) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Get the website
		const website = await websitesRepository.getById(id);

		if (!website) {
			return NextResponse.json({ error: "Website not found" }, { status: 404 });
		}

		// Check if the user owns the website
		const [user] = await db
			.select()
			.from(usersTable)
			.where(eq(usersTable.clerkId, userId));

		if (!user) {
			return NextResponse.json({ error: "User not found" }, { status: 404 });
		}

		if (website.userId !== user.id) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Get the crawl operations with pagination
		const { operations, total } =
			await crawlOperationsRepository.getByWebsiteId(id, page, pageSize);

		return NextResponse.json({
			operations,
			pagination: {
				page,
				pageSize,
				total,
				pageCount: Math.ceil(total / pageSize),
			},
		});
	} catch (error) {
		console.error("Error fetching crawl operations:", error);
		return NextResponse.json(
			{ error: "Failed to fetch crawl operations" },
			{ status: 500 },
		);
	}
}
