import { env } from "@/env";
import {
  chatConfigurationsRepository,
  websitesRepository,
} from "@/lib/db/repositories";
import type { ChatConfiguration } from "@/lib/db/schema";
import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { ApiError, withErrorHandling } from "../../../../utils/api";

// Enable console logging for debugging
const debug = true;
const log = (...args: unknown[]) => {
  if (debug) {
    console.log("[embed-code]", ...args);
  }
};

export const dynamic = "force-dynamic";

export async function GET(
  _request: Request,
  {
    params,
  }: {
    params: Promise<{ id: string }>;
  }
) {
  return withErrorHandling(async () => {
    const { id } = await params;
    log("GET request for website ID:", id);

    const { userId } = await auth();
    log("User ID:", userId);

    if (!userId) {
      log("Unauthorized: No user ID");
      throw new ApiError("Unauthorized", 401);
    }

    try {
      // Check if the website exists and belongs to the user
      const website = await websitesRepository.getById(id);
      log("Website found:", website ? "yes" : "no");

      if (!website) {
        throw new ApiError("Website not found", 404);
      }

      // Get the chat configuration
      log("Creating or getting chat configuration");
      const chatConfig =
        await chatConfigurationsRepository.createDefaultIfNotExists(id);
      log("Chat config:", chatConfig);

      // Generate the embed code
      log("Generating embed code");
      const embedCode = generateEmbedCode(id, chatConfig);
      log("Embed code generated");

      return { embedCode };
    } catch (error) {
      log("Error in GET handler:", error);
      throw error;
    }
  });
}

/**
 * Generate the embed code for a website
 */
function generateEmbedCode(websiteId: string, chatConfig: ChatConfiguration) {
  // Convert position from BOTTOM_RIGHT to bottom-right format
  const position = chatConfig.position?.toLowerCase().replace("_", "-");

  // Get the app URL from environment or use a fallback
  const appUrl =
    env.NEXT_PUBLIC_APP_URL ||
    (typeof window !== "undefined"
      ? window.location.origin
      : "https://bublai.com");

  return `<!-- Bubl Embed Code v2.0 -->
<script>
  (function(w, d, s, o) {
    // Create script element
    var js = d.createElement(s);
    js.async = true;
    js.src = '${appUrl}/widget/v2/loader.js';

    // Add initialization data
    w.Bubl = w.Bubl || {
      config: {
        websiteId: '${websiteId}',
        primaryColor: '${chatConfig.primaryColor}',
        secondaryColor: '${chatConfig.secondaryColor}',
        position: '${position}',
        welcomeMessage: '${chatConfig.welcomeMessage?.replace(/'/g, "\\'")}',
        headerText: '${chatConfig.headerText?.replace(/'/g, "\\'")}',
        initiallyOpen: false,
        apiBaseUrl: '${appUrl}'
      },
      // Optional callback when widget is ready
      onReady: function() {
        console.log('Bubl widget v2 is ready');
      }
    };

    // Append script to document
    var s = d.getElementsByTagName(s)[0];
    s.parentNode.insertBefore(js, s);
  })(window, document, 'script');
</script>
<!-- End Bubl Embed Code v2.0 -->`;
}
