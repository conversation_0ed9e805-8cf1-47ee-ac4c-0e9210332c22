import { db } from "@/lib/db";
import {
	chatConfigurationsRepository,
	websitesRepository,
} from "@/lib/db/repositories";
import { ChatConfigurationSchema } from "@/lib/db/schema/chatConfigurations";
import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { z } from "zod";
import { ApiError, withErrorHandling } from "../../../../utils/api";

// Enable console logging for debugging
const debug = true;
const log = (...args: unknown[]) => {
	if (debug) {
		console.log("[chat-config]", ...args);
	}
};

export const dynamic = "force-dynamic";

// Schema for updating chat configuration
const UpdateChatConfigSchema = z.object({
	name: z.string().optional(),
	primaryColor: z.string().optional(),
	secondaryColor: z.string().optional(),
	headerText: z.string().optional(),
	welcomeMessage: z.string().optional(),
	position: z.enum(["BOTTOM_RIGHT", "BOTTOM_LEFT"]).optional(),
	isActive: z.boolean().optional(),
	allowedDomains: z.array(z.string()).optional(),
});

export async function GET(
	_request: Request,
	{
		params,
	}: {
		params: Promise<{ id: string }>;
	},
) {
	return withErrorHandling(async () => {
		const { id } = await params;
		log("GET request for website ID:", id);

		const { userId } = await auth();
		log("User ID:", userId);

		if (!userId) {
			log("Unauthorized: No user ID");
			throw new ApiError("Unauthorized", 401);
		}

		try {
			// Check if the website exists and belongs to the user
			const website = await websitesRepository.getById(id);
			log("Website found:", website ? "yes" : "no");

			if (!website) {
				throw new ApiError("Website not found", 404);
			}

			// Get or create a default chat configuration
			log("Creating or getting chat configuration");
			const chatConfig =
				await chatConfigurationsRepository.createDefaultIfNotExists(id);
			log("Chat config:", chatConfig);

			return chatConfig;
		} catch (error) {
			log("Error in GET handler:", error);
			throw error;
		}
	});
}

export async function PUT(
	request: Request,
	{
		params,
	}: {
		params: Promise<{ id: string }>;
	},
) {
	return withErrorHandling(async () => {
		const { id } = await params;
		log("PUT request for website ID:", id);

		const { userId } = await auth();
		log("User ID:", userId);

		if (!userId) {
			log("Unauthorized: No user ID");
			throw new ApiError("Unauthorized", 401);
		}

		try {
			// Check if the website exists and belongs to the user
			const website = await websitesRepository.getById(id);
			log("Website found:", website ? "yes" : "no");

			if (!website) {
				throw new ApiError("Website not found", 404);
			}

			// Get the request body
			const body = await request.json();
			log("Request body:", body);

			const validatedData = UpdateChatConfigSchema.parse(body);
			log("Validated data:", validatedData);

			// Get the current chat configuration or create a default one
			log("Creating or getting chat configuration");
			const chatConfig =
				await chatConfigurationsRepository.createDefaultIfNotExists(id);
			log("Chat config before update:", chatConfig);

			// Update the chat configuration
			log("Updating chat configuration with ID:", chatConfig.id);
			log("Update data:", JSON.stringify(validatedData));

			try {
				const updatedConfig = await chatConfigurationsRepository.update(
					chatConfig.id,
					validatedData,
				);
				log("Updated config:", updatedConfig);

				return updatedConfig;
			} catch (error) {
				log("Error updating chat configuration:", error);
				throw new ApiError(
					`Failed to update chat configuration: ${
						error instanceof Error ? error.message : "Unknown error"
					}`,
					500,
				);
			}
		} catch (error) {
			log("Error in PUT handler:", error);
			throw error;
		}
	});
}
