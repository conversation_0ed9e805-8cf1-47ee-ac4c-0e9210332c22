import { db } from "@/lib/db";
import {
	websitePagesRepository,
	websitesRepository,
} from "@/lib/db/repositories";
import { usersTable } from "@/lib/db/schema";
import { auth } from "@clerk/nextjs/server";
import { eq } from "drizzle-orm";
import { type NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> },
) {
	try {
		const session = await auth();
		const clerkUserId = session.userId;

		if (!clerkUserId) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const id = (await params).id;

		// Get the user from the database using Clerk ID
		const [user] = await db
			.select()
			.from(usersTable)
			.where(eq(usersTable.clerkId, clerkUserId));

		if (!user) {
			return NextResponse.json({ error: "User not found" }, { status: 404 });
		}

		// Get the website to verify ownership
		const website = await websitesRepository.getById(id);
		if (!website) {
			return NextResponse.json({ error: "Website not found" }, { status: 404 });
		}

		// Check if the website belongs to the user
		if (website.userId !== user.id) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Get pagination parameters
		const searchParams = request.nextUrl.searchParams;
		const page = Number.parseInt(searchParams.get("page") || "1");
		const pageSize = Number.parseInt(searchParams.get("pageSize") || "10");

		// Calculate offset
		const offset = (page - 1) * pageSize;

		// Get pages with pagination
		const pages = await websitePagesRepository.getByWebsiteId(
			id,
			pageSize,
			offset,
		);

		// Get total count for pagination
		const totalPages = await websitePagesRepository.countByWebsiteId(id);

		return NextResponse.json({
			pages,
			pagination: {
				page,
				pageSize,
				total: totalPages,
				pageCount: Math.ceil(totalPages / pageSize),
			},
		});
	} catch (error) {
		console.error("Error fetching website pages:", error);
		return NextResponse.json(
			{ error: "Failed to fetch website pages" },
			{ status: 500 },
		);
	}
}
