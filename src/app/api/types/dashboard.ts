import { z } from "zod";

// Base response type for all API endpoints
export interface ApiResponse<T> {
	success: boolean;
	data: T;
	error?: string;
}

// Metrics types
export interface DashboardMetrics {
	totalConversations: number;
	totalMessages: number;
	averageResponseTime: number;
	userSatisfactionRate: number;
	activeWebsites: number;
	lastUpdated: string;
}

// Website types
export interface Website {
	id: string;
	name: string;
	url: string;
	createdAt: string;
	updatedAt: string;
	isActive: boolean;
	organizationId: string;
}

export interface WebsiteStats extends Website {
	conversationCount: number;
	messageCount: number;
	averageResponseTime: number;
	satisfactionRate: number;
}

// Analytics types
export interface AnalyticsData {
	period: string;
	conversations: {
		total: number;
		trend: number; // percentage change
	};
	messages: {
		total: number;
		trend: number;
	};
	performance: {
		averageResponseTime: number;
		trend: number;
	};
	engagement: {
		activeUsers: number;
		trend: number;
	};
}

// Validation schemas
export const websiteSchema = z.object({
	name: z.string().min(1).max(100),
	url: z.string().url(),
	isActive: z.boolean().optional().default(true),
});

export type CreateWebsiteRequest = z.infer<typeof websiteSchema>;

export const websiteUpdateSchema = websiteSchema.partial();

export type UpdateWebsiteRequest = z.infer<typeof websiteUpdateSchema>;
