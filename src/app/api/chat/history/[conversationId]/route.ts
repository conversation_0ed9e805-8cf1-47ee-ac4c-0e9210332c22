import { ConversationMemory } from "@/lib/ai/memory/conversation-memory";
import { db } from "@/lib/db";
import { websitesRepository } from "@/lib/db/repositories";
import { usersTable } from "@/lib/db/schema";
import { auth } from "@clerk/nextjs/server";
import { eq } from "drizzle-orm";

// Initialize conversation memory
const conversationMemory = new ConversationMemory();

export async function GET(
	_req: Request,
	{
		params,
	}: {
		params: Promise<{ conversationId: string }>;
	},
) {
	try {
		const { conversationId } = await params;

		// Get the conversation
		const conversation =
			await conversationMemory.getConversation(conversationId);

		if (!conversation) {
			return new Response(JSON.stringify({ error: "Conversation not found" }), {
				status: 404,
				headers: {
					"Content-Type": "application/json",
				},
			});
		}

		// For authenticated routes, verify ownership
		const session = await auth();
		if (session.userId) {
			// Get the user from the database using Clerk ID
			const [user] = await db
				.select()
				.from(usersTable)
				.where(eq(usersTable.clerkId, session.userId));

			if (!user) {
				return new Response(JSON.stringify({ error: "User not found" }), {
					status: 404,
					headers: {
						"Content-Type": "application/json",
					},
				});
			}

			// Get the website to verify ownership
			const website = await websitesRepository.getById(conversation.websiteId);
			if (!website) {
				return new Response(JSON.stringify({ error: "Website not found" }), {
					status: 404,
					headers: {
						"Content-Type": "application/json",
					},
				});
			}

			// Check if the website belongs to the user
			if (website.userId !== user.id) {
				return new Response(JSON.stringify({ error: "Unauthorized" }), {
					status: 401,
					headers: {
						"Content-Type": "application/json",
					},
				});
			}
		}

		// Get all messages for the conversation
		const messages = await conversationMemory.getMessages(conversationId);

		// Return the messages
		return new Response(
			JSON.stringify({
				conversation,
				messages,
			}),
			{
				headers: {
					"Content-Type": "application/json",
				},
			},
		);
	} catch (error) {
		console.error("Error retrieving conversation history:", error);
		return new Response(
			JSON.stringify({ error: "Failed to retrieve conversation history" }),
			{
				status: 500,
				headers: {
					"Content-Type": "application/json",
				},
			},
		);
	}
}
