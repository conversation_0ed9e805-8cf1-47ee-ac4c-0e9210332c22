import { ConversationMemory } from "@/lib/ai/memory/conversation-memory";
import { db } from "@/lib/db";
import { websitesRepository } from "@/lib/db/repositories";
import { usersTable } from "@/lib/db/schema";
import type { Conversation } from "@/lib/db/schema/conversations";
import { applySecurityHeaders } from "@/lib/middleware/securityHeaders";
import { planLimitsService } from "@/lib/services/plan-limits";
import { ChatRequest, chatRequestSchema } from "@/lib/validation/chat";
import { mastra } from "@/mastra";
import { getAuth } from "@clerk/nextjs/server";
import { RuntimeContext } from "@mastra/core/di";
import type { Message } from "ai";
import { eq } from "drizzle-orm";
import { NextRequest, NextResponse } from "next/server";

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

// Initialize conversation memory
const conversationMemory = new ConversationMemory();

export async function POST(req: Request) {
	try {
		const json = await req.json();
		const parseResult = chatRequestSchema.safeParse(json);
		if (!parseResult.success) {
			return new Response(
				JSON.stringify({
					error: "Invalid request body",
					details: parseResult.error.flatten(),
				}),
				{
					status: 400,
					headers: { "Content-Type": "application/json" },
				},
			);
		}
		const { messages, websiteId, visitorId, conversationId } = parseResult.data;

		if (!websiteId) {
			return new Response(JSON.stringify({ error: "websiteId is required" }), {
				status: 400,
				headers: {
					"Content-Type": "application/json",
				},
			});
		}

		// Verify the website exists
		const website = await websitesRepository.getById(websiteId);
		if (!website) {
			return new Response(JSON.stringify({ error: "Website not found" }), {
				status: 404,
				headers: {
					"Content-Type": "application/json",
				},
			});
		}

		// Get the user who owns the website
		if (!website.userId) {
			return new Response(JSON.stringify({ error: "Website has no owner" }), {
				status: 404,
				headers: {
					"Content-Type": "application/json",
				},
			});
		}

		const [user] = await db
			.select()
			.from(usersTable)
			.where(eq(usersTable.id, website.userId));

		if (!user) {
			return new Response(
				JSON.stringify({ error: "Website owner not found" }),
				{
					status: 404,
					headers: {
						"Content-Type": "application/json",
					},
				},
			);
		}

		// Check if the user has reached their daily message limit
		const messageLimitCheck = await planLimitsService.checkMessagesLimit(
			user.id,
		);

		if (messageLimitCheck.hasReachedLimit) {
			return new Response(
				JSON.stringify({
					error: `The website owner has reached their daily limit of ${messageLimitCheck.limit} messages. Please try again tomorrow.`,
					currentCount: messageLimitCheck.currentCount,
					limit: messageLimitCheck.limit,
				}),
				{
					status: 403,
					headers: {
						"Content-Type": "application/json",
					},
				},
			);
		}

		// Generate a visitor ID if not provided
		const actualVisitorId = visitorId || `anonymous-${Date.now()}`;

		// Get or create a conversation
		let conversation: Conversation;

		// If conversationId is provided, try to get that specific conversation first
		if (conversationId) {
			console.log(
				`[Chat API] Trying to get conversation with ID: ${conversationId}`,
			);

			// Check if the conversation ID is a valid UUID format
			const uuidRegex =
				/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
			if (!uuidRegex.test(conversationId)) {
				console.error(
					`[Chat API] Invalid conversation ID format: ${conversationId}. Creating a new conversation.`,
				);
				// Create a new conversation instead of using the invalid ID
				conversation = await conversationMemory.getOrCreateConversation({
					websiteId,
					visitorId: actualVisitorId,
				});
			} else {
				// Try to get the existing conversation with the valid UUID
				const existingConversation =
					await conversationMemory.getConversation(conversationId);

				if (existingConversation) {
					console.log(
						`[Chat API] Found existing conversation with ID: ${existingConversation.id}`,
					);
					conversation = existingConversation;
				} else {
					console.log(
						`[Chat API] Conversation with ID ${conversationId} not found, creating new one`,
					);
					// Create a new conversation without specifying the ID
					conversation = await conversationMemory.getOrCreateConversation({
						websiteId,
						visitorId: actualVisitorId,
					});
				}
			}
		} else {
			// If no conversationId is provided, get or create based on websiteId and visitorId
			conversation = await conversationMemory.getOrCreateConversation({
				websiteId,
				visitorId: actualVisitorId,
			});
		}

		// Get the chat agent from MastrAI
		const chatAgent = mastra.getAgent("chatAgent");

		// Get the last user message
		const lastUserMessage = messages[messages.length - 1];

		// Store the user message in the database
		if (lastUserMessage.role === "user") {
			await conversationMemory.addMessage({
				conversationId: conversation.id,
				content: lastUserMessage.content,
				role: "user",
			});
		}

		// Create a runtime context to pass the websiteId and user message to the tool
		const runtimeContext = new RuntimeContext();
		runtimeContext.set("websiteId", websiteId);
		runtimeContext.set("conversationId", conversation.id);

		// Add the user's message to the runtime context for the website context tool
		if (lastUserMessage.role === "user") {
			runtimeContext.set("userMessage", lastUserMessage.content);
		}

		// Use the agent to generate a response
		const response = await chatAgent.generate(messages as Message[], {
			runtimeContext,
		});

		// Store the response in the database
		await conversationMemory.addMessage({
			conversationId: conversation.id,
			content: response.text,
			role: "assistant",
		});

		// Create a readable stream from the response text
		const encoder = new TextEncoder();
		const stream = new ReadableStream({
			async start(controller) {
				// Send the text chunk
				controller.enqueue(encoder.encode(`0:${response.text}\n`));
				// Send the done chunk
				controller.enqueue(encoder.encode("2:\n"));
				controller.close();
			},
		});

		// Return the stream with the correct headers
		const responseObj = new Response(stream, {
			headers: {
				"Content-Type": "text/plain; charset=utf-8",
				"X-Accel-Buffering": "no",
				"Cache-Control": "no-cache",
			},
		});

		// Ensure responseObj is a NextResponse before applying security headers
		const nextResponse =
			responseObj instanceof NextResponse
				? responseObj
				: new NextResponse(responseObj.body, {
						status: responseObj.status,
						headers: responseObj.headers,
					});
		return applySecurityHeaders(nextResponse);
	} catch (error) {
		console.error("Error in chat API route:", error);
		return new Response(
			JSON.stringify({ error: "Failed to process chat request" }),
			{
				status: 500,
				headers: {
					"Content-Type": "application/json",
				},
			},
		);
	}
}
