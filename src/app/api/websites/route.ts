import { db } from "@/lib/db";
import { websitesTable } from "@/lib/db/schema/websites";
import { NextResponse } from "next/server";

export async function GET() {
	try {
		const websiteList = await db
			.select({
				id: websitesTable.id,
				name: websitesTable.name,
				url: websitesTable.url,
			})
			.from(websitesTable)
			.orderBy(websitesTable.name);

		return NextResponse.json(websiteList);
	} catch (error) {
		console.error("Error fetching websites:", error);
		return NextResponse.json(
			{ error: "Failed to fetch websites" },
			{ status: 500 },
		);
	}
}
