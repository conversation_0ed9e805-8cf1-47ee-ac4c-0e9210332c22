import { db } from "@/lib/db";
import { ragMetrics } from "@/lib/db/schema/ragMetrics";
import { sql } from "drizzle-orm";
import { type NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
	try {
		const searchParams = request.nextUrl.searchParams;
		const websiteId = searchParams.get("websiteId");
		const timeRange = searchParams.get("timeRange") || "24h";

		if (!websiteId) {
			return NextResponse.json(
				{ error: "Website ID is required" },
				{ status: 400 },
			);
		}

		// Calculate the start time based on the time range
		const startTime = new Date();
		switch (timeRange) {
			case "1h":
				startTime.setHours(startTime.getHours() - 1);
				break;
			case "24h":
				startTime.setHours(startTime.getHours() - 24);
				break;
			case "7d":
				startTime.setDate(startTime.getDate() - 7);
				break;
			case "30d":
				startTime.setDate(startTime.getDate() - 30);
				break;
			default:
				startTime.setHours(startTime.getHours() - 24);
		}

		// Query metrics for the specified website and time range
		const metrics = await db
			.select()
			.from(ragMetrics)
			.where(
				sql`${ragMetrics.websiteId} = ${websiteId} AND ${ragMetrics.timestamp} >= ${startTime}`,
			)
			.orderBy(ragMetrics.timestamp);

		return NextResponse.json(metrics);
	} catch (error) {
		console.error("Error fetching RAG metrics:", error);
		return NextResponse.json(
			{ error: "Failed to fetch metrics" },
			{ status: 500 },
		);
	}
}
