import { NextResponse } from "next/server";
import type { ZodSchema } from "zod";
import { ZodError } from "zod";
import type { ApiResponse } from "../types/dashboard";

export class ApiError extends Error {
	public details?: unknown;
	constructor(
		message: string,
		public statusCode = 400,
		details?: unknown,
	) {
		super(message);
		this.name = "ApiError";
		this.details = details;
	}
}

export function createSuccessResponse<T>(
	data: T,
): NextResponse<ApiResponse<T>> {
	return NextResponse.json(
		{
			success: true,
			data,
		},
		{ status: 200 },
	);
}

export function createErrorResponse(
	error: unknown,
): NextResponse<ApiResponse<null>> {
	console.error("API Error:", error);

	if (error instanceof ApiError) {
		return NextResponse.json(
			{
				success: false,
				data: null,
				error: error.message,
			},
			{ status: error.statusCode },
		);
	}

	if (error instanceof ZodError) {
		return NextResponse.json(
			{
				success: false,
				data: null,
				error: "Validation error",
				details: error.errors,
			},
			{ status: 400 },
		);
	}

	// Handle database errors
	if (error instanceof Error) {
		console.error("Detailed error:", error.message, error.stack);

		// Check for common database errors
		if (
			error.message.includes("relation") &&
			error.message.includes("does not exist")
		) {
			return NextResponse.json(
				{
					success: false,
					data: null,
					error: "Database schema error: Table does not exist",
					details: error.message,
				},
				{ status: 500 },
			);
		}

		return NextResponse.json(
			{
				success: false,
				data: null,
				error: `Server error: ${error.message}`,
			},
			{ status: 500 },
		);
	}

	return NextResponse.json(
		{
			success: false,
			data: null,
			error: "Internal server error",
		},
		{ status: 500 },
	);
}

export async function withErrorHandling<T>(
	handler: () => Promise<T>,
): Promise<NextResponse> {
	try {
		const result = await handler();
		return createSuccessResponse(result);
	} catch (error) {
		return createErrorResponse(error);
	}
}

export function validateRequest<T>(
	schema: ZodSchema<T>,
	data: unknown,
): { success: true; data: T } {
	const result = schema.safeParse(data);
	if (!result.success) {
		throw new ApiError("Invalid request body", 400, result.error.flatten());
	}
	return { success: true, data: result.data };
}
