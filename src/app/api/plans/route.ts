import { db } from "@/lib/db";
import { plansTable } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

/**
 * Public API route to get all active plans
 * This is used by the marketing pages to display plans without requiring authentication
 */
export async function GET() {
	try {
		// Get all active plans
		const plans = await db
			.select()
			.from(plansTable)
			.where(eq(plansTable.isActive, "ACTIVE"));

		return NextResponse.json(plans);
	} catch (error) {
		console.error("Error fetching plans:", error);
		return NextResponse.json(
			{ error: "Failed to fetch plans" },
			{ status: 500 },
		);
	}
}
