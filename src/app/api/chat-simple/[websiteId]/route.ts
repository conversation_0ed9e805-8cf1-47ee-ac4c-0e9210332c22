import { websitesRepository } from "@/lib/db/repositories";
import { handleCorsPreflightRequest } from "@/lib/middleware/cors";
import { searchSimilarContent } from "@/lib/vector-search";
import { type NextRequest, NextResponse } from "next/server";

/**
 * Handle OPTIONS requests for CORS preflight
 */
export function OPTIONS(request: NextRequest) {
	return handleCorsPreflightRequest(request);
}

export async function POST(
	req: NextRequest,
	{
		params,
	}: {
		params: Promise<{ websiteId: string }>;
	},
) {
	try {
		const { messages } = await req.json();
		const { websiteId } = await params;

		if (!websiteId) {
			// Create a response with CORS headers directly
			const headers = new Headers({
				"Content-Type": "application/json",
				"Access-Control-Allow-Origin": "*",
				"Access-Control-Allow-Methods": "GET, POST, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type",
			});

			return new Response(JSON.stringify({ error: "websiteId is required" }), {
				status: 400,
				headers,
			});
		}

		// Verify the website exists
		const website = await websitesRepository.getById(websiteId);
		if (!website) {
			// Create a response with CORS headers directly
			const headers = new Headers({
				"Content-Type": "application/json",
				"Access-Control-Allow-Origin": "*",
				"Access-Control-Allow-Methods": "GET, POST, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type",
			});

			return new Response(JSON.stringify({ error: "Website not found" }), {
				status: 404,
				headers,
			});
		}

		// Get the last user message
		const lastUserMessage = messages[messages.length - 1];
		if (!lastUserMessage || lastUserMessage.role !== "user") {
			const headers = new Headers({
				"Content-Type": "application/json",
				"Access-Control-Allow-Origin": "*",
				"Access-Control-Allow-Methods": "GET, POST, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type",
			});

			return new Response(
				JSON.stringify({ error: "No valid user message found" }),
				{
					status: 400,
					headers,
				},
			);
		}

		// Try to search for similar content
		let responseContent = "";
		try {
			const searchResults = await searchSimilarContent(
				websiteId,
				lastUserMessage.content,
			);

			if (searchResults && searchResults.length > 0) {
				// Use the search results to generate a response
				const context = searchResults
					.map((result) => result.content)
					.join("\n\n");

				responseContent = `Based on the information from ${website.name}, I can tell you that:\n\n${context}\n\nIs there anything else you'd like to know?`;
			} else {
				// Fallback response if no relevant content is found
				responseContent = `I don't have specific information about that from ${website.name}. Could you try asking something else about the website?`;
			}
		} catch (error) {
			console.error("Error searching for similar content:", error);
			responseContent = `I'm having trouble accessing information from ${website.name} right now. Please try again later.`;
		}

		// Create a response with CORS headers
		const headers = new Headers({
			"Content-Type": "application/json",
			"Access-Control-Allow-Origin": "*",
			"Access-Control-Allow-Methods": "GET, POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type",
		});

		return new Response(
			JSON.stringify({
				role: "assistant",
				content: responseContent,
			}),
			{
				status: 200,
				headers,
			},
		);
	} catch (error) {
		console.error("Error in chat API route:", error);
		// Create a response with CORS headers directly
		const headers = new Headers({
			"Content-Type": "application/json",
			"Access-Control-Allow-Origin": "*",
			"Access-Control-Allow-Methods": "GET, POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type",
		});

		return new Response(
			JSON.stringify({ error: "Failed to process chat request" }),
			{
				status: 500,
				headers,
			},
		);
	}
}
