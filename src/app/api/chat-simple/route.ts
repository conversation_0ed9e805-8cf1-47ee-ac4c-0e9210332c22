import { handleCorsPreflightRequest } from "@/lib/middleware/cors";
import { type NextRequest, NextResponse } from "next/server";

/**
 * Handle OPTIONS requests for CORS preflight
 */
export function OPTIONS(request: NextRequest) {
	return handleCorsPreflightRequest(request);
}

export async function POST(req: NextRequest) {
	try {
		const { messages } = await req.json();

		// Get the last user message
		const lastUserMessage = messages[messages.length - 1];
		if (!lastUserMessage || lastUserMessage.role !== "user") {
			const headers = new Headers({
				"Content-Type": "application/json",
				"Access-Control-Allow-Origin": "*",
				"Access-Control-Allow-Methods": "GET, POST, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type",
			});

			return new Response(
				JSON.stringify({ error: "No valid user message found" }),
				{
					status: 400,
					headers,
				},
			);
		}

		// Generate a simple response
		const userQuery = lastUserMessage.content.toLowerCase();
		let responseContent = "";

		if (userQuery.includes("hello") || userQuery.includes("hi")) {
			responseContent = "Hello! How can I help you today?";
		} else if (userQuery.includes("help")) {
			responseContent = "I'm here to help! What would you like to know about?";
		} else if (userQuery.includes("thank")) {
			responseContent =
				"You're welcome! Is there anything else I can help you with?";
		} else if (userQuery.includes("bye") || userQuery.includes("goodbye")) {
			responseContent = "Goodbye! Have a great day!";
		} else {
			responseContent =
				"I'm a simple chat assistant. To get more detailed information, please select a specific website.";
		}

		// Create a response with CORS headers
		const headers = new Headers({
			"Content-Type": "application/json",
			"Access-Control-Allow-Origin": "*",
			"Access-Control-Allow-Methods": "GET, POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type",
		});

		return new Response(
			JSON.stringify({
				role: "assistant",
				content: responseContent,
			}),
			{
				status: 200,
				headers,
			},
		);
	} catch (error) {
		console.error("Error in chat API route:", error);
		// Create a response with CORS headers directly
		const headers = new Headers({
			"Content-Type": "application/json",
			"Access-Control-Allow-Origin": "*",
			"Access-Control-Allow-Methods": "GET, POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type",
		});

		return new Response(
			JSON.stringify({ error: "Failed to process chat request" }),
			{
				status: 500,
				headers,
			},
		);
	}
}
