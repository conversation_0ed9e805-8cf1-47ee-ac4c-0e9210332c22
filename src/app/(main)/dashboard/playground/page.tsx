"use client";

import { SimpleChatWidget } from "@/components/chat-ui/SimpleChatWidget";
import { DashboardLayout } from "@/components/dashboard/layout";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { useWebsites } from "@/hooks";
import { useState } from "react";

export default function TestChatPage() {
	const { data: websites, isLoading } = useWebsites();
	const [selectedWebsiteId, setSelectedWebsiteId] = useState<string | null>(
		null,
	);
	const [position, setPosition] = useState<"bottom-right" | "bottom-left">(
		"bottom-right",
	);
	const [showChat, setShowChat] = useState(false);

	const handleSelectWebsite = (websiteId: string) => {
		setSelectedWebsiteId(websiteId);
		setShowChat(false); // Reset chat when changing website
	};

	const handleShowChat = () => {
		setShowChat(true);
	};

	return (
		<div>
			<div className="p-6 space-y-6">
				<Card>
					<CardHeader>
						<CardTitle>Chat Widget Configuration</CardTitle>
						<CardDescription>
							Select a website and configure the chat widget
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							<div>
								<label
									htmlFor="website"
									className="block text-sm font-medium mb-2"
								>
									Select Website
								</label>
								<select
									className="w-full p-2 border rounded-md"
									value={selectedWebsiteId || ""}
									onChange={(e) => handleSelectWebsite(e.target.value)}
									disabled={isLoading}
								>
									<option value="">Select a website</option>
									{websites?.map((website) => (
										<option key={website.id} value={website.id}>
											{website.name}
										</option>
									))}
								</select>
							</div>

							<div>
								<label
									htmlFor="position"
									className="block text-sm font-medium mb-2"
								>
									Widget Position
								</label>
								<div className="flex space-x-4">
									<label className="flex items-center">
										<input
											type="radio"
											name="position"
											value="bottom-right"
											checked={position === "bottom-right"}
											onChange={() => setPosition("bottom-right")}
											className="mr-2"
										/>
										Bottom Right
									</label>
									<label className="flex items-center">
										<input
											type="radio"
											name="position"
											value="bottom-left"
											checked={position === "bottom-left"}
											onChange={() => setPosition("bottom-left")}
											className="mr-2"
										/>
										Bottom Left
									</label>
								</div>
							</div>

							<Button
								onClick={handleShowChat}
								disabled={!selectedWebsiteId}
								className="w-full"
							>
								Show Chat Widget
							</Button>
						</div>
					</CardContent>
				</Card>

				{showChat && selectedWebsiteId && (
					<>
						<Card>
							<CardHeader>
								<CardTitle>Chat Widget Preview</CardTitle>
								<CardDescription>
									This is how your chat widget will appear on your website
								</CardDescription>
							</CardHeader>
							<CardContent>
								<p className="text-sm text-muted-foreground mb-4">
									The chat widget will appear in the{" "}
									{position === "bottom-right" ? "bottom right" : "bottom left"}{" "}
									corner. You can interact with it just like your website
									visitors would.
								</p>
							</CardContent>
						</Card>

						<SimpleChatWidget
							title="Website Assistant"
							position={position}
							primaryColor="#4F46E5"
							secondaryColor="#FFFFFF"
							welcomeMessage="Hi there! I'm your AI assistant. How can I help you today?"
							websiteId={selectedWebsiteId}
							initiallyOpen={true}
						/>
					</>
				)}
			</div>
		</div>
	);
}
