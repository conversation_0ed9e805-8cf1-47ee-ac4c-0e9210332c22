import {
	DashboardContent,
	DashboardSidebar,
} from "@/components/dashboard/DashboardClientComponents";
import { SidebarProvider } from "@/contexts/SidebarContext";
import { db } from "@/lib/db";
import { websitesTable } from "@/lib/db/schema";
import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";

export default async function DashboardLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	// Get the userId from auth() -- if userId is null, the user is not signed in
	const { userId } = await auth();

	// Protect the route by checking if the user is signed in
	if (!userId) {
		redirect("/");
	}

	return (
		<SidebarProvider>
			<div className="flex min-h-screen bg-background">
				<DashboardSidebar />
				<DashboardContent>{children}</DashboardContent>
			</div>
		</SidebarProvider>
	);
}
