import { RagMetricsDashboard } from "@/components/rag/RagMetricsDashboard";
import type { Metadata } from "next";

export const metadata: Metadata = {
	title: "RAG Metrics Dashboard",
	description: "Monitor and analyze RAG system performance metrics",
};

export default function RagMetricsPage() {
	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-4xl font-bold tracking-tight">RAG Metrics</h1>
				<p className="text-muted-foreground">
					Monitor and analyze the performance of your RAG system
				</p>
			</div>
			<RagMetricsDashboard />
		</div>
	);
}
