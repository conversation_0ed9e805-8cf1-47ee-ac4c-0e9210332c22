import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";

export default async function SetupLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	// Get the userId from auth() -- if null, the user is not signed in
	const { userId } = await auth();

	// Protect the route by checking if the user is signed in
	if (!userId) {
		redirect("/");
	}

	return (
		<div className="min-h-screen bg-gray-50 flex flex-col">
			<main className="flex-1">{children}</main>
		</div>
	);
}
