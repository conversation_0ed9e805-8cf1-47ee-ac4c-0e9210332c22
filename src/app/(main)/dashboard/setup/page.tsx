"use client";

import { WebsiteForm } from "@/components/WebsiteForm";
import { ErrorState } from "@/components/ui";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function SetupPage() {
	const [error, setError] = useState<Error | null>(null);
	const router = useRouter();

	const handleWebsiteCreated = () => {
		router.push("/dashboard");
	};

	if (error) {
		return (
			<div className="container max-w-md mx-auto py-10">
				<ErrorState
					message={error.message || "Something went wrong"}
					retry={() => window.location.reload()}
				/>
			</div>
		);
	}

	return (
		<div className="container max-w-md mx-auto py-10">
			<h1 className="text-2xl font-bold mb-6 text-center">
				Get Started with Bubl
			</h1>

			<Card>
				<CardHeader>
					<CardTitle>Add Your Website</CardTitle>
					<CardDescription>
						Let's add your website to get started
					</CardDescription>
				</CardHeader>
				<CardContent>
					<WebsiteForm onSuccess={handleWebsiteCreated} />
				</CardContent>
			</Card>
		</div>
	);
}
