"use client";

import { ErrorState, LoadingState } from "@/components/ui";
import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
	Pa<PERSON><PERSON>,
	PaginationContent,
	PaginationEllipsis,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from "@/components/ui/pagination";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { useWebsitePages } from "@/hooks/useWebsitePages";
import { formatDistanceToNow } from "date-fns";
import { ArrowLeft, ExternalLink, Search } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface WebsitePagesClientProps {
	websiteId: string;
}

export function WebsitePagesClient({ websiteId }: WebsitePagesClientProps) {
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [searchQuery, setSearchQuery] = useState("");

	const { data, isLoading, isError, error } = useWebsitePages(
		websiteId,
		page,
		pageSize,
	);

	// Filter pages based on search query
	const filteredPages = data?.pages.filter(
		(page) =>
			page.url.toLowerCase().includes(searchQuery.toLowerCase()) ||
			page.title.toLowerCase().includes(searchQuery.toLowerCase()),
	);

	return (
		<div className="space-y-8">
			<div className="mb-4">
				<h1 className="text-3xl font-bold text-foreground mb-2">
					Crawled Pages
				</h1>
				<p className="text-muted-foreground">
					View all pages that have been crawled from your website
				</p>
			</div>

			<div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
				<Link
					href={`/dashboard/websites/${websiteId}`}
					className="inline-flex items-center text-sm text-primary hover:text-primary/80"
				>
					<ArrowLeft className="mr-2 h-4 w-4" />
					Back to website details
				</Link>
			</div>

			<Card>
				<CardHeader>
					<CardTitle>Pages</CardTitle>
					<CardDescription>
						All pages that have been crawled from your website
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="mb-4">
						<div className="relative">
							<Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
							<Input
								placeholder="Search pages by URL or title..."
								className="pl-8"
								value={searchQuery}
								onChange={(e) => setSearchQuery(e.target.value)}
							/>
						</div>
					</div>

					{isLoading ? (
						<LoadingState message="Loading pages..." />
					) : isError ? (
						<ErrorState
							message={error?.message || "Failed to load pages"}
							retry={() => window.location.reload()}
						/>
					) : filteredPages && filteredPages.length > 0 ? (
						<>
							<div className="rounded-md border">
								<Table>
									<TableHeader>
										<TableRow>
											<TableHead>Title</TableHead>
											<TableHead>URL</TableHead>
											<TableHead>Last Crawled</TableHead>
											<TableHead className="w-[100px]">Actions</TableHead>
										</TableRow>
									</TableHeader>
									<TableBody>
										{filteredPages.map((page) => (
											<TableRow key={page.id}>
												<TableCell className="font-medium">
													{page.title}
												</TableCell>
												<TableCell className="max-w-[300px] truncate">
													{page.url}
												</TableCell>
												<TableCell>
													{formatDistanceToNow(new Date(page.lastCrawledAt), {
														addSuffix: true,
													})}
												</TableCell>
												<TableCell>
													<a
														href={page.url}
														target="_blank"
														rel="noopener noreferrer"
														className="inline-flex items-center text-sm text-primary hover:text-primary/80"
													>
														<ExternalLink className="h-4 w-4" />
														<span className="sr-only">Visit page</span>
													</a>
												</TableCell>
											</TableRow>
										))}
									</TableBody>
								</Table>
							</div>

							{/* Pagination */}
							{data && data.pagination.pageCount > 1 && (
								<div className="mt-4">
									<Pagination>
										<PaginationContent>
											<PaginationItem>
												<PaginationPrevious
													onClick={() => setPage((p) => Math.max(1, p - 1))}
												/>
											</PaginationItem>

											{Array.from({ length: data.pagination.pageCount }).map(
												(_, i) => {
													// Show first page, last page, and pages around current page
													if (
														i === 0 ||
														i === data.pagination.pageCount - 1 ||
														(i >= page - 2 && i <= page + 2)
													) {
														return (
															<PaginationItem key={`page-${i + 1}`}>
																<PaginationLink
																	isActive={page === i + 1}
																	onClick={() => setPage(i + 1)}
																>
																	{i + 1}
																</PaginationLink>
															</PaginationItem>
														);
													}

													// Show ellipsis for skipped pages
													if (
														(i === 1 && page > 3) ||
														(i === data.pagination.pageCount - 2 &&
															page < data.pagination.pageCount - 2)
													) {
														return (
															<PaginationItem
																key={
																	i === 1 ? "ellipsis-start" : "ellipsis-end"
																}
															>
																<PaginationEllipsis />
															</PaginationItem>
														);
													}

													return null;
												},
											)}

											<PaginationItem>
												<PaginationNext
													onClick={() =>
														setPage((p) =>
															Math.min(data.pagination.pageCount, p + 1),
														)
													}
												/>
											</PaginationItem>
										</PaginationContent>
									</Pagination>
								</div>
							)}
						</>
					) : (
						<div className="py-12 text-center">
							<p className="text-muted-foreground mb-4">
								No pages have been crawled yet or no pages match your search.
							</p>
							<Link href={`/dashboard/websites/${websiteId}/crawl`}>
								<Button>Configure Crawler</Button>
							</Link>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
