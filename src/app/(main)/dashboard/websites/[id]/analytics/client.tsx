"use client";

import {
	<PERSON>,
	CardContent,
	CardDescription,
	<PERSON><PERSON><PERSON><PERSON>,
	CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface WebsiteStats {
	totalConversations: number;
	totalMessages: number;
	averageResponseTime: number;
	ragUsagePercentage: number;
	widgetLoads: { count: number; avgLoadDuration: number };
	widgetEngagements: { count: number; avgDuration: number };
	errors: { count: number; recent: string[] };
	widgetFID: { avg: number };
	widgetTTI: { avg: number };
	webVitals: {
		cls: { avg: number };
		lcp: { avg: number };
		fcp: { avg: number };
		ttfb: { avg: number };
		fid: { avg: number };
	};
	networkInfo: {
		avgDownlink: number;
		avg4gPercentage: number;
		avg3gPercentage: number;
		avgRtt: number;
	};
}

export function AnalyticsClient({ websiteId }: { websiteId: string }) {
	const [stats, setStats] = useState<WebsiteStats | null>(null);
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		const fetchStats = async () => {
			try {
				setIsLoading(true);
				const response = await fetch(
					`/api/dashboard/websites/${websiteId}/analytics`,
				);

				if (!response.ok) {
					throw new Error(`Failed to fetch analytics: ${response.status}`);
				}

				const data = await response.json();
				setStats(data);
			} catch (error) {
				console.error("Error fetching analytics:", error);
				toast.error("Failed to load analytics data");
			} finally {
				setIsLoading(false);
			}
		};

		fetchStats();
	}, [websiteId]);

	return (
		<Tabs defaultValue="overview" className="w-full">
			<TabsList className="mb-4">
				<TabsTrigger value="overview">Overview</TabsTrigger>
				<TabsTrigger value="performance">Performance</TabsTrigger>
				<TabsTrigger value="rag">RAG Usage</TabsTrigger>
			</TabsList>

			<TabsContent value="overview" className="space-y-4">
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium">
								Total Conversations
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{isLoading ? "Loading..." : stats?.totalConversations || 0}
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium">
								Total Messages
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{isLoading ? "Loading..." : stats?.totalMessages || 0}
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium">
								Avg. Response Time
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{isLoading
									? "Loading..."
									: `${Number(stats?.averageResponseTime || 0).toFixed(0)}ms`}
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium">RAG Usage</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{isLoading
									? "Loading..."
									: `${Number(stats?.ragUsagePercentage || 0).toFixed(1)}%`}
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium">
								Widget Loads
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{isLoading ? "Loading..." : stats?.widgetLoads.count || 0}
							</div>
							<div className="text-xs text-muted-foreground mt-1">
								Avg. Load Time:{" "}
								{isLoading
									? "-"
									: `${Number(stats?.widgetLoads.avgLoadDuration || 0).toFixed(0)}ms`}
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium">
								Widget Engagements
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{isLoading ? "Loading..." : stats?.widgetEngagements.count || 0}
							</div>
							<div className="text-xs text-muted-foreground mt-1">
								Avg. Duration:{" "}
								{isLoading
									? "-"
									: `${Number(stats?.widgetEngagements.avgDuration || 0).toFixed(0)}ms`}
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium">
								Widget Errors
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{isLoading ? "Loading..." : stats?.errors.count || 0}
							</div>
							<div className="text-xs text-muted-foreground mt-1">
								Recent:{" "}
								{isLoading
									? "-"
									: stats?.errors.recent?.length
										? stats.errors.recent.map((msg) => (
												<div
													key={
														typeof msg === "string" ? msg : JSON.stringify(msg)
													}
												>
													{msg}
												</div>
											))
										: "None"}
							</div>
						</CardContent>
					</Card>
				</div>

				<Card>
					<CardHeader>
						<CardTitle>Analytics Overview</CardTitle>
						<CardDescription>
							Summary of chat usage and performance metrics
						</CardDescription>
					</CardHeader>
					<CardContent>
						{isLoading ? (
							<div className="h-[300px] flex items-center justify-center">
								<p className="text-muted-foreground">
									Loading analytics data...
								</p>
							</div>
						) : stats ? (
							<div className="space-y-4">
								<p className="text-muted-foreground">
									Your chat widget has handled {stats.totalConversations}{" "}
									conversations with a total of {stats.totalMessages} messages.
									The average response time is{" "}
									{Number(stats.averageResponseTime || 0).toFixed(0)}ms, and RAG
									is used in {Number(stats.ragUsagePercentage || 0).toFixed(1)}%
									of responses.
								</p>

								<div className="h-[200px] bg-muted/20 rounded-md flex items-center justify-center">
									<p className="text-muted-foreground">
										Chart visualization coming soon
									</p>
								</div>
							</div>
						) : (
							<div className="h-[300px] flex items-center justify-center">
								<p className="text-muted-foreground">
									No analytics data available
								</p>
							</div>
						)}
					</CardContent>
				</Card>
			</TabsContent>

			<TabsContent value="performance" className="space-y-4">
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium">Avg. FID</CardTitle>
							<CardDescription className="text-xs">
								First Input Delay
							</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{isLoading
									? "Loading..."
									: `${Number(stats?.widgetFID.avg || 0).toFixed(0)}ms`}
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium">Avg. TTI</CardTitle>
							<CardDescription className="text-xs">
								Time to Interactive
							</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{isLoading
									? "Loading..."
									: `${Number(stats?.widgetTTI.avg || 0).toFixed(0)}ms`}
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium">Avg. LCP</CardTitle>
							<CardDescription className="text-xs">
								Largest Contentful Paint
							</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{isLoading
									? "Loading..."
									: `${Number(stats?.webVitals?.lcp.avg || 0).toFixed(0)}ms`}
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium">Avg. CLS</CardTitle>
							<CardDescription className="text-xs">
								Cumulative Layout Shift
							</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{isLoading
									? "Loading..."
									: Number(stats?.webVitals?.cls.avg || 0).toFixed(3)}
							</div>
						</CardContent>
					</Card>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<Card>
						<CardHeader>
							<CardTitle>Core Web Vitals</CardTitle>
							<CardDescription>
								Key metrics that affect user experience
							</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								<div className="grid grid-cols-2 gap-4">
									<div>
										<h4 className="text-sm font-medium mb-1">
											First Contentful Paint (FCP)
										</h4>
										<p className="text-xl font-semibold">
											{isLoading
												? "Loading..."
												: `${Number(stats?.webVitals?.fcp.avg || 0).toFixed(0)}ms`}
										</p>
										<p className="text-xs text-muted-foreground mt-1">
											Time until first content is painted
										</p>
									</div>
									<div>
										<h4 className="text-sm font-medium mb-1">
											Time to First Byte (TTFB)
										</h4>
										<p className="text-xl font-semibold">
											{isLoading
												? "Loading..."
												: `${Number(stats?.webVitals?.ttfb.avg || 0).toFixed(0)}ms`}
										</p>
										<p className="text-xs text-muted-foreground mt-1">
											Time until first byte is received
										</p>
									</div>
								</div>

								<div className="mt-4">
									<h4 className="text-sm font-medium mb-2">
										Performance Assessment
									</h4>
									<div className="p-3 rounded-md bg-muted/30">
										{isLoading ? (
											<p>Loading assessment...</p>
										) : (
											<p className="text-sm">
												{Number(stats?.webVitals?.lcp.avg || 0) < 2500 &&
												Number(stats?.webVitals?.fid.avg || 0) < 100 &&
												Number(stats?.webVitals?.cls.avg || 0) < 0.1 ? (
													<span className="text-green-600 font-medium">
														Good
													</span>
												) : Number(stats?.webVitals?.lcp.avg || 0) < 4000 &&
													Number(stats?.webVitals?.fid.avg || 0) < 300 &&
													Number(stats?.webVitals?.cls.avg || 0) < 0.25 ? (
													<span className="text-amber-600 font-medium">
														Needs Improvement
													</span>
												) : (
													<span className="text-red-600 font-medium">Poor</span>
												)}{" "}
												- Your widget's performance is{" "}
												{Number(stats?.webVitals?.lcp.avg || 0) < 2500 &&
												Number(stats?.webVitals?.fid.avg || 0) < 100 &&
												Number(stats?.webVitals?.cls.avg || 0) < 0.1
													? "excellent and provides a great user experience."
													: Number(stats?.webVitals?.lcp.avg || 0) < 4000 &&
															Number(stats?.webVitals?.fid.avg || 0) < 300 &&
															Number(stats?.webVitals?.cls.avg || 0) < 0.25
														? "acceptable but could be improved for better user experience."
														: "below recommended thresholds and may be affecting user experience."}
											</p>
										)}
									</div>
								</div>
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle>Network Performance</CardTitle>
							<CardDescription>
								Network conditions affecting widget performance
							</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								<div className="grid grid-cols-2 gap-4">
									<div>
										<h4 className="text-sm font-medium mb-1">Avg. Downlink</h4>
										<p className="text-xl font-semibold">
											{isLoading
												? "Loading..."
												: `${Number(stats?.networkInfo?.avgDownlink || 0).toFixed(1)} Mbps`}
										</p>
									</div>
									<div>
										<h4 className="text-sm font-medium mb-1">
											Avg. Round Trip Time
										</h4>
										<p className="text-xl font-semibold">
											{isLoading
												? "Loading..."
												: `${Number(stats?.networkInfo?.avgRtt || 0).toFixed(0)}ms`}
										</p>
									</div>
								</div>

								<div className="mt-4">
									<h4 className="text-sm font-medium mb-2">Connection Types</h4>
									<div className="grid grid-cols-2 gap-2">
										<div className="p-2 rounded-md bg-muted/30">
											<p className="text-xs text-muted-foreground">
												4G Connections
											</p>
											<p className="text-lg font-semibold">
												{isLoading
													? "Loading..."
													: `${Number(stats?.networkInfo?.avg4gPercentage || 0).toFixed(1)}%`}
											</p>
										</div>
										<div className="p-2 rounded-md bg-muted/30">
											<p className="text-xs text-muted-foreground">
												3G Connections
											</p>
											<p className="text-lg font-semibold">
												{isLoading
													? "Loading..."
													: `${Number(stats?.networkInfo?.avg3gPercentage || 0).toFixed(1)}%`}
											</p>
										</div>
									</div>
								</div>

								<div className="mt-4">
									<h4 className="text-sm font-medium mb-2">
										Optimization Recommendations
									</h4>
									<div className="p-3 rounded-md bg-muted/30">
										{isLoading ? (
											<p>Loading recommendations...</p>
										) : (
											<ul className="text-sm list-disc pl-4 space-y-1">
												{Number(stats?.webVitals?.lcp.avg || 0) > 2500 && (
													<li>
														Optimize widget loading time by reducing initial
														payload size
													</li>
												)}
												{Number(stats?.webVitals?.cls.avg || 0) > 0.1 && (
													<li>
														Improve layout stability by setting fixed dimensions
														for widget elements
													</li>
												)}
												{Number(stats?.webVitals?.fid.avg || 0) > 100 && (
													<li>
														Improve interactivity by optimizing JavaScript
														execution
													</li>
												)}
												{Number(stats?.networkInfo?.avg3gPercentage || 0) >
													20 && (
													<li>
														Consider implementing a lighter version for users on
														slower connections
													</li>
												)}
												{Number(stats?.networkInfo?.avgRtt || 0) > 100 && (
													<li>
														Optimize network requests to reduce latency impact
													</li>
												)}
											</ul>
										)}
									</div>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>
			</TabsContent>

			<TabsContent value="rag" className="space-y-4">
				<Card>
					<CardHeader>
						<CardTitle>RAG Usage Analytics</CardTitle>
						<CardDescription>
							Detailed analytics on RAG (Retrieval-Augmented Generation) usage
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="h-[300px] flex items-center justify-center">
							<p className="text-muted-foreground">RAG analytics coming soon</p>
						</div>
					</CardContent>
				</Card>
			</TabsContent>
		</Tabs>
	);
}
