"use client";

import { ChatConfigurationForm } from "@/components/dashboard/ChatConfigurationForm";
import { EmbedCodeGenerator } from "@/components/dashboard/EmbedCodeGenerator";
import { ReactEmbedCodeGenerator } from "@/components/dashboard/ReactEmbedCodeGenerator";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useState } from "react";

// Import the ChatConfig type from the form component
import type { ChatConfig } from "@/components/dashboard/ChatConfigurationForm";

interface ChatConfigClientProps {
	websiteId: string;
}

export function ChatConfigClient({ websiteId }: ChatConfigClientProps) {
	// Track preview updates (happens on input change)
	const [previewUpdated, setPreviewUpdated] = useState(false);

	// Track configuration saves (happens on form submit)
	const [configSaved, setConfigSaved] = useState(false);

	// Store the current configuration for preview
	const [currentConfig, setCurrentConfig] = useState<ChatConfig>({
		name: "Default Configuration",
		position: "BOTTOM_RIGHT",
		primaryColor: "#4F46E5",
		secondaryColor: "#FFFFFF",
		headerText: "Chat Assistant",
		welcomeMessage: "Hi there! How can I help you today?",
		isActive: true,
		allowedDomains: [],
	});

	return (
		<div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
			<div className="space-y-8">
				<ChatConfigurationForm
					websiteId={websiteId}
					// Update preview in real-time as inputs change
					onConfigUpdate={() => setPreviewUpdated((prev) => !prev)}
					// Update embed code when configuration is saved
					onConfigSave={() => setConfigSaved((prev) => !prev)}
					// Get the current configuration for preview
					onConfigChange={(config) => {
						setCurrentConfig(config);
						setPreviewUpdated((prev) => !prev);
					}}
				/>
				{/* Always show the embed code generator */}
				<div className="mt-8">
					<h2 className="text-2xl font-bold mb-4">Embed Code</h2>
					<p className="text-muted-foreground mb-4">
						Use this code to embed the chat widget on your website. The widget
						will use the configuration you saved above.
					</p>

					<Tabs defaultValue="html" className="w-full">
						<TabsList className="mb-4">
							<TabsTrigger value="html">HTML</TabsTrigger>
							<TabsTrigger value="react">React</TabsTrigger>
						</TabsList>

						<TabsContent value="html" className="mt-0">
							<EmbedCodeGenerator
								websiteId={websiteId}
								// Force refresh when configuration is saved
								key={`embed-code-${websiteId}-${configSaved ? "saved" : "initial"}`}
							/>
						</TabsContent>

						<TabsContent value="react" className="mt-0">
							<ReactEmbedCodeGenerator
								websiteId={websiteId}
								chatConfig={{
									primaryColor: currentConfig.primaryColor,
									secondaryColor: currentConfig.secondaryColor,
									position: currentConfig.position,
									welcomeMessage: currentConfig.welcomeMessage,
									headerText: currentConfig.headerText,
								}}
								// Force refresh when configuration is saved
								key={`react-embed-code-${websiteId}-${configSaved ? "saved" : "initial"}`}
							/>
						</TabsContent>
					</Tabs>
				</div>
			</div>
			<div />
		</div>
	);
}
