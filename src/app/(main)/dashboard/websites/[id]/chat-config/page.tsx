import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { ChatConfigClient } from "./client";

export default async function ChatConfigPage({
	params,
}: { params: Promise<{ id: string }> }) {
	const { id } = await params;

	return (
		<div className="space-y-8">
			<div className="mb-4">
				<h1 className="text-3xl font-bold text-foreground mb-2">
					Chat Configuration
				</h1>
				<p className="text-muted-foreground">
					Customize your chat widget and get the embed code
				</p>
			</div>

			<div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
				<Link
					href={`/dashboard/websites/${id}`}
					className="inline-flex items-center text-sm text-primary hover:text-primary/80"
				>
					<ArrowLeft className="mr-2 h-4 w-4" />
					Back to website details
				</Link>
			</div>

			<ChatConfigClient websiteId={id} />
		</div>
	);
}
