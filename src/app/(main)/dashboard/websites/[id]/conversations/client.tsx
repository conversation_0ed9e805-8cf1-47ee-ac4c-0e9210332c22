"use client";

import { ConversationDetail } from "@/components/dashboard/ConversationDetail";
import { ErrorState, LoadingState } from "@/components/ui";
import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
	Pagination,
	PaginationContent,
	PaginationEllipsis,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from "@/components/ui/pagination";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { useWebsiteConversations } from "@/hooks/useWebsiteConversations";
import { formatDistanceToNow } from "date-fns";
import { ArrowLeft, MessageSquare, Search } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface WebsiteConversationsClientProps {
	websiteId: string;
}

export function WebsiteConversationsClient({
	websiteId,
}: WebsiteConversationsClientProps) {
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedConversationId, setSelectedConversationId] = useState<
		string | null
	>(null);

	const { data, isLoading, isError, error } = useWebsiteConversations(
		websiteId,
		page,
		pageSize,
	);

	// Filter conversations based on search query
	const filteredConversations = data?.conversations.filter(
		(conversation) =>
			conversation.visitorId
				.toLowerCase()
				.includes(searchQuery.toLowerCase()) ||
			conversation.id.toLowerCase().includes(searchQuery.toLowerCase()),
	);

	return (
		<div className="space-y-8">
			<div className="mb-4">
				<h1 className="text-3xl font-bold text-foreground mb-2">
					Conversations
				</h1>
				<p className="text-muted-foreground">
					View all conversations from your website chat widget
				</p>
			</div>

			<div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
				<Link
					href={`/dashboard/websites/${websiteId}`}
					className="inline-flex items-center text-sm text-primary hover:text-primary/80"
				>
					<ArrowLeft className="mr-2 h-4 w-4" />
					Back to website details
				</Link>
			</div>

			<Card>
				<CardHeader>
					<CardTitle>Conversations</CardTitle>
					<CardDescription>
						All conversations from your website chat widget
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="mb-4">
						<div className="relative">
							<Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
							<Input
								placeholder="Search by visitor ID..."
								className="pl-8"
								value={searchQuery}
								onChange={(e) => setSearchQuery(e.target.value)}
							/>
						</div>
					</div>

					{isLoading ? (
						<LoadingState message="Loading conversations..." />
					) : isError ? (
						<ErrorState
							message={error?.message || "Failed to load conversations"}
							retry={() => window.location.reload()}
						/>
					) : filteredConversations && filteredConversations.length > 0 ? (
						<>
							<div className="rounded-md border">
								<Table>
									<TableHeader>
										<TableRow>
											<TableHead>Visitor ID</TableHead>
											<TableHead>Status</TableHead>
											<TableHead>Started</TableHead>
											<TableHead>Referring URL</TableHead>
											<TableHead className="w-[100px]">Actions</TableHead>
										</TableRow>
									</TableHeader>
									<TableBody>
										{filteredConversations.map((conversation) => (
											<TableRow key={conversation.id}>
												<TableCell className="font-medium">
													{conversation.visitorId.substring(0, 8)}...
												</TableCell>
												<TableCell>
													<span
														className={`px-2 py-1 rounded-full text-xs ${
															conversation.status === "active"
																? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
																: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200"
														}`}
													>
														{conversation.status}
													</span>
												</TableCell>
												<TableCell>
													{formatDistanceToNow(
														new Date(conversation.createdAt),
														{
															addSuffix: true,
														},
													)}
												</TableCell>
												<TableCell className="max-w-[200px] truncate">
													{conversation.referringUrl || "Direct"}
												</TableCell>
												<TableCell>
													<Button
														variant="ghost"
														size="icon"
														onClick={() =>
															setSelectedConversationId(conversation.id)
														}
													>
														<MessageSquare className="h-4 w-4" />
														<span className="sr-only">View conversation</span>
													</Button>
												</TableCell>
											</TableRow>
										))}
									</TableBody>
								</Table>
							</div>

							{/* Pagination */}
							{data && data.pagination.pageCount > 1 ? (
								<div className="mt-4">
									<Pagination>
										<PaginationContent>
											<PaginationItem>
												<PaginationPrevious
													onClick={() => setPage((p) => Math.max(1, p - 1))}
												/>
											</PaginationItem>

											{Array.from({ length: data.pagination.pageCount }).map(
												(_, i) => {
													// Show first page, last page, and pages around current page
													if (
														i === 0 ||
														i === data.pagination.pageCount - 1 ||
														(i >= page - 2 && i <= page + 2)
													) {
														return (
															<PaginationItem key={`page-${i + 1}`}>
																<PaginationLink
																	isActive={page === i + 1}
																	onClick={() => setPage(i + 1)}
																>
																	{i + 1}
																</PaginationLink>
															</PaginationItem>
														);
													}

													// Show ellipsis for skipped pages
													if (
														(i === 1 && page > 3) ||
														(i === data.pagination.pageCount - 2 &&
															page < data.pagination.pageCount - 2)
													) {
														return (
															<PaginationItem
																key={
																	i === 1 ? "ellipsis-start" : "ellipsis-end"
																}
															>
																<PaginationEllipsis />
															</PaginationItem>
														);
													}

													return null;
												},
											)}

											<PaginationItem>
												<PaginationNext
													onClick={() =>
														setPage((p) =>
															Math.min(data.pagination.pageCount, p + 1),
														)
													}
												/>
											</PaginationItem>
										</PaginationContent>
									</Pagination>
								</div>
							) : null}
						</>
					) : (
						<div className="py-12 text-center">
							<p className="text-muted-foreground">
								No conversations found or no conversations match your search.
							</p>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Conversation Detail Dialog */}
			<Dialog
				open={!!selectedConversationId}
				onOpenChange={(open) => !open && setSelectedConversationId(null)}
			>
				<DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
					<DialogHeader>
						<DialogTitle>Conversation Details</DialogTitle>
						<DialogDescription>
							View the full conversation thread
						</DialogDescription>
					</DialogHeader>

					{selectedConversationId && (
						<ConversationDetail conversationId={selectedConversationId} />
					)}
				</DialogContent>
			</Dialog>
		</div>
	);
}
