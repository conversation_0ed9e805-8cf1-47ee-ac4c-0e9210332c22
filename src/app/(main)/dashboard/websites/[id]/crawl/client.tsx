"use client";

import { CrawlConfigurationForm } from "@/components/dashboard/CrawlConfigurationForm";
import { ErrorState, LoadingState } from "@/components/ui";
import { Button } from "@/components/ui/button";
import { useUserPlan, useWebsite } from "@/hooks";
import { ArrowLeft, CreditCard } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface CrawlConfigurationClientProps {
	websiteId: string;
}

export function CrawlConfigurationClient({
	websiteId,
}: CrawlConfigurationClientProps) {
	const router = useRouter();
	const {
		data: website,
		isLoading: isWebsiteLoading,
		isError: isWebsiteError,
		error: websiteError,
	} = useWebsite(websiteId);
	const {
		data: userPlan,
		isLoading: isPlanLoading,
		isError: isPlanError,
		error: planError,
	} = useUserPlan();
	const [isCrawling, setIsCrawling] = useState(false);

	const isLoading = isWebsiteLoading || isPlanLoading;
	const isError = isWebsiteError || isPlanError;
	const error = websiteError || planError;

	const handleCrawlStart = () => {
		setIsCrawling(true);
	};

	const handleCrawlComplete = () => {
		setIsCrawling(false);
		// Redirect back to website detail page after a short delay
		setTimeout(() => {
			router.push(`/dashboard/websites/${websiteId}`);
		}, 2000);
	};

	if (isLoading) {
		return <LoadingState message="Loading website details..." />;
	}

	if (isError) {
		return (
			<ErrorState
				message={error?.message || "Failed to load website details"}
			/>
		);
	}

	if (!website) {
		return (
			<div className="p-6 bg-muted/20 rounded-lg border border-border/40 text-center">
				<p className="text-card-foreground">Website not found</p>
				<Link
					href="/dashboard/websites"
					className="text-primary hover:text-primary/80 mt-4 inline-flex items-center"
				>
					<ArrowLeft className="mr-2 h-4 w-4" />
					Back to websites
				</Link>
			</div>
		);
	}

	return (
		<div className="space-y-8">
			<div className="mb-4">
				<h1 className="text-3xl font-bold text-foreground mb-2">
					Crawl Configuration
				</h1>
				<p className="text-muted-foreground">
					Configure and start crawling for {website.name}
				</p>
			</div>

			<div className="mb-6">
				<Link
					href={`/dashboard/websites/${websiteId}`}
					className="inline-flex items-center text-sm text-primary hover:text-primary/80"
				>
					<ArrowLeft className="mr-2 h-4 w-4" />
					Back to website details
				</Link>
			</div>

			{/* Plan Information */}
			{userPlan && (
				<div className="mb-6 p-4 bg-muted/20 rounded-lg border border-border/40">
					<div className="flex items-center justify-between">
						<div>
							<h3 className="text-lg font-medium text-foreground">
								{userPlan.name} Plan
							</h3>
							<p className="text-sm text-muted-foreground">
								Your plan allows up to {userPlan.pagesPerWebsiteLimit} pages per
								website
							</p>
						</div>
						{userPlan.name !== "Enterprise" && (
							<Link href="/dashboard/plans">
								<Button variant="outline" size="sm" className="text-xs">
									<CreditCard className="mr-2 h-4 w-4" />
									Upgrade
								</Button>
							</Link>
						)}
					</div>
				</div>
			)}

			<CrawlConfigurationForm
				websiteId={websiteId}
				userPlan={userPlan}
				onCrawlStart={handleCrawlStart}
				onCrawlComplete={handleCrawlComplete}
			/>
		</div>
	);
}
