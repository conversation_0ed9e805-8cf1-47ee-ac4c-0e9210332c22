"use client";

import { OptimizeAllRagButton } from "@/components/dashboard/websites/optimize-rag-button";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export default function RagOptimizationPage() {
	const [isLoading, setIsLoading] = useState(false);

	const handleTriggerScheduled = async () => {
		setIsLoading(true);
		try {
			// Send a request to trigger the scheduled RAG optimization
			await fetch("/api/inngest/events", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					name: "rag/all.optimize",
					data: {
						force: false,
					},
				}),
			});

			toast.success("Scheduled RAG optimization triggered successfully");
		} catch (error) {
			console.error("Error triggering scheduled RAG optimization:", error);
			toast.error("Failed to trigger scheduled RAG optimization");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="space-y-8">
			<div className="mb-4">
				<h1 className="text-3xl font-bold text-foreground mb-2">
					RAG Optimization
				</h1>
				<p className="text-muted-foreground">
					Optimize your Retrieval-Augmented Generation (RAG) system for better
					performance and accuracy
				</p>
			</div>

			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
				<Card>
					<CardHeader>
						<CardTitle>Manual Optimization</CardTitle>
						<CardDescription>
							Manually trigger RAG optimization for all websites.
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="flex flex-col gap-4">
							<p className="text-sm text-muted-foreground">
								This will optimize the RAG system for all active websites. The
								process includes optimizing chunking strategies, rebuilding
								vector indexes, and clearing caches.
							</p>
							<OptimizeAllRagButton variant="default" />
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Scheduled Optimization</CardTitle>
						<CardDescription>
							Trigger the scheduled RAG optimization job.
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="flex flex-col gap-4">
							<p className="text-sm text-muted-foreground">
								This will trigger the same job that runs automatically every
								Sunday at midnight. It will optimize all active websites based
								on their current state.
							</p>
							<Button onClick={handleTriggerScheduled} disabled={isLoading}>
								{isLoading ? (
									<>
										<Loader2 className="mr-2 h-4 w-4 animate-spin" />
										Triggering...
									</>
								) : (
									"Trigger Scheduled Job"
								)}
							</Button>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Optimization Status</CardTitle>
						<CardDescription>
							View the status of RAG optimization jobs.
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="flex flex-col gap-4">
							<p className="text-sm text-muted-foreground">
								You can view the status of all RAG optimization jobs in the
								Inngest dashboard. This includes both manual and scheduled
								optimization jobs.
							</p>
							<Button
								variant="outline"
								onClick={() => window.open("http://localhost:8288", "_blank")}
							>
								Open Inngest Dashboard
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>

			<div className="mt-8">
				<Card>
					<CardHeader>
						<CardTitle>About RAG Optimization</CardTitle>
						<CardDescription>
							Learn more about how RAG optimization works and why it's
							important.
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="flex flex-col gap-4">
							<p className="text-sm">
								Retrieval-Augmented Generation (RAG) optimization improves the
								performance and accuracy of your chat system by:
							</p>
							<ul className="list-disc pl-5 text-sm space-y-2">
								<li>
									<strong>Optimizing chunking strategies:</strong> Using
									content-aware chunking to create more meaningful text segments
									for better retrieval.
								</li>
								<li>
									<strong>Rebuilding vector indexes:</strong> Adjusting index
									parameters based on dataset size for faster and more accurate
									searches.
								</li>
								<li>
									<strong>Implementing reranking:</strong> Using a two-stage
									retrieval process to improve the relevance of search results.
								</li>
								<li>
									<strong>Managing caches:</strong> Clearing and rebuilding
									caches to ensure fresh results while maintaining performance.
								</li>
							</ul>
							<p className="text-sm">
								Regular optimization is recommended as your dataset grows to
								maintain optimal performance and accuracy.
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
