'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export default function TestWidgetV2Page() {
  const [widgetLoaded, setWidgetLoaded] = useState(false)
  const [testResults, setTestResults] = useState<string[]>([])

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`])
  }

  const loadWidget = () => {
    if (widgetLoaded) {
      addTestResult('Widget already loaded')
      return
    }

    // Create script element
    const script = document.createElement('script')
    script.async = true
    script.src = '/widget/v2/loader.js';

    // Set up configuration
    (window as any).Bubl = {
      config: {
        websiteId: 'test-website-id',
        primaryColor: '#3b82f6',
        secondaryColor: '#ffffff',
        position: 'bottom-right',
        welcomeMessage: 'Hello! This is the new Widget V2 with Shadow DOM isolation.',
        headerText: 'Widget V2 Test',
        initiallyOpen: false,
        apiBaseUrl: window.location.origin,
      },
      onReady: () => {
        addTestResult('✅ Widget V2 loaded successfully')
        setWidgetLoaded(true)
      }
    }

    // Add error handling
    script.onerror = () => {
      addTestResult('❌ Failed to load Widget V2')
    }

    // Append to document
    document.head.appendChild(script)
    addTestResult('📦 Loading Widget V2...')
  }

  const testWidgetAPI = () => {
    const api = (window as any).Bubl?.api
    if (!api) {
      addTestResult('❌ Widget API not available')
      return
    }

    addTestResult('🧪 Testing Widget API...')

    // Test open
    setTimeout(() => {
      api.open()
      addTestResult('📖 Called api.open()')
    }, 1000)

    // Test close
    setTimeout(() => {
      api.close()
      addTestResult('📕 Called api.close()')
    }, 3000)

    // Test toggle
    setTimeout(() => {
      api.toggle()
      addTestResult('🔄 Called api.toggle()')
    }, 5000)
  }

  const destroyWidget = () => {
    const api = (window as any).Bubl?.api
    if (api?.destroy) {
      api.destroy()
      addTestResult('🗑️ Widget destroyed')
      setWidgetLoaded(false)
    } else {
      addTestResult('❌ Destroy API not available')
    }
  }

  const clearResults = () => {
    setTestResults([])
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">Widget V2 Test Page</h1>
        <p className="text-lg text-muted-foreground">
          Test the new Shadow DOM isolated chat widget
        </p>
        <Badge variant="secondary" className="text-sm">
          Complete Style Isolation • Zero Host Interference
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Widget Controls</CardTitle>
            <CardDescription>
              Test the widget loading, API methods, and destruction
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Button
                onClick={loadWidget}
                disabled={widgetLoaded}
                variant={widgetLoaded ? "secondary" : "default"}
              >
                {widgetLoaded ? "Widget Loaded" : "Load Widget V2"}
              </Button>

              <Button
                onClick={testWidgetAPI}
                disabled={!widgetLoaded}
                variant="outline"
              >
                Test API
              </Button>

              <Button
                onClick={destroyWidget}
                disabled={!widgetLoaded}
                variant="destructive"
              >
                Destroy Widget
              </Button>
            </div>

            <div className="text-sm text-muted-foreground space-y-2">
              <p><strong>Features to test:</strong></p>
              <ul className="list-disc list-inside space-y-1">
                <li>Widget loads without affecting page styles</li>
                <li>Chat bubble appears in bottom-right corner</li>
                <li>Clicking bubble opens/closes chat window</li>
                <li>Chat window has proper styling and functionality</li>
                <li>Markdown rendering in AI responses (headers, lists, code, etc.)</li>
                <li>Real-time streaming responses from Mastra</li>
                <li>Page links and forms remain fully functional</li>
                <li>No style conflicts or layout shifts</li>
                <li>API methods work correctly</li>
                <li>Widget can be destroyed cleanly</li>
              </ul>

              <p><strong>Try asking questions that might return markdown:</strong></p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li>"Can you give me a list of features?"</li>
                <li>"Show me some code examples"</li>
                <li>"Explain this with headings and bullet points"</li>
                <li>"Format your response with markdown"</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
            <CardDescription>
              Real-time feedback from widget operations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Console Output</span>
                <Button
                  onClick={clearResults}
                  variant="ghost"
                  size="sm"
                >
                  Clear
                </Button>
              </div>

              <div className="bg-muted rounded-lg p-4 h-64 overflow-y-auto">
                {testResults.length === 0 ? (
                  <p className="text-muted-foreground text-sm">
                    No test results yet. Load the widget to begin testing.
                  </p>
                ) : (
                  <div className="space-y-1">
                    {testResults.map((result, index) => (
                      <div key={index} className="text-sm font-mono">
                        {result}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sample Content */}
      <Card>
        <CardHeader>
          <CardTitle>Sample Page Content</CardTitle>
          <CardDescription>
            This content tests that the widget doesn't interfere with normal page elements
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>
            This is sample content to test that the widget doesn't interfere with normal page functionality.
            The widget should be completely isolated and not affect any of these elements.
          </p>

          <div className="flex gap-4">
            <Button variant="outline">Sample Button</Button>
            <Button variant="secondary">Another Button</Button>
          </div>

          <div className="space-y-2">
            <label htmlFor="test-input" className="text-sm font-medium">
              Test Input Field
            </label>
            <input
              id="test-input"
              type="text"
              placeholder="Type here to test input functionality"
              className="w-full px-3 py-2 border border-input rounded-md"
            />
          </div>

          <div className="space-y-2">
            <p className="text-sm font-medium">Test Links</p>
            <div className="space-x-4">
              <a href="#" className="text-blue-600 hover:underline">
                Sample Link 1
              </a>
              <a href="#" className="text-blue-600 hover:underline">
                Sample Link 2
              </a>
              <a href="#" className="text-blue-600 hover:underline">
                Sample Link 3
              </a>
            </div>
          </div>

          <p className="text-sm text-muted-foreground">
            All of these elements should remain fully functional when the widget is loaded.
            The widget should not interfere with clicking, typing, or any other interactions.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
