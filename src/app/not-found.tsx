import { But<PERSON> } from "@/components/ui/button";
import { Home } from "lucide-react";
import Link from "next/link";

export default function NotFound() {
	return (
		<div className="flex flex-col items-center justify-center min-h-screen px-4 text-center">
			<div className="space-y-6 max-w-md">
				<h1 className="text-6xl font-bold">404</h1>
				<h2 className="text-2xl font-semibold">Page Not Found</h2>
				<p className="text-muted-foreground">
					The page you are looking for doesn't exist or has been moved.
				</p>
				<Link href="/">
					<Button>
						<Home className="mr-2 h-4 w-4" />
						Back to Home
					</Button>
				</Link>
			</div>
		</div>
	);
}
