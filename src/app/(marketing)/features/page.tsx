import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	ArrowRight,
	Check,
	MessageSquare,
	Search,
	Shield,
	Zap,
} from "lucide-react";
import type { Metada<PERSON> } from "next";
import Link from "next/link";

export const metadata: Metadata = {
	title: "Features | Bubl",
	description:
		"Explore the powerful features of Bubl that help you add intelligent chat to your website.",
};

export default function FeaturesPage() {
	return (
		<div className="flex flex-col min-h-screen">
			{/* Hero Section */}
			<section className="py-20 px-4 md:px-6 lg:py-32 bg-gradient-to-b from-background to-muted/30">
				<div className="container mx-auto max-w-5xl">
					<div className="flex flex-col items-center text-center space-y-8">
						<h1 className="text-4xl md:text-6xl font-bold tracking-tight">
							Powerful Features for Your{" "}
							<span className="text-primary">Website</span>
						</h1>
						<p className="text-xl text-muted-foreground max-w-2xl">
							<PERSON><PERSON><PERSON> comes packed with everything you need to provide instant
							answers to your visitors.
						</p>
					</div>
				</div>
			</section>

			{/* Feature Sections */}
			<section className="py-20 px-4 md:px-6">
				<div className="container mx-auto max-w-5xl">
					{/* Feature 1 */}
					<div className="grid md:grid-cols-2 gap-12 items-center mb-24">
						<div className="order-2 md:order-1">
							<h2 className="text-3xl font-bold tracking-tight mb-4">
								Smart Website Crawler
							</h2>
							<p className="text-lg text-muted-foreground mb-6">
								Our intelligent crawler automatically indexes your website
								content to power the AI chatbot with accurate information.
							</p>
							<ul className="space-y-3 mb-8">
								<li className="flex items-start">
									<Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
									<span>Configurable crawl depth and frequency</span>
								</li>
								<li className="flex items-start">
									<Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
									<span>Support for JavaScript-rendered content</span>
								</li>
								<li className="flex items-start">
									<Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
									<span>Include/exclude patterns for targeted crawling</span>
								</li>
								<li className="flex items-start">
									<Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
									<span>Robots.txt compliance and rate limiting</span>
								</li>
							</ul>
						</div>
						<div className="order-1 md:order-2 bg-card border border-border/40 rounded-lg p-6 shadow-md">
							{/* TODO: Add image */}
							{/* <img
                src="/images/crawler-feature.png"
                alt="Website Crawler"
                className="w-full h-auto rounded"
                onError={(e) => {
                  e.currentTarget.src = "/chat-illustration.svg";
                  e.currentTarget.className = "w-full h-auto p-12";
                }}
              /> */}
						</div>
					</div>

					{/* Feature 2 */}
					<div className="grid md:grid-cols-2 gap-12 items-center mb-24">
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-md">
							{/* TODO: Add image */}
							{/* <img
                src="/images/chat-widget-feature.png"
                alt="Customizable Chat Widget"
                className="w-full h-auto rounded"
                onError={(e) => {
                  e.currentTarget.src = "/chat-illustration.svg"
                  e.currentTarget.className = "w-full h-auto p-12"
                }}
              /> */}
						</div>
						<div>
							<h2 className="text-3xl font-bold tracking-tight mb-4">
								Customizable Chat Widget
							</h2>
							<p className="text-lg text-muted-foreground mb-6">
								Personalize the chat widget to match your brand perfectly with
								our easy-to-use customization options.
							</p>
							<ul className="space-y-3 mb-8">
								<li className="flex items-start">
									<Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
									<span>Custom colors to match your brand</span>
								</li>
								<li className="flex items-start">
									<Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
									<span>Adjustable position on your website</span>
								</li>
								<li className="flex items-start">
									<Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
									<span>Personalized welcome messages</span>
								</li>
								<li className="flex items-start">
									<Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
									<span>Custom header text and styling</span>
								</li>
							</ul>
						</div>
					</div>

					{/* Feature 3 */}
					<div className="grid md:grid-cols-2 gap-12 items-center mb-24">
						<div className="order-2 md:order-1">
							<h2 className="text-3xl font-bold tracking-tight mb-4">
								AI-Powered Responses
							</h2>
							<p className="text-lg text-muted-foreground mb-6">
								Leverages advanced AI to provide natural, accurate answers based
								on your website content.
							</p>
							<ul className="space-y-3 mb-8">
								<li className="flex items-start">
									<Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
									<span>Natural language understanding</span>
								</li>
								<li className="flex items-start">
									<Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
									<span>Context-aware conversations</span>
								</li>
								<li className="flex items-start">
									<Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
									<span>Accurate information retrieval</span>
								</li>
								<li className="flex items-start">
									<Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
									<span>Continuous learning and improvement</span>
								</li>
							</ul>
						</div>
						<div className="order-1 md:order-2 bg-card border border-border/40 rounded-lg p-6 shadow-md">
							{/* TODO: Add image */}
							{/* <img
                src="/images/ai-responses-feature.png"
                alt="AI-Powered Responses"
                className="w-full h-auto rounded"
                onError={(e) => {
                  e.currentTarget.src = "/chat-illustration.svg"
                  e.currentTarget.className = "w-full h-auto p-12"
                }}
              /> */}
						</div>
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="py-20 px-4 md:px-6 bg-muted/30">
				<div className="container mx-auto max-w-5xl">
					<div className="bg-card border border-border/40 rounded-lg p-8 md:p-12 shadow-lg">
						<div className="text-center mb-8">
							<h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-4">
								Ready to Get Started?
							</h2>
							<p className="text-xl text-muted-foreground max-w-2xl mx-auto">
								Add Bubl to your website today and start providing instant
								answers to your visitors.
							</p>
						</div>
						<div className="flex flex-col sm:flex-row justify-center gap-4">
							<Link href="/dashboard">
								<Button size="lg" className="rounded-full">
									Start Free Trial
									<ArrowRight className="ml-2 h-4 w-4" />
								</Button>
							</Link>
							<Link href="/pricing">
								<Button size="lg" variant="outline" className="rounded-full">
									View Pricing
								</Button>
							</Link>
						</div>
					</div>
				</div>
			</section>
		</div>
	);
}
