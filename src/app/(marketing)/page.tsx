import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	ArrowRight,
	Check,
	MessageSquare,
	Search,
	Shield,
	Zap,
} from "lucide-react";
import Link from "next/link";

export default function MarketingPage() {
	return (
		<div className="flex flex-col min-h-screen">
			{/* Hero Section */}
			<section className="py-20 px-4 md:px-6 lg:py-32 bg-gradient-to-b from-background to-muted/30">
				<div className="container mx-auto max-w-5xl">
					<div className="flex flex-col items-center text-center space-y-8">
						<h1 className="text-4xl md:text-6xl font-bold tracking-tight">
							Add Intelligent Chat to Your Website
							<span className="text-primary"> in Minutes</span>
						</h1>
						<p className="text-xl text-muted-foreground max-w-2xl">
							Bubl helps your visitors find answers instantly with an AI chatbot
							that understands your website content.
						</p>
						<div className="flex flex-col sm:flex-row gap-4 pt-4">
							<Link href="/dashboard">
								<Button size="lg" className="rounded-full">
									Get Started Free
									<ArrowRight className="ml-2 h-4 w-4" />
								</Button>
							</Link>
							<Link href="#how-it-works">
								<Button size="lg" variant="outline" className="rounded-full">
									See How It Works
								</Button>
							</Link>
						</div>
					</div>

					{/* Hero Image */}
					<div className="mt-16 flex justify-center">
						<div className="relative w-full max-w-4xl">
							<div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg blur-3xl opacity-50" />
							<div className="relative bg-card border border-border/40 shadow-xl rounded-lg overflow-hidden">
								{/* TODO: Add image */}
								{/* <img
                  src="/images/hero-screenshot.png"
                  alt="Bubl in action"
                  className="w-full h-auto"
                /> */}
							</div>
						</div>
					</div>
				</div>
			</section>

			{/* Features Section */}
			<section className="py-20 px-4 md:px-6" id="features">
				<div className="container mx-auto max-w-5xl">
					<div className="text-center mb-16">
						<h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-4">
							Powerful Features for Your Website
						</h2>
						<p className="text-xl text-muted-foreground max-w-2xl mx-auto">
							Everything you need to provide instant answers to your visitors
						</p>
					</div>

					<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
						{/* Feature 1 */}
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
							<div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
								<Search className="h-6 w-6 text-primary" />
							</div>
							<h3 className="text-xl font-semibold mb-2">
								Smart Website Crawler
							</h3>
							<p className="text-muted-foreground">
								Automatically indexes your website content to power the AI
								chatbot with accurate information.
							</p>
						</div>

						{/* Feature 2 */}
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
							<div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
								<MessageSquare className="h-6 w-6 text-primary" />
							</div>
							<h3 className="text-xl font-semibold mb-2">
								Customizable Chat Widget
							</h3>
							<p className="text-muted-foreground">
								Personalize colors, position, and messaging to match your brand
								perfectly.
							</p>
						</div>

						{/* Feature 3 */}
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
							<div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
								<Zap className="h-6 w-6 text-primary" />
							</div>
							<h3 className="text-xl font-semibold mb-2">
								AI-Powered Responses
							</h3>
							<p className="text-muted-foreground">
								Leverages advanced AI to provide natural, accurate answers based
								on your website content.
							</p>
						</div>

						{/* Feature 4 */}
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
							<div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
								<Shield className="h-6 w-6 text-primary" />
							</div>
							<h3 className="text-xl font-semibold mb-2">Secure & Compliant</h3>
							<p className="text-muted-foreground">
								Built with security and privacy in mind, including GDPR
								compliance for EU customers.
							</p>
						</div>

						{/* Feature 5 */}
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
							<div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									strokeWidth="2"
									strokeLinecap="round"
									strokeLinejoin="round"
									className="h-6 w-6 text-primary"
									aria-label="Simple Integration"
								>
									<title>Simple Integration icon</title>
									<path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83" />
								</svg>
							</div>
							<h3 className="text-xl font-semibold mb-2">Simple Integration</h3>
							<p className="text-muted-foreground">
								Add to your website with a single line of code. No complex setup
								required.
							</p>
						</div>

						{/* Feature 6 */}
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
							<div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									strokeWidth="2"
									strokeLinecap="round"
									strokeLinejoin="round"
									className="h-6 w-6 text-primary"
								>
									<title>Analytics icon</title>
									<path d="M3 3v18h18" />
									<path d="M18.4 9.6a9 9 0 0 0-9.2 9.8" />
									<path d="m8 4 4 4-4 4" />
								</svg>
							</div>
							<h3 className="text-xl font-semibold mb-2">Detailed Analytics</h3>
							<p className="text-muted-foreground">
								Track conversations, popular questions, and user satisfaction to
								continuously improve.
							</p>
						</div>
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="py-20 px-4 md:px-6 bg-muted/30">
				<div className="container mx-auto max-w-5xl">
					<div className="bg-card border border-border/40 rounded-lg p-8 md:p-12 shadow-lg">
						<div className="text-center mb-8">
							<h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-4">
								Ready to Transform Your Website?
							</h2>
							<p className="text-xl text-muted-foreground max-w-2xl mx-auto">
								Get started with Bubl today and provide instant answers to your
								visitors.
							</p>
						</div>
						<div className="flex flex-col sm:flex-row justify-center gap-4">
							<Link href="/dashboard">
								<Button size="lg" className="rounded-full">
									Start Free Trial
									<ArrowRight className="ml-2 h-4 w-4" />
								</Button>
							</Link>
							<Link href="/pricing">
								<Button size="lg" variant="outline" className="rounded-full">
									View Pricing
								</Button>
							</Link>
						</div>
					</div>
				</div>
			</section>
		</div>
	);
}
