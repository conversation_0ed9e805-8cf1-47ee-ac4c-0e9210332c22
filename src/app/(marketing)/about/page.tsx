import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import type { Metada<PERSON> } from "next";
import Link from "next/link";

export const metadata: Metadata = {
	title: "About Us | Bubl",
	description:
		"Learn about the team behind <PERSON><PERSON><PERSON> and our mission to transform website interactions with intelligent AI chatbots.",
};

// Team members data
const teamMembers = [
	{
		name: "<PERSON>",
		role: "Founder & CEO",
		bio: "<PERSON> has over 10 years of experience in AI and machine learning. She founded Bubl with a vision to make website interactions more intelligent and helpful.",
		image: "/images/team/sarah.jpg",
	},
	{
		name: "<PERSON>",
		role: "CTO",
		bio: "<PERSON> leads our technical team and has extensive experience in natural language processing and conversational AI systems.",
		image: "/images/team/michael.jpg",
	},
	{
		name: "<PERSON>",
		role: "Head of Product",
		bio: "<PERSON> oversees product development and ensures Bubl meets the needs of our customers with intuitive design and powerful features.",
		image: "/images/team/alex.jpg",
	},
	{
		name: "<PERSON>",
		role: "Head of Customer Success",
		bio: "<PERSON> leads our customer success team, ensuring that every customer gets the most value from <PERSON><PERSON><PERSON>.",
		image: "/images/team/emily.jpg",
	},
];

export default function AboutPage() {
	return (
		<div className="flex flex-col min-h-screen">
			{/* Hero Section */}
			<section className="py-20 px-4 md:px-6 lg:py-32 bg-gradient-to-b from-background to-muted/30">
				<div className="container mx-auto max-w-5xl">
					<div className="flex flex-col items-center text-center space-y-8">
						<h1 className="text-4xl md:text-6xl font-bold tracking-tight">
							About <span className="text-primary">Bubl</span>
						</h1>
						<p className="text-xl text-muted-foreground max-w-2xl">
							We're on a mission to transform website interactions with
							intelligent AI chatbots that understand your content.
						</p>
					</div>
				</div>
			</section>

			{/* Our Story Section */}
			<section className="py-20 px-4 md:px-6">
				<div className="container mx-auto max-w-5xl">
					<div className="grid md:grid-cols-2 gap-12 items-center">
						<div>
							<h2 className="text-3xl font-bold tracking-tight mb-6">
								Our Story
							</h2>
							<div className="space-y-4 text-lg">
								<p>
									Bubl was founded in 2023 with a simple but powerful idea: what
									if every website could have an intelligent assistant that
									actually understands the site's content?
								</p>
								<p>
									Our team of AI specialists and web developers came together to
									create a solution that makes it easy for any website owner to
									add a truly helpful AI chatbot to their site.
								</p>
								<p>
									Unlike generic chatbots, Bubl actually reads and understands
									your website content, allowing it to provide accurate, helpful
									responses to your visitors' questions.
								</p>
								<p>
									Today, we're helping businesses of all sizes improve their
									customer experience, reduce support tickets, and increase
									conversions with our intelligent chat solution.
								</p>
							</div>
						</div>
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-md">
							{/* TODO: Add image */}
							{/* <img 
                src="/images/about-illustration.png" 
                alt="Our Story" 
                className="w-full h-auto rounded"
                onError={(e) => {
                  e.currentTarget.src = "/chat-illustration.svg";
                  e.currentTarget.className = "w-full h-auto p-12";
                }}
              /> */}
						</div>
					</div>
				</div>
			</section>

			{/* Our Values Section */}
			<section className="py-20 px-4 md:px-6 bg-muted/30">
				<div className="container mx-auto max-w-5xl">
					<h2 className="text-3xl font-bold tracking-tight mb-12 text-center">
						Our Values
					</h2>
					<div className="grid md:grid-cols-3 gap-8">
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-sm">
							<div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									strokeWidth="2"
									strokeLinecap="round"
									strokeLinejoin="round"
									className="h-6 w-6 text-primary"
								>
									<title>Quality First icon</title>
									<path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
									<path d="m9 12 2 2 4-4" />
								</svg>
							</div>
							<h3 className="text-xl font-semibold mb-2">Quality First</h3>
							<p className="text-muted-foreground">
								We believe in delivering high-quality solutions that actually
								work. Our AI is continuously improved to provide the most
								accurate and helpful responses.
							</p>
						</div>
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-sm">
							<div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									strokeWidth="2"
									strokeLinecap="round"
									strokeLinejoin="round"
									className="h-6 w-6 text-primary"
								>
									<title>Customer Success icon</title>
									<path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
									<path d="M8 14s1.5 2 4 2 4-2 4-2" />
									<line x1="9" y1="9" x2="9.01" y2="9" />
									<line x1="15" y1="9" x2="15.01" y2="9" />
								</svg>
							</div>
							<h3 className="text-xl font-semibold mb-2">Customer Success</h3>
							<p className="text-muted-foreground">
								Your success is our success. We're committed to helping our
								customers get the most value from Bubl through excellent support
								and continuous improvement.
							</p>
						</div>
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-sm">
							<div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									strokeWidth="2"
									strokeLinecap="round"
									strokeLinejoin="round"
									className="h-6 w-6 text-primary"
								>
									<title>Innovation icon</title>
									<path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
									<path d="M12 8v4l3 3" />
								</svg>
							</div>
							<h3 className="text-xl font-semibold mb-2">Innovation</h3>
							<p className="text-muted-foreground">
								We're constantly pushing the boundaries of what's possible with
								AI and web technology to deliver innovative solutions that solve
								real problems.
							</p>
						</div>
					</div>
				</div>
			</section>

			{/* Team Section */}
			<section className="py-20 px-4 md:px-6">
				<div className="container mx-auto max-w-5xl">
					<h2 className="text-3xl font-bold tracking-tight mb-12 text-center">
						Meet Our Team
					</h2>
					<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
						{teamMembers.map((member, index) => (
							<div
								key={member.name}
								className="bg-card border border-border/40 rounded-lg p-6 shadow-sm text-center"
							>
								<div className="w-24 h-24 rounded-full mx-auto mb-4 overflow-hidden bg-muted">
									<img
										src={member.image}
										alt={member.name}
										className="w-full h-full object-cover"
										// onError={(e) => {
										//   e.currentTarget.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(member.name)}&background=random`
										// }}
									/>
								</div>
								<h3 className="text-lg font-semibold mb-1">{member.name}</h3>
								<p className="text-sm text-primary mb-3">{member.role}</p>
								<p className="text-sm text-muted-foreground">{member.bio}</p>
							</div>
						))}
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="py-20 px-4 md:px-6 bg-muted/30">
				<div className="container mx-auto max-w-5xl">
					<div className="bg-card border border-border/40 rounded-lg p-8 md:p-12 shadow-lg">
						<div className="text-center mb-8">
							<h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-4">
								Join Us on Our Mission
							</h2>
							<p className="text-xl text-muted-foreground max-w-2xl mx-auto">
								Add Bubl to your website today and start providing instant
								answers to your visitors.
							</p>
						</div>
						<div className="flex flex-col sm:flex-row justify-center gap-4">
							<Link href="/dashboard">
								<Button size="lg" className="rounded-full">
									Get Started Free
									<ArrowRight className="ml-2 h-4 w-4" />
								</Button>
							</Link>
							<Link href="/contact">
								<Button size="lg" variant="outline" className="rounded-full">
									Contact Us
								</Button>
							</Link>
						</div>
					</div>
				</div>
			</section>
		</div>
	);
}
