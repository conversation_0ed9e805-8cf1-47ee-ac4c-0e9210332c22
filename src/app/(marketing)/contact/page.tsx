import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Mail, MapPin, Phone } from "lucide-react";
import type { Metadata } from "next";

export const metadata: Metadata = {
	title: "Contact Us | Bubl",
	description:
		"Get in touch with the Bubl team for questions, support, or partnership opportunities.",
};

export default function ContactPage() {
	return (
		<div className="flex flex-col min-h-screen">
			{/* Hero Section */}
			<section className="py-20 px-4 md:px-6 lg:py-24 bg-gradient-to-b from-background to-muted/30">
				<div className="container mx-auto max-w-5xl">
					<div className="flex flex-col items-center text-center space-y-6">
						<h1 className="text-4xl md:text-5xl font-bold tracking-tight">
							Get in Touch
						</h1>
						<p className="text-xl text-muted-foreground max-w-2xl">
							Have questions or need help? We're here for you.
						</p>
					</div>
				</div>
			</section>

			{/* Contact Form Section */}
			<section className="py-16 px-4 md:px-6">
				<div className="container mx-auto max-w-5xl">
					<div className="grid md:grid-cols-2 gap-12">
						{/* Contact Information */}
						<div>
							<h2 className="text-2xl font-bold mb-6">Contact Information</h2>
							<p className="text-muted-foreground mb-8">
								Fill out the form and our team will get back to you within 24
								hours.
							</p>

							<div className="space-y-6">
								<div className="flex items-start">
									<div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-4">
										<Phone className="h-5 w-5 text-primary" />
									</div>
									<div>
										<h3 className="font-medium">Phone</h3>
										<p className="text-muted-foreground">+****************</p>
									</div>
								</div>

								<div className="flex items-start">
									<div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-4">
										<Mail className="h-5 w-5 text-primary" />
									</div>
									<div>
										<h3 className="font-medium">Email</h3>
										<p className="text-muted-foreground"><EMAIL></p>
									</div>
								</div>

								<div className="flex items-start">
									<div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-4">
										<MapPin className="h-5 w-5 text-primary" />
									</div>
									<div>
										<h3 className="font-medium">Office</h3>
										<p className="text-muted-foreground">
											123 AI Boulevard
											<br />
											San Francisco, CA 94103
											<br />
											United States
										</p>
									</div>
								</div>
							</div>

							<div className="mt-12">
								<h3 className="text-xl font-bold mb-4">Connect With Us</h3>
								<div className="flex space-x-4">
									<a
										href="https://twitter.com/bublchat"
										target="_blank"
										rel="noopener noreferrer"
										className="h-10 w-10 rounded-full bg-muted flex items-center justify-center hover:bg-muted/80 transition-colors"
									>
										<svg
											xmlns="http://www.w3.org/2000/svg"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											strokeWidth="2"
											strokeLinecap="round"
											strokeLinejoin="round"
											className="h-5 w-5"
										>
											<title>Twitter icon</title>
											<path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
										</svg>
									</a>
									<a
										href="https://linkedin.com/company/bublchat"
										target="_blank"
										rel="noopener noreferrer"
										className="h-10 w-10 rounded-full bg-muted flex items-center justify-center hover:bg-muted/80 transition-colors"
									>
										<svg
											xmlns="http://www.w3.org/2000/svg"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											strokeWidth="2"
											strokeLinecap="round"
											strokeLinejoin="round"
											className="h-5 w-5"
										>
											<title>LinkedIn icon</title>
											<path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
											<rect width="4" height="12" x="2" y="9" />
											<circle cx="4" cy="4" r="2" />
										</svg>
									</a>
									<a
										href="https://facebook.com/bublchat"
										target="_blank"
										rel="noopener noreferrer"
										className="h-10 w-10 rounded-full bg-muted flex items-center justify-center hover:bg-muted/80 transition-colors"
									>
										<svg
											xmlns="http://www.w3.org/2000/svg"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											strokeWidth="2"
											strokeLinecap="round"
											strokeLinejoin="round"
											className="h-5 w-5"
										>
											<title>Facebook icon</title>
											<path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
										</svg>
									</a>
								</div>
							</div>
						</div>

						{/* Contact Form */}
						<div className="bg-card border border-border/40 rounded-lg p-8 shadow-md">
							<h2 className="text-2xl font-bold mb-6">Send Us a Message</h2>
							<form className="space-y-6">
								<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
									<div className="space-y-2">
										<label htmlFor="first-name" className="text-sm font-medium">
											First Name
										</label>
										<Input id="first-name" placeholder="John" required />
									</div>
									<div className="space-y-2">
										<label htmlFor="last-name" className="text-sm font-medium">
											Last Name
										</label>
										<Input id="last-name" placeholder="Doe" required />
									</div>
								</div>

								<div className="space-y-2">
									<label htmlFor="email" className="text-sm font-medium">
										Email
									</label>
									<Input
										id="email"
										type="email"
										placeholder="<EMAIL>"
										required
									/>
								</div>

								<div className="space-y-2">
									<label htmlFor="subject" className="text-sm font-medium">
										Subject
									</label>
									<Input
										id="subject"
										placeholder="How can we help you?"
										required
									/>
								</div>

								<div className="space-y-2">
									<label htmlFor="message" className="text-sm font-medium">
										Message
									</label>
									<Textarea
										id="message"
										placeholder="Your message here..."
										rows={5}
										required
									/>
								</div>

								<Button type="submit" className="w-full">
									Send Message
								</Button>
							</form>
						</div>
					</div>
				</div>
			</section>

			{/* FAQ Section */}
			<section className="py-16 px-4 md:px-6 bg-muted/30">
				<div className="container mx-auto max-w-5xl">
					<h2 className="text-3xl font-bold text-center mb-12">
						Frequently Asked Questions
					</h2>
					<div className="grid md:grid-cols-2 gap-8">
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-sm">
							<h3 className="text-xl font-semibold mb-3">
								How quickly can I set up Bubl?
							</h3>
							<p className="text-muted-foreground">
								You can set up Bubl in just a few minutes. Simply sign up, add
								your website, and our system will start crawling your content.
								Once that's done, you can customize your chat widget and add it
								to your site with a single line of code.
							</p>
						</div>
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-sm">
							<h3 className="text-xl font-semibold mb-3">
								Do I need technical knowledge to use Bubl?
							</h3>
							<p className="text-muted-foreground">
								No technical knowledge is required. Our platform is designed to
								be user-friendly with a simple dashboard for managing your
								chatbot. Adding the chat widget to your website is as easy as
								copying and pasting a code snippet.
							</p>
						</div>
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-sm">
							<h3 className="text-xl font-semibold mb-3">
								Can I customize the appearance of the chat widget?
							</h3>
							<p className="text-muted-foreground">
								Yes, you can fully customize the appearance of your chat widget
								to match your brand. Change colors, position, welcome messages,
								and more through our easy-to-use dashboard.
							</p>
						</div>
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-sm">
							<h3 className="text-xl font-semibold mb-3">
								How does Bubl handle website updates?
							</h3>
							<p className="text-muted-foreground">
								Bubl can be configured to automatically recrawl your website on
								a schedule (daily, weekly, or monthly) to keep the chatbot's
								knowledge up to date with your latest content.
							</p>
						</div>
					</div>
				</div>
			</section>
		</div>
	);
}
