import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>R<PERSON> } from "lucide-react";
import type { Metadata } from "next";
import Link from "next/link";

export const metadata: Metadata = {
	title: "Blog | Bubl",
	description:
		"Read the latest articles about AI chatbots, customer support, and website optimization.",
};

// Sample blog posts data
const blogPosts = [
	{
		id: "1",
		title: "How AI Chatbots Are Transforming Customer Support",
		excerpt:
			"Discover how AI-powered chatbots are revolutionizing customer support by providing instant answers and reducing support ticket volume.",
		date: "May 15, 2023",
		author: "<PERSON>",
		slug: "ai-chatbots-transforming-customer-support",
		category: "Customer Support",
	},
	{
		id: "2",
		title: "5 Ways to Optimize Your Website's Conversion Rate with AI Chat",
		excerpt:
			"Learn how implementing an AI chatbot on your website can significantly improve conversion rates and customer satisfaction.",
		date: "June 2, 2023",
		author: "<PERSON>",
		slug: "optimize-website-conversion-rate-ai-chat",
		category: "Conversion Optimization",
	},
	{
		id: "3",
		title: "The Future of Website Interactions: AI Assistants",
		excerpt:
			"Explore how AI assistants are changing the way visitors interact with websites and what this means for the future of web design.",
		date: "June 28, 2023",
		author: "<PERSON>",
		slug: "future-website-interactions-ai-assistants",
		category: "AI Technology",
	},
	{
		id: "4",
		title: "How to Train Your AI Chatbot for Better Customer Engagement",
		excerpt:
			"Practical tips for training your AI chatbot to better understand your website content and provide more accurate responses.",
		date: "July 12, 2023",
		author: "Emily Watson",
		slug: "train-ai-chatbot-better-customer-engagement",
		category: "AI Training",
	},
	{
		id: "5",
		title: "Measuring the ROI of AI Chatbots: Key Metrics to Track",
		excerpt:
			"Learn which metrics you should be tracking to measure the return on investment of implementing an AI chatbot on your website.",
		date: "August 5, 2023",
		author: "David Kim",
		slug: "measuring-roi-ai-chatbots-key-metrics",
		category: "Analytics",
	},
	{
		id: "6",
		title: "GDPR Compliance for AI Chatbots: What You Need to Know",
		excerpt:
			"A comprehensive guide to ensuring your AI chatbot implementation complies with GDPR and other privacy regulations.",
		date: "September 18, 2023",
		author: "Laura Martinez",
		slug: "gdpr-compliance-ai-chatbots",
		category: "Compliance",
	},
];

export default function BlogPage() {
	return (
		<div className="flex flex-col min-h-screen">
			{/* Hero Section */}
			<section className="py-20 px-4 md:px-6 lg:py-24 bg-gradient-to-b from-background to-muted/30">
				<div className="container mx-auto max-w-5xl">
					<div className="flex flex-col items-center text-center space-y-6">
						<h1 className="text-4xl md:text-5xl font-bold tracking-tight">
							Bubl Blog
						</h1>
						<p className="text-xl text-muted-foreground max-w-2xl">
							Insights, tips, and news about AI chatbots, customer support, and
							website optimization
						</p>
					</div>
				</div>
			</section>

			{/* Blog Posts */}
			<section className="py-16 px-4 md:px-6">
				<div className="container mx-auto max-w-5xl">
					<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
						{blogPosts.map((post) => (
							<article
								key={post.id}
								className="flex flex-col h-full bg-card border border-border/40 rounded-lg shadow-sm hover:shadow-md transition-shadow overflow-hidden"
							>
								<div className="p-6 flex flex-col flex-grow">
									<div className="flex items-center gap-2 mb-3">
										<span className="text-xs font-medium px-2.5 py-0.5 rounded-full bg-primary/10 text-primary">
											{post.category}
										</span>
										<span className="text-xs text-muted-foreground">
											{post.date}
										</span>
									</div>
									<h2 className="text-xl font-semibold mb-3 line-clamp-2">
										<Link
											href={`/blog/${post.slug}`}
											className="hover:text-primary transition-colors"
										>
											{post.title}
										</Link>
									</h2>
									<p className="text-muted-foreground mb-4 line-clamp-3 flex-grow">
										{post.excerpt}
									</p>
									<div className="mt-auto">
										<div className="flex items-center justify-between">
											<span className="text-sm font-medium">
												By {post.author}
											</span>
											<Link href={`/blog/${post.slug}`}>
												<Button
													variant="ghost"
													size="sm"
													className="text-primary hover:text-primary/80"
												>
													Read More
													<ArrowRight className="ml-1 h-3 w-3" />
												</Button>
											</Link>
										</div>
									</div>
								</div>
							</article>
						))}
					</div>
				</div>
			</section>

			{/* Newsletter Section */}
			<section className="py-16 px-4 md:px-6 bg-muted/30">
				<div className="container mx-auto max-w-5xl">
					<div className="bg-card border border-border/40 rounded-lg p-8 md:p-12 shadow-lg">
						<div className="text-center mb-8">
							<h2 className="text-2xl md:text-3xl font-bold tracking-tight mb-4">
								Subscribe to Our Newsletter
							</h2>
							<p className="text-lg text-muted-foreground max-w-2xl mx-auto">
								Get the latest articles, news, and tips about AI chatbots and
								website optimization delivered to your inbox.
							</p>
						</div>
						<form className="max-w-md mx-auto">
							<div className="flex flex-col sm:flex-row gap-3">
								<input
									type="email"
									placeholder="Enter your email"
									className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
								/>
								<Button type="submit" className="whitespace-nowrap">
									Subscribe
								</Button>
							</div>
						</form>
					</div>
				</div>
			</section>
		</div>
	);
}
