@import "tailwindcss/preflight";
@tailwind utilities;

/* Widget-specific styles */
:root {
	--chat-primary-color: #4f46e5;
	--chat-secondary-color: #ffffff;
	--background: transparent;
	--border: none;
}

/* Chat Widget Container */
.chat-widget {
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px
		rgba(0, 0, 0, 0.04);
	border: 1px solid hsl(var(--border));
	/* border-radius: 10px; */
	padding-left: 10px;
}

.chat-widget:focus-within {
	box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px
		rgba(0, 0, 0, 0.04);
}

/* Chat Messages Container */
.chat-messages-container {
	scroll-behavior: smooth;
	padding: 1rem;
	overflow-y: auto;
	overflow-x: hidden;
}

/* Message Animations */
@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.chat-message {
	animation: fadeIn 0.3s ease-out forwards;
	position: relative;
	margin-bottom: 1rem;
}

/* Fullscreen mode */
.chat-fullscreen {
	position: fixed !important;
	top: 0 !important;
	left: 0 !important;
	right: 0 !important;
	bottom: 0 !important;
	width: 100% !important;
	height: 100% !important;
	max-height: 100% !important;
	max-width: 100% !important;
	border-radius: 0 !important;
	z-index: 2147483647 !important; /* Maximum z-index value */
	margin: 0 !important;
	padding: 0 !important;
	transform: none !important;
}

/* Hide scrollbar for Chrome, Safari and Opera */
::-webkit-scrollbar {
	display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
* {
	-ms-overflow-style: none; /* IE and Edge */
	scrollbar-width: none; /* Firefox */
}

.ai-thought {
	padding: 10px !important;
}
