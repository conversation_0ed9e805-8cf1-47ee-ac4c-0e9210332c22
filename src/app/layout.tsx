import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import "../styles/chat.css";

const geistSans = Geist({
	variable: "--font-geist-sans",
	subsets: ["latin"],
});

const geistMono = Geist_Mono({
	variable: "--font-geist-mono",
	subsets: ["latin"],
});

export const metadata: Metadata = {
	title: {
		template: "%s | Bubl",
		default: "Bubl - Add Intelligent Chat to Your Website",
	},
	description:
		"<PERSON><PERSON>l helps you create intelligent chatbots for your website that understand and can answer questions about your content.",
	keywords: [
		"chatbot",
		"ai chat",
		"website chat",
		"customer support",
		"ai assistant",
		"website assistant",
	],
	authors: [{ name: "Bubl Team" }],
	creator: "Bubl",
	publisher: "Bubl",
	formatDetection: {
		email: false,
		address: false,
		telephone: false,
	},
	openGraph: {
		type: "website",
		locale: "en_US",
		url: "https://bublai.com",
		title: "Bubl - Add Intelligent Chat to Your Website",
		description:
			"<PERSON><PERSON><PERSON> helps you create intelligent chatbots for your website that understand and can answer questions about your content.",
		siteName: "Bubl",
	},
	twitter: {
		card: "summary_large_image",
		title: "Bubl - Add Intelligent Chat to Your Website",
		description:
			"Bubl helps you create intelligent chatbots for your website that understand and can answer questions about your content.",
		creator: "@bublchat",
	},
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en" suppressHydrationWarning>
			<body
				className={`${geistSans.variable} ${geistMono.variable} antialiased`}
			>
				{children}
			</body>
		</html>
	);
}
