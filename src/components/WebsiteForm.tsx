import { ErrorState } from "@/components/ui";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useCreateWebsite, useUpdateWebsite } from "@/hooks";
import type { PlanUsage } from "@/hooks/usePlanUsage";
import type { Plan } from "@/hooks/useUserPlan";
import type { CreateWebsiteInput, UpdateWebsiteInput } from "@/lib/api/types";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface WebsiteFormProps {
	websiteId?: string;
	defaultValues?: {
		name: string;
		url: string;
	};
	userPlan?: Plan;
	planUsage?: PlanUsage;
	onSuccess?: () => void;
}

type FormData = {
	name: string;
	url: string;
};

export function WebsiteForm({
	websiteId,
	defaultValues,
	userPlan,
	planUsage,
	onSuccess,
}: WebsiteFormProps) {
	const [formData, setFormData] = useState<FormData>({
		name: defaultValues?.name || "",
		url: defaultValues?.url || "",
	});

	const {
		mutate: createWebsite,
		isPending: isCreating,
		error: createError,
	} = useCreateWebsite();

	const {
		mutate: updateWebsite,
		isPending: isUpdating,
		error: updateError,
	} = useUpdateWebsite(websiteId || "");

	const isLoading = isCreating || isUpdating;
	const error = createError || updateError;

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		// Validate URL format
		try {
			new URL(formData.url);
		} catch (err) {
			toast.error("Invalid URL", {
				description: "Please enter a valid URL including http:// or https://",
			});
			return;
		}

		if (websiteId) {
			updateWebsite(formData as UpdateWebsiteInput, {
				onSuccess: () => {
					toast.success("Website updated", {
						description: `"${formData.name}" has been updated successfully.`,
					});
					setFormData({ name: "", url: "" });
					onSuccess?.();
				},
				onError: (err) => {
					toast.error("Failed to update website", {
						description:
							err.message || "An error occurred while updating the website.",
					});
				},
			});
		} else {
			createWebsite(formData as CreateWebsiteInput, {
				onSuccess: () => {
					toast.success("Website added", {
						description: `"${formData.name}" has been added to your account.`,
					});
					setFormData({ name: "", url: "" });
					onSuccess?.();
				},
				onError: (err) => {
					toast.error("Failed to add website", {
						description:
							err.message || "An error occurred while adding the website.",
					});
				},
			});
		}
	};

	// Check if the user has reached their website limit
	const hasReachedLimit =
		planUsage &&
		userPlan &&
		planUsage.websites.count >= planUsage.websites.limit;

	// Calculate remaining websites
	const remainingWebsites =
		planUsage && userPlan
			? planUsage.websites.limit - planUsage.websites.count
			: 0;

	return (
		<form onSubmit={handleSubmit} className="space-y-5">
			{error && (
				<ErrorState
					message={error.message || "Something went wrong"}
					className="mb-4"
				/>
			)}

			{userPlan && planUsage && !websiteId && (
				<div className="p-3 bg-muted/30 rounded-lg mb-4">
					<p className="text-sm text-muted-foreground">
						<span className="font-medium">{userPlan.name} Plan:</span> You can
						add up to {planUsage.websites.limit} websites.
						{remainingWebsites > 0 && (
							<>
								{" "}
								You have {remainingWebsites} website
								{remainingWebsites !== 1 ? "s" : ""} remaining.
							</>
						)}
					</p>
				</div>
			)}

			<div className="space-y-2">
				<label
					htmlFor="name"
					className="text-sm font-medium text-card-foreground leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
				>
					Website Name
				</label>
				<Input
					id="name"
					value={formData.name}
					onChange={(e) =>
						setFormData((prev) => ({ ...prev, name: e.target.value }))
					}
					placeholder="My Website"
					required
					className="border-border/50 focus-visible:ring-primary/20"
				/>
			</div>

			<div className="space-y-2">
				<label
					htmlFor="url"
					className="text-sm font-medium text-card-foreground leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
				>
					Website URL
				</label>
				<Input
					id="url"
					type="url"
					value={formData.url}
					onChange={(e) =>
						setFormData((prev) => ({ ...prev, url: e.target.value }))
					}
					placeholder="https://example.com"
					required
					className="border-border/50 focus-visible:ring-primary/20"
				/>
				<p className="text-xs text-muted-foreground mt-1.5">
					Enter the full URL including http:// or https://
				</p>
			</div>

			<Button
				type="submit"
				disabled={isLoading}
				className="w-full rounded-full mt-2"
			>
				{isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
				{websiteId ? "Update Website" : "Add Website"}
			</Button>
		</form>
	);
}
