"use client";

import { Badge } from "@/components/ui/badge";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { formatDistanceToNow } from "date-fns";
import { useEffect, useState } from "react";
import {
	Bar,
	BarChart,
	CartesianGrid,
	Line,
	LineChart,
	ResponsiveContainer,
	Tooltip,
	XAxis,
	YAxis,
} from "recharts";

interface RagMetrics {
	id: string;
	websiteId: string;
	timestamp: string;
	searchLatency: number;
	searchResultsCount: number;
	averageSimilarity: number;
	cacheHitRate: number;
	totalChunks: number;
	averageChunkSize: number;
	chunkingLatency: number;
	embeddingLatency: number;
	embeddingDimensions: number;
	errorCount: number;
	errorTypes: Record<string, number>;
}

interface Website {
	id: string;
	name: string;
}

type TimeRange = "1h" | "24h" | "7d" | "30d";

export function RagMetricsDashboard() {
	const [websites, setWebsites] = useState<Website[]>([]);
	const [selectedWebsite, setSelectedWebsite] = useState<string>("");
	const [metrics, setMetrics] = useState<RagMetrics[]>([]);
	const [timeRange, setTimeRange] = useState<TimeRange>("24h");
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		// Fetch websites
		const fetchWebsites = async () => {
			try {
				const response = await fetch("/api/websites");
				const data = await response.json();
				setWebsites(data);
				if (data.length > 0) {
					setSelectedWebsite(data[0].id);
				}
			} catch (error) {
				console.error("Error fetching websites:", error);
			}
		};

		fetchWebsites();
	}, []);

	useEffect(() => {
		// Fetch metrics for selected website
		const fetchMetrics = async () => {
			if (!selectedWebsite) return;

			setLoading(true);
			try {
				const response = await fetch(
					`/api/rag-metrics?websiteId=${selectedWebsite}&timeRange=${timeRange}`,
				);
				const data = await response.json();
				setMetrics(data);
			} catch (error) {
				console.error("Error fetching metrics:", error);
			} finally {
				setLoading(false);
			}
		};

		fetchMetrics();
	}, [selectedWebsite, timeRange]);

	const formatLatency = (ms: number) => `${ms.toFixed(2)}ms`;
	const formatPercentage = (value: number) => `${(value * 100).toFixed(1)}%`;

	const renderMetricsCard = (
		title: string,
		value: number | string,
		description?: string,
		trend?: { value: number; isPositive: boolean },
	) => (
		<Card>
			<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle className="text-sm font-medium">{title}</CardTitle>
				{trend && (
					<Badge variant={trend.isPositive ? "default" : "destructive"}>
						{trend.isPositive ? "+" : "-"}
						{Math.abs(trend.value).toFixed(1)}%
					</Badge>
				)}
			</CardHeader>
			<CardContent>
				<div className="text-2xl font-bold">{value}</div>
				{description && (
					<p className="text-xs text-muted-foreground">{description}</p>
				)}
			</CardContent>
		</Card>
	);

	const renderChart = (
		data: RagMetrics[],
		dataKey: keyof RagMetrics,
		name: string,
		color: string,
	) => (
		<ResponsiveContainer width="100%" height={300}>
			<LineChart data={data}>
				<CartesianGrid strokeDasharray="3 3" />
				<XAxis
					dataKey="timestamp"
					tickFormatter={(value) => formatDistanceToNow(new Date(value))}
				/>
				<YAxis />
				<Tooltip
					labelFormatter={(value) => formatDistanceToNow(new Date(value))}
					formatter={(value: number) => [value, name]}
				/>
				<Line
					type="monotone"
					dataKey={dataKey}
					stroke={color}
					strokeWidth={2}
					dot={false}
				/>
			</LineChart>
		</ResponsiveContainer>
	);

	if (loading) {
		return (
			<div className="space-y-4">
				<Skeleton className="h-8 w-[200px]" />
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
					{Array.from({ length: 4 }).map((_, i) => (
						<Skeleton key={`metrics-skeleton-${i + 1}`} className="h-[120px]" />
					))}
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-4">
			<div className="flex items-center justify-between">
				<h2 className="text-3xl font-bold tracking-tight">RAG Metrics</h2>
				<div className="flex items-center space-x-2">
					<Select value={selectedWebsite} onValueChange={setSelectedWebsite}>
						<SelectTrigger className="w-[200px]">
							<SelectValue placeholder="Select website" />
						</SelectTrigger>
						<SelectContent>
							{websites.map((website) => (
								<SelectItem key={website.id} value={website.id}>
									{website.name}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
					<Select
						value={timeRange}
						onValueChange={(value: TimeRange) => setTimeRange(value)}
					>
						<SelectTrigger className="w-[100px]">
							<SelectValue placeholder="Time range" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="1h">1h</SelectItem>
							<SelectItem value="24h">24h</SelectItem>
							<SelectItem value="7d">7d</SelectItem>
							<SelectItem value="30d">30d</SelectItem>
						</SelectContent>
					</Select>
				</div>
			</div>

			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				{renderMetricsCard(
					"Search Latency",
					formatLatency(metrics[metrics.length - 1]?.searchLatency || 0),
					"Average search response time",
				)}
				{renderMetricsCard(
					"Cache Hit Rate",
					formatPercentage(metrics[metrics.length - 1]?.cacheHitRate || 0),
					"Percentage of cache hits",
				)}
				{renderMetricsCard(
					"Average Similarity",
					formatPercentage(metrics[metrics.length - 1]?.averageSimilarity || 0),
					"Average result similarity score",
				)}
				{renderMetricsCard(
					"Error Rate",
					`${metrics[metrics.length - 1]?.errorCount || 0} errors`,
					"Total errors in time period",
				)}
			</div>

			<Tabs defaultValue="performance" className="space-y-4">
				<TabsList>
					<TabsTrigger value="performance">Performance</TabsTrigger>
					<TabsTrigger value="quality">Quality</TabsTrigger>
					<TabsTrigger value="errors">Errors</TabsTrigger>
				</TabsList>

				<TabsContent value="performance" className="space-y-4">
					<Card>
						<CardHeader>
							<CardTitle>Search Latency</CardTitle>
							<CardDescription>
								Response time for search operations
							</CardDescription>
						</CardHeader>
						<CardContent>
							{renderChart(metrics, "searchLatency", "Latency (ms)", "#2563eb")}
						</CardContent>
					</Card>

					<div className="grid gap-4 md:grid-cols-2">
						<Card>
							<CardHeader>
								<CardTitle>Embedding Latency</CardTitle>
								<CardDescription>Time to generate embeddings</CardDescription>
							</CardHeader>
							<CardContent>
								{renderChart(
									metrics,
									"embeddingLatency",
									"Latency (ms)",
									"#16a34a",
								)}
							</CardContent>
						</Card>

						<Card>
							<CardHeader>
								<CardTitle>Chunking Latency</CardTitle>
								<CardDescription>
									Time to process content chunks
								</CardDescription>
							</CardHeader>
							<CardContent>
								{renderChart(
									metrics,
									"chunkingLatency",
									"Latency (ms)",
									"#dc2626",
								)}
							</CardContent>
						</Card>
					</div>
				</TabsContent>

				<TabsContent value="quality" className="space-y-4">
					<Card>
						<CardHeader>
							<CardTitle>Search Quality</CardTitle>
							<CardDescription>
								Metrics for search result quality
							</CardDescription>
						</CardHeader>
						<CardContent>
							{renderChart(
								metrics,
								"averageSimilarity",
								"Similarity Score",
								"#2563eb",
							)}
						</CardContent>
					</Card>

					<div className="grid gap-4 md:grid-cols-2">
						<Card>
							<CardHeader>
								<CardTitle>Cache Performance</CardTitle>
								<CardDescription>Cache hit rate over time</CardDescription>
							</CardHeader>
							<CardContent>
								{renderChart(metrics, "cacheHitRate", "Hit Rate", "#16a34a")}
							</CardContent>
						</Card>

						<Card>
							<CardHeader>
								<CardTitle>Result Count</CardTitle>
								<CardDescription>Number of results per search</CardDescription>
							</CardHeader>
							<CardContent>
								{renderChart(metrics, "searchResultsCount", "Count", "#dc2626")}
							</CardContent>
						</Card>
					</div>
				</TabsContent>

				<TabsContent value="errors" className="space-y-4">
					<Card>
						<CardHeader>
							<CardTitle>Error Distribution</CardTitle>
							<CardDescription>Types of errors encountered</CardDescription>
						</CardHeader>
						<CardContent>
							<ResponsiveContainer width="100%" height={300}>
								<BarChart
									data={Object.entries(
										metrics[metrics.length - 1]?.errorTypes || {},
									).map(([type, count]) => ({
										type,
										count,
									}))}
								>
									<CartesianGrid strokeDasharray="3 3" />
									<XAxis dataKey="type" />
									<YAxis />
									<Tooltip />
									<Bar dataKey="count" fill="#dc2626" />
								</BarChart>
							</ResponsiveContainer>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle>Error Timeline</CardTitle>
							<CardDescription>Error count over time</CardDescription>
						</CardHeader>
						<CardContent>
							{renderChart(metrics, "errorCount", "Error Count", "#dc2626")}
						</CardContent>
					</Card>
				</TabsContent>
			</Tabs>
		</div>
	);
}
