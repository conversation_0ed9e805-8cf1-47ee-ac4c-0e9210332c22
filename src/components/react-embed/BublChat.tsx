"use client";

import React, { useEffect, useRef } from "react";

export interface BublChatProps {
	/**
	 * Your website ID from the Bubl dashboard
	 */
	websiteId: string;

	/**
	 * Primary color for the chat widget (hex code)
	 */
	primaryColor?: string;

	/**
	 * Secondary color for the chat widget (hex code)
	 */
	secondaryColor?: string;

	/**
	 * Position of the chat widget
	 */
	position?: "bottom-right" | "bottom-left";

	/**
	 * Welcome message displayed when the chat is opened
	 */
	welcomeMessage?: string;

	/**
	 * Text displayed in the header of the chat widget
	 */
	headerText?: string;

	/**
	 * Whether the chat widget should be initially open
	 */
	initiallyOpen?: boolean;

	/**
	 * Callback function called when the widget is ready
	 */
	onReady?: () => void;
}

/**
 * BublChat is a React component for embedding the Bubl chat widget in React applications.
 *
 * @example
 * ```tsx
 * import { BublChat } from "@bubl/react";
 *
 * function App() {
 *   return (
 *     <div>
 *       <h1>My Website</h1>
 *       <BublChat
 *         websiteId="your-website-id"
 *         primaryColor="#4F46E5"
 *         position="bottom-right"
 *       />
 *     </div>
 *   );
 * }
 * ```
 */
export function BublChat({
	websiteId,
	primaryColor = "#4F46E5",
	secondaryColor = "#FFFFFF",
	position = "bottom-right",
	welcomeMessage = "Hi there! How can I help you today?",
	headerText = "Chat Assistant",
	initiallyOpen = false,
	onReady,
}: BublChatProps) {
	const scriptLoaded = useRef(false);
	const appUrl = process.env.NEXT_PUBLIC_APP_URL || "https://bublai.com";

	useEffect(() => {
		// Skip if the script is already loaded or if we're not in the browser
		if (scriptLoaded.current || typeof window === "undefined") {
			return;
		}

		// Mark as loaded to prevent duplicate loading
		scriptLoaded.current = true;

		// Set up the global Bubl object with configuration
		window.Bubl = window.Bubl || {
			config: {
				websiteId,
				primaryColor,
				secondaryColor,
				position,
				welcomeMessage,
				headerText,
				initiallyOpen,
			},
			onReady:
				onReady ||
				(() => {
					console.log("Bubl widget is ready");
				}),
		};

		// Create and load the script
		const script = document.createElement("script");
		script.async = true;
		script.src = `${appUrl}/widget/v1/loader.js`;

		// Append the script to the document
		const firstScript = document.getElementsByTagName("script")[0];
		firstScript.parentNode?.insertBefore(script, firstScript);

		// Clean up function
		return () => {
			// Remove the widget container if it exists
			const container = document.getElementById("bubl-widget-container");
			if (container) {
				container.remove();
			}

			// Remove the script if it exists
			if (script.parentNode) {
				script.parentNode.removeChild(script);
			}

			// Reset the loaded flag
			scriptLoaded.current = false;

			// Clean up global object
			window.Bubl = undefined;
		};
	}, [
		websiteId,
		primaryColor,
		secondaryColor,
		position,
		welcomeMessage,
		headerText,
		initiallyOpen,
		onReady,
		appUrl,
	]);

	// This component doesn't render anything visible
	return null;
}

// We're using the type declaration from the @bubl/react package
