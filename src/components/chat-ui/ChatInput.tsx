"use client";

import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { FileIcon, PaperclipIcon, SendIcon, XIcon } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { Textarea } from "../ui/textarea";

export interface ChatInputProps {
	value: string;
	onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
	onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
	disabled?: boolean;
	placeholder?: string;
	onFileAttach?: (file: File) => void;
	attachedFiles?: File[];
	onFileRemove?: (fileIndex: number) => void;
}

export function ChatInput({
	value,
	onChange,
	onSubmit,
	disabled = false,
	placeholder = "Type your message...",
	onFileAttach,
	attachedFiles = [],
	onFileRemove,
}: ChatInputProps) {
	const textareaRef = useRef<HTMLTextAreaElement>(null);
	const fileInputRef = useRef<HTMLInputElement>(null);
	const [isDragging, setIsDragging] = useState(false);

	const handleKeyDown = useCallback(
		(e: React.KeyboardEvent<HTMLTextAreaElement>) => {
			if (e.key === "Enter" && !e.shiftKey) {
				e.preventDefault();
				if (value.trim() && !disabled) {
					const form = e.currentTarget.form;
					if (form) {
						const submitEvent = new Event("submit", {
							cancelable: true,
							bubbles: true,
						});
						form.dispatchEvent(submitEvent);
					}
				}
			}
		},
		[value, disabled],
	);

	// Auto-resize textarea
	useEffect(() => {
		const textarea = textareaRef.current;
		if (!textarea) return;

		textarea.style.height = "auto";
		textarea.style.height = `${textarea.scrollHeight}px`;
	}, []);

	const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();
		if (value.trim() && !disabled) {
			onSubmit(e);
		}
	};

	return (
		<form
			onSubmit={handleFormSubmit}
			className="p-4 bg-zinc-900 border-t border-zinc-800"
		>
			{/* Attached files */}
			{attachedFiles.length > 0 && (
				<div className="mb-2 flex flex-wrap gap-2">
					{attachedFiles.map((file, index) => (
						<div
							key={`file-${file.name}-${file.size}-${index}`}
							className="flex items-center gap-2 bg-zinc-800 p-2 rounded-md text-sm"
						>
							<div className="flex-shrink-0 text-zinc-400">
								<FileIcon size={16} />
							</div>
							<div className="flex-1 min-w-0">
								<div className="truncate font-medium text-zinc-300">
									{file.name}
								</div>
								<div className="text-xs text-zinc-500">
									{(file.size / 1024).toFixed(1)} KB
								</div>
							</div>
							<button
								type="button"
								className="flex-shrink-0 p-1 rounded-full hover:bg-zinc-700"
								onClick={() => onFileRemove?.(index)}
								aria-label="Remove file"
							>
								<XIcon size={16} className="text-zinc-400" />
							</button>
						</div>
					))}
				</div>
			)}

			{/* Input area with drag and drop */}
			<div
				className={cn(
					"relative",
					isDragging &&
						"bg-zinc-800/50 border-2 border-dashed border-primary rounded-md",
				)}
			>
				<div className="relative flex items-end gap-2">
					<Textarea
						ref={textareaRef}
						value={value}
						onChange={onChange}
						onKeyDown={handleKeyDown}
						placeholder={placeholder}
						disabled={disabled}
						className={cn(
							"flex-1 p-3 max-h-[150px] font-sm",
							"rounded-md border border-zinc-700 bg-zinc-800 resize-none",
							"focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-zinc-600 focus-visible:border-zinc-600",
							"disabled:opacity-50 disabled:cursor-not-allowed",
							"placeholder:text-zinc-500 text-zinc-300",
						)}
						rows={1}
					/>

					<Button
						type="submit"
						disabled={!value.trim() || disabled}
						size="icon"
						variant="ghost"
						className="bg-zinc-800 hover:bg-zinc-700 text-zinc-400 hover:text-zinc-300 rounded-full h-10 w-10"
						aria-label="Send message"
					>
						<SendIcon className="w-5 h-5" />
					</Button>
				</div>
			</div>
		</form>
	);
}
