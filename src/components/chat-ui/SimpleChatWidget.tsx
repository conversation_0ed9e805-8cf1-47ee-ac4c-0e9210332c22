"use client";

import { useEffect, useState } from "react";
import { Chat<PERSON> } from "./ChatUI";

export interface SimpleChatWidgetProps {
	websiteId?: string;
	position?: "bottom-right" | "bottom-left";
	title?: string;
	primaryColor?: string;
	secondaryColor?: string;
	welcomeMessage?: string;
	initiallyOpen?: boolean;
	isWidget?: boolean;
	visitorId?: string;
}

export function SimpleChatWidget({
	websiteId,
	position = "bottom-right",
	title = "Chat Assistant",
	primaryColor = "#4F46E5",
	secondaryColor = "#FFFFFF",
	welcomeMessage = "Hi there! How can I help you today?",
	initiallyOpen = false,
	isWidget = false,
	visitorId,
}: SimpleChatWidgetProps) {
	const [mounted, setMounted] = useState(false);

	// Set mounted to true after the component mounts to avoid hydration issues
	useEffect(() => {
		setMounted(true);
	}, []);

	// Only render the content after the component has mounted on the client
	if (!mounted) {
		return null;
	}

	return (
		<ChatUI
			websiteId={websiteId}
			position={position}
			title={title}
			primaryColor={primaryColor}
			secondaryColor={secondaryColor}
			initiallyOpen={initiallyOpen}
			isWidget={isWidget}
			visitorId={visitorId}
			initialMessages={
				welcomeMessage
					? [
							{
								role: "assistant",
								content: welcomeMessage,
							},
						]
					: []
			}
		/>
	);
}
