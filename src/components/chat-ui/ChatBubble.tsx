"use client";

import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import type { ChatBubbleProps } from "@/types/chat";
import {
	CheckIcon,
	CopyIcon,
	ThumbsDownIcon,
	ThumbsUpIcon,
} from "lucide-react";
import { useEffect, useState } from "react";
import ReactMarkdown from "react-markdown";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { oneDark } from "react-syntax-highlighter/dist/esm/styles/prism";
import remarkGfm from "remark-gfm";

export function ChatBubble({ message }: ChatBubbleProps) {
	const isUser = message.role === "user";
	const [isClient, setIsClient] = useState(false);
	const [isCopied, setIsCopied] = useState(false);
	const [reaction, setReaction] = useState<"like" | "dislike" | null>(null);

	// Set isClient to true once component mounts
	useEffect(() => {
		setIsClient(true);
	}, []);

	// Reset copy state after 2 seconds
	useEffect(() => {
		if (isCopied) {
			const timer = setTimeout(() => {
				setIsCopied(false);
			}, 2000);
			return () => clearTimeout(timer);
		}
	}, [isCopied]);

	// Handle copy code to clipboard
	const handleCopyCode = (code: string) => {
		navigator.clipboard.writeText(code);
		setIsCopied(true);
	};

	// Handle reaction
	const handleReaction = (type: "like" | "dislike") => {
		setReaction(reaction === type ? null : type);
	};

	return (
		<div
			className={cn(
				"flex items-start gap-2",
				isUser ? "flex-row-reverse" : "flex-row",
			)}
		>
			<Avatar className="mt-1">
				{isUser ? (
					<AvatarFallback className="bg-primary text-primary-foreground text-xs">
						U
					</AvatarFallback>
				) : (
					<AvatarFallback className="bg-blue-600 text-white text-xs">
						AI
					</AvatarFallback>
				)}
			</Avatar>
			<div
				className={cn(
					"max-w-[80%] px-4 py-3 rounded-lg chat-message",
					isUser ? "bg-indigo-600 text-white" : "bg-zinc-800 text-zinc-300",
				)}
			>
				<div className="whitespace-pre-wrap break-words prose-sm">
					<ReactMarkdown
						remarkPlugins={[remarkGfm]}
						components={{
							// @ts-ignore - inline is a valid prop from react-markdown
							code({ node, inline, className, children, ...props }) {
								const match = /language-(\w+)/.exec(className || "");
								const language = match ? match[1] : "";

								return !inline && match ? (
									<div className="rounded-md overflow-hidden my-2 border border-zinc-700">
										<div className="flex items-center justify-between px-4 py-2 bg-zinc-900">
											<span className="text-xs font-mono text-zinc-400">
												{language}
											</span>
											<button
												type="button"
												className="p-1 rounded hover:bg-zinc-800 transition-colors"
												onClick={() => handleCopyCode(String(children))}
											>
												{isCopied ? (
													<CheckIcon size={16} className="text-green-500" />
												) : (
													<CopyIcon size={16} className="text-zinc-400" />
												)}
											</button>
										</div>
										<SyntaxHighlighter
											// @ts-ignore - oneDark style is compatible
											style={oneDark}
											language={language}
											PreTag="div"
											{...props}
											className="text-sm"
										>
											{String(children).replace(/\n$/, "")}
										</SyntaxHighlighter>
									</div>
								) : (
									<code
										className={cn(
											"rounded bg-zinc-900 px-1.5 py-0.5 font-mono text-sm text-zinc-300",
											className,
										)}
										{...props}
									>
										{children}
									</code>
								);
							},
							// Enhance other markdown elements
							a: ({ node, ...props }) => (
								<a
									className="text-primary underline hover:text-primary/80"
									target="_blank"
									rel="noopener noreferrer"
									{...props}
								/>
							),
							ul: ({ node, ...props }) => (
								<ul className="list-disc pl-6 my-2" {...props} />
							),
							ol: ({ node, ...props }) => (
								<ol className="list-decimal pl-6 my-2" {...props} />
							),
							li: ({ node, ...props }) => <li className="my-1" {...props} />,
							blockquote: ({ node, ...props }) => (
								<blockquote
									className="border-l-4 border-muted-foreground/20 pl-4 italic my-2"
									{...props}
								/>
							),
							table: ({ node, ...props }) => (
								<div className="overflow-x-auto my-4">
									<table className="w-full border-collapse" {...props} />
								</div>
							),
							th: ({ node, ...props }) => (
								<th
									className="border border-muted-foreground/20 px-4 py-2 text-left font-semibold"
									{...props}
								/>
							),
							td: ({ node, ...props }) => (
								<td
									className="border border-muted-foreground/20 px-4 py-2"
									{...props}
								/>
							),
						}}
					>
						{message.content}
					</ReactMarkdown>
				</div>

				{/* Message footer with timestamp and reactions */}
				<div className="flex items-center justify-between mt-2">
					{isClient && (
						<div
							className={cn(
								"text-xs",
								isUser ? "text-white/70" : "text-zinc-500",
							)}
						>
							{new Date(message.timestamp).toLocaleTimeString()}
						</div>
					)}

					{/* {!isUser && (
						<div className="flex items-center gap-1">
							<Button
								variant="ghost"
								size="icon"
								className={cn(
									"h-6 w-6",
									reaction === "like" && "text-green-500",
								)}
								onClick={() => handleReaction("like")}
							>
								<ThumbsUpIcon size={14} />
							</Button>
							<Button
								variant="ghost"
								size="icon"
								className={cn(
									"h-6 w-6",
									reaction === "dislike" && "text-red-500",
								)}
								onClick={() => handleReaction("dislike")}
							>
								<ThumbsDownIcon size={14} />
							</Button>
						</div>
					)} */}
				</div>
			</div>
		</div>
	);
}
