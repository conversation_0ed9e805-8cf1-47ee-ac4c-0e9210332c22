"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { MessageSquare, X } from "lucide-react";

export interface ChatToggleButtonProps {
	isOpen: boolean;
	onClick: () => void;
	primaryColor?: string;
	position?: "bottom-right" | "bottom-left";
}

export function ChatToggleButton({
	isOpen,
	onClick,
	primaryColor = "#4F46E5",
	position = "bottom-right",
}: ChatToggleButtonProps) {
	// Enhanced click handler to ensure the event is properly processed
	const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
		console.log(
			"ChatToggleButton: Button clicked, current isOpen state:",
			isOpen,
		);

		// Call the original onClick handler
		onClick();

		// Prevent any default behavior or event bubbling that might interfere
		e.preventDefault();
		e.stopPropagation();
	};

	return (
		<button
			type="button"
			onClick={handleClick}
			style={{ backgroundColor: primaryColor }}
			className={cn(
				"chat-toggle-button fixed z-50 rounded-full p-3 shadow-lg transition-all duration-300 ease-in-out",
				"w-12 h-12 flex items-center justify-center",
				position === "bottom-right" ? "bottom-4 right-4" : "bottom-4 left-4",
			)}
			aria-label={isOpen ? "Close chat" : "Open chat"}
		>
			{isOpen ? (
				<X className="h-5 w-5 text-white" />
			) : (
				<MessageSquare className="h-5 w-5 text-white" />
			)}
		</button>
	);
}
