"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useSidebar } from "@/contexts/SidebarContext";
import { useWebsites } from "@/hooks";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import {
	BarChart3,
	ChevronDown,
	CreditCard,
	Globe,
	Laptop,
	LayoutDashboard,
	Loader2,
	MessageCircle,
	Settings,
	Zap,
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useMemo, useState } from "react";

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {}

// Define types for sidebar items
interface SubItem {
	title: string;
	href: string;
	icon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
	tooltip?: string;
}

// Used for the items in the sidebar
type SidebarItem = {
	title: string;
	icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
	href: string;
	subItems?: SubItem[];
	dynamicSubItems?: "loading" | SubItem[];
};

export function ModernSidebar({ className }: SidebarProps) {
	const pathname = usePathname();
	const { data: websites, isLoading: isLoadingWebsites } = useWebsites();
	const { close } = useSidebar();
	const [openMenus, setOpenMenus] = useState<Record<string, boolean>>({
		// Default to open the Websites section if we're on a website-related page
		Websites: pathname?.includes("/dashboard/websites") ?? false,
	});

	// Function to handle link clicks - closes sidebar on mobile
	const handleLinkClick = () => {
		// Only close the sidebar on mobile devices
		if (typeof window !== "undefined" && window.innerWidth < 768) {
			close();
		}
	};

	const sidebarItems: SidebarItem[] = useMemo(
		() => [
			{
				title: "Overview",
				icon: LayoutDashboard,
				href: "/dashboard",
			},
			{
				title: "Websites",
				icon: Globe,
				href: "/dashboard/websites",
				subItems: [
					{ title: "All Sites", href: "/dashboard/websites", icon: Globe },
					// {
					// 	title: "Add New",
					// 	href: "/dashboard/websites#add",
					// 	icon: PlusCircle,
					// },
				],
				dynamicSubItems: isLoadingWebsites
					? "loading"
					: websites?.map((website) => ({
							title: website.name,
							href: `/dashboard/websites/${website.id}`,
							icon: Laptop,
							tooltip: website.url,
						})),
			},
			{
				title: "Plans",
				icon: CreditCard,
				href: "/dashboard/plans",
			},

			// RAG Optimization page hidden temporarily - will be shown only to admin users later
			// {
			// 	title: "RAG Optimization",
			// 	icon: Zap,
			// 	href: "/dashboard/rag-optimization",
			// },
			{
				title: "Playground",
				icon: MessageCircle,
				href: "/dashboard/playground",
			},
			{
				title: "Settings",
				icon: Settings,
				href: "/dashboard/settings",
			},
		],
		[websites, isLoadingWebsites],
	);

	// Auto-open the menu for the current section when the path changes
	useEffect(() => {
		// Only run this effect once on mount and when pathname changes
		const currentItems = sidebarItems; // Capture the current value

		// Check if we need to open any menus based on the current path
		const updatedMenus = { ...openMenus };
		let hasChanges = false;

		for (const item of currentItems) {
			// Only open menus for non-dashboard paths that match the current path
			// and only if they have subitems
			if (
				pathname?.includes(item.href) &&
				item.href !== "/dashboard" &&
				(item.subItems || item.dynamicSubItems)
			) {
				// Only update if the menu isn't already open
				if (!updatedMenus[item.title]) {
					updatedMenus[item.title] = true;
					hasChanges = true;
				}
			}
		}

		// Only update state if we actually made changes
		if (hasChanges) {
			setOpenMenus(updatedMenus);
		}
	}, [pathname, openMenus, sidebarItems]); // Added missing dependencies

	return (
		<div className={cn("pb-12 min-h-screen bg-sidebar", className)}>
			<div className="space-y-6 py-8">
				<div className="px-6">
					<div className="mb-10">
						<Link
							href="/dashboard"
							className="flex items-center space-x-3"
							onClick={handleLinkClick}
						>
							<div className="p-1.5 bg-primary/10 rounded-md">
								<Zap className="h-6 w-6 text-primary" />
							</div>
							<span className="text-xl font-semibold text-sidebar-foreground">
								Bubl
							</span>
						</Link>
					</div>
					<nav className="space-y-1.5">
						{sidebarItems.map((item, index) => (
							<div key={item.title} className="mb-1">
								<div className="flex items-center">
									<Link
										href={item.href}
										className={cn(
											"flex-1",
											// Don't navigate if clicking on an item with subitems
											item.subItems || item.dynamicSubItems
												? "pointer-events-none"
												: "",
										)}
										onClick={handleLinkClick}
									>
										<motion.div
											initial={{ opacity: 0, x: -10 }}
											animate={{ opacity: 1, x: 0 }}
											transition={{ delay: index * 0.05 }}
											className="flex-1"
										>
											<Button
												variant={pathname === item.href ? "secondary" : "ghost"}
												className={cn(
													"w-full justify-start text-sm font-medium rounded-lg h-10 cursor-pointer",
													pathname === item.href
														? "bg-primary/10 text-primary hover:bg-primary/20"
														: "text-sidebar-foreground hover:text-sidebar-foreground hover:bg-sidebar-accent/30",
												)}
											>
												<item.icon className="mr-3 h-4.5 w-4.5" />
												{item.title}
												{/* {(item.subItems || item.dynamicSubItems) && (
													<ChevronDown
														className={cn(
															"ml-auto h-4 w-4 opacity-60 transition-transform duration-200",
															openMenus[item.title] ? "transform rotate-180" : ""
														)}
													/>
												)} */}
											</Button>
										</motion.div>
									</Link>
									{(item.subItems || item.dynamicSubItems) && (
										<Button
											variant="ghost"
											size="sm"
											className="h-8 w-8 p-0 ml-1"
											onClick={(e) => {
												// Stop propagation to prevent any parent handlers from firing
												e.stopPropagation();
												// Toggle this specific menu
												setOpenMenus((prev: Record<string, boolean>) => ({
													...prev,
													[item.title]: !prev[item.title],
												}));
											}}
										>
											<span className="sr-only">Toggle {item.title} menu</span>
											<ChevronDown
												className={cn(
													"h-4 w-4 transition-transform duration-200",
													openMenus[item.title] ? "transform rotate-180" : "",
												)}
											/>
										</Button>
									)}
								</div>
								{(item.subItems || item.dynamicSubItems) &&
									openMenus[item.title] && (
										<div className="ml-4 mt-1 space-y-1">
											{/* Static subitems */}
											{item.subItems?.map((subItem) => (
												<Link
													key={subItem.title}
													href={subItem.href}
													className="cursor-pointer"
													onClick={handleLinkClick}
												>
													<Button
														variant="ghost"
														className={cn(
															"w-full justify-start pl-7 text-sm h-9 rounded-md cursor-pointer",
															pathname === subItem.href
																? "text-primary font-medium"
																: "text-sidebar-foreground/70 hover:text-sidebar-foreground",
														)}
													>
														{subItem.icon && (
															<subItem.icon className="mr-2 h-4 w-4" />
														)}
														{subItem.title}
													</Button>
												</Link>
											))}

											{/* Divider if we have both static and dynamic items */}
											{item.subItems &&
												item.dynamicSubItems &&
												item.dynamicSubItems !== "loading" &&
												item.dynamicSubItems.length > 0 && (
													<div className="my-2 border-t border-sidebar-accent/30" />
												)}

											{/* Loading state */}
											{item.dynamicSubItems === "loading" && (
												<div className="px-2 py-1">
													<div className="flex items-center space-x-2">
														<Loader2 className="h-3 w-3 animate-spin text-sidebar-foreground/50" />
														<span className="text-xs text-sidebar-foreground/50">
															Loading websites...
														</span>
													</div>
												</div>
											)}

											{/* Dynamic subitems */}
											{item.dynamicSubItems &&
												item.dynamicSubItems !== "loading" &&
												Array.isArray(item.dynamicSubItems) &&
												item.dynamicSubItems.map((subItem) => (
													<Link
														key={subItem.href}
														href={subItem.href}
														onClick={handleLinkClick}
													>
														<Button
															variant="ghost"
															className={cn(
																"w-full justify-start pl-7 text-sm h-9 rounded-md group",
																pathname === subItem.href
																	? "text-primary font-medium"
																	: "text-sidebar-foreground/70 hover:text-sidebar-foreground",
															)}
															title={subItem.tooltip}
														>
															{subItem.icon && (
																<subItem.icon className="mr-2 h-4 w-4" />
															)}
															<span className="truncate max-w-[150px]">
																{subItem.title}
															</span>
														</Button>
													</Link>
												))}
										</div>
									)}
							</div>
						))}
					</nav>
				</div>
			</div>
			<div className="absolute bottom-0 left-0 right-0 p-6">
				<div className="rounded-lg bg-primary/5 p-4 border border-primary/10">
					<h4 className="text-sm font-medium text-sidebar-foreground mb-2">
						Need help?
					</h4>
					<p className="text-xs text-sidebar-foreground/70 mb-3">
						Check our documentation for tips and guides.
					</p>
					<Button
						variant="outline"
						size="sm"
						className="w-full bg-sidebar border-primary/20 text-primary hover:bg-primary/10"
						onClick={handleLinkClick}
					>
						View Docs
					</Button>
				</div>
			</div>
		</div>
	);
}
