"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON>,
	CardContent,
	CardDescription,
	<PERSON><PERSON><PERSON>er,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { useUserPlan } from "@/hooks";
import { usePlans } from "@/hooks/usePlans";
import { useUpdateUserPlan } from "@/hooks/useUpdateUserPlan";
import { Check, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

export function PlansClient() {
	const router = useRouter();
	const { data: plans, isLoading: isPlansLoading } = usePlans();
	const { data: userPlan, isLoading: isUserPlanLoading } = useUserPlan();
	const updateUserPlan = useUpdateUserPlan();

	const isLoading = isPlansLoading || isUserPlanLoading;

	const handleSelectPlan = async (planId: string) => {
		try {
			await updateUserPlan.mutateAsync({ planId });
			toast.success("Plan updated successfully");

			// Redirect to dashboard after successful plan update
			setTimeout(() => {
				router.push("/dashboard");
			}, 1000);
		} catch (error) {
			console.error("Error updating plan:", error);
			toast.error(
				error instanceof Error ? error.message : "Failed to update plan",
			);
		}
	};

	const formatPrice = (price: number) => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: "USD",
			minimumFractionDigits: 0,
		}).format(price / 100);
	};

	if (isLoading) {
		return (
			<div className="flex justify-center items-center h-64">
				<Loader2 className="h-8 w-8 animate-spin text-primary" />
			</div>
		);
	}

	return (
		<div className="space-y-8">
			<div className="mb-4">
				<h1 className="text-3xl font-bold text-foreground mb-2">
					Subscription Plans
				</h1>
				<p className="text-muted-foreground">
					Choose the plan that best fits your needs
				</p>
			</div>

			<div className="grid grid-cols-1 gap-6 md:grid-cols-3">
				{plans?.map((plan) => {
					const isCurrentPlan = userPlan?.id === plan.id;
					const features = plan.features
						? plan.features.split(",").map((f) => f.trim())
						: [];

					return (
						<Card
							key={plan.id}
							className={`border-border/40 shadow-sm hover:shadow-md transition-shadow duration-200 ${
								isCurrentPlan ? "border-primary/50 bg-primary/5" : ""
							}`}
						>
							<CardHeader>
								<CardTitle className="text-xl font-bold text-card-foreground">
									{plan.name}
								</CardTitle>
								<CardDescription className="text-muted-foreground">
									{plan.description}
								</CardDescription>
								<div className="mt-2">
									<span className="text-3xl font-bold text-card-foreground">
										{formatPrice(plan.price)}
									</span>
									{plan.price > 0 && (
										<span className="text-muted-foreground ml-1">/month</span>
									)}
								</div>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="space-y-2">
									<div className="flex justify-between text-sm">
										<span className="text-muted-foreground">Websites</span>
										<span className="font-medium text-card-foreground">
											{plan.websiteLimit}
										</span>
									</div>
									<div className="flex justify-between text-sm">
										<span className="text-muted-foreground">
											Pages per website
										</span>
										<span className="font-medium text-card-foreground">
											{plan.pagesPerWebsiteLimit}
										</span>
									</div>
									<div className="flex justify-between text-sm">
										<span className="text-muted-foreground">
											Messages per day
										</span>
										<span className="font-medium text-card-foreground">
											{plan.messagesPerDayLimit}
										</span>
									</div>
								</div>

								<div className="pt-4 space-y-2">
									{features.map((feature) => (
										<div key={feature} className="flex items-center gap-2">
											<Check className="h-4 w-4 text-primary" />
											<span className="text-sm">{feature}</span>
										</div>
									))}
								</div>
							</CardContent>
							<CardFooter>
								<Button
									className={`w-full rounded-full ${
										isCurrentPlan
											? "bg-primary/10 text-primary hover:bg-primary/20 border-primary/20"
											: ""
									}`}
									variant={isCurrentPlan ? "outline" : "default"}
									disabled={isCurrentPlan || updateUserPlan.isPending}
									onClick={() => handleSelectPlan(plan.id)}
								>
									{updateUserPlan.isPending ? (
										<>
											<Loader2 className="mr-2 h-4 w-4 animate-spin" />
											Updating...
										</>
									) : isCurrentPlan ? (
										"Current Plan"
									) : (
										"Select Plan"
									)}
								</Button>
							</CardFooter>
						</Card>
					);
				})}
			</div>
		</div>
	);
}
