"use client";

import { OptimizeRagButton } from "@/components/dashboard/websites/optimize-rag-button";
import { ErrorState, LoadingState } from "@/components/ui";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { useWebsite } from "@/hooks";
import { useCsrfToken } from "@/hooks/useCsrfToken";
import type { Website } from "@/lib/api/types";
import { ArrowLeft, RefreshCw } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { toast } from "sonner";
import { ChatConfigurationForm } from "./ChatConfigurationForm";
import { CrawlStatusList } from "./CrawlStatusList";
import { EmbedCodeGenerator } from "./EmbedCodeGenerator";

interface WebsiteDetailClientProps {
	id: string;
	initialData?: Website;
}

export function WebsiteDetailClient({
	id,
	initialData,
}: WebsiteDetailClientProps) {
	const {
		data: website,
		isLoading,
		isError,
		error,
		refetch,
	} = useWebsite(id, { initialData });
	const [isCrawling, setIsCrawling] = useState(false);
	const [configUpdated, setConfigUpdated] = useState(false);
	const { getRequestOptions } = useCsrfToken();

	const handleCrawlWebsite = async () => {
		try {
			setIsCrawling(true);
			const response = await fetch(
				`/api/dashboard/websites/${id}/crawl`,
				getRequestOptions({
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						maxPages: 100,
						maxDepth: 3,
						generateEmbeddings: true,
					}),
				}),
			);

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to start crawling");
			}

			toast.success("Website crawling started", {
				description: "This process may take a few minutes to complete.",
			});
		} catch (err) {
			toast.error("Failed to start crawling", {
				description:
					err instanceof Error ? err.message : "An unknown error occurred",
			});
		} finally {
			setIsCrawling(false);
		}
	};

	if (isLoading) {
		return <LoadingState message="Loading website details..." />;
	}

	if (isError) {
		return (
			<ErrorState
				message={error?.message || "Failed to load website details"}
				retry={() => refetch()}
			/>
		);
	}

	if (!website) {
		return (
			<div className="p-6 bg-muted/20 rounded-lg border border-border/40 text-center">
				<p className="text-card-foreground">Website not found</p>
				<Link
					href="/dashboard/websites"
					className="text-primary hover:text-primary/80 mt-4 inline-flex items-center"
				>
					<ArrowLeft className="mr-2 h-4 w-4" />
					Back to websites
				</Link>
			</div>
		);
	}

	return (
		<div className="space-y-8">
			<div className="mb-4">
				<h1 className="text-3xl font-bold text-foreground mb-2">
					{website.name}
				</h1>
				<p className="text-muted-foreground">
					Manage and configure your website
				</p>
			</div>

			<div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
				<Link
					href="/dashboard/websites"
					className="inline-flex items-center text-sm text-primary hover:text-primary/80"
				>
					<ArrowLeft className="mr-2 h-4 w-4" />
					Back to websites
				</Link>

				<div className="flex gap-2">
					<Link href={`/dashboard/websites/${id}/crawl`}>
						<Button variant="outline" className="rounded-full">
							Configure Crawler
						</Button>
					</Link>
					<Button
						onClick={handleCrawlWebsite}
						disabled={isCrawling}
						className="rounded-full"
					>
						{isCrawling ? (
							<>
								<RefreshCw className="mr-2 h-4 w-4 animate-spin" />
								Crawling...
							</>
						) : (
							<>
								<RefreshCw className="mr-2 h-4 w-4" />
								Quick Crawl
							</>
						)}
					</Button>
					<OptimizeRagButton websiteId={id} className="rounded-full" />
				</div>
			</div>

			{/* Website Information */}
			<Card className="border-border/40 shadow-sm hover:shadow-md transition-shadow duration-200">
				<CardHeader>
					<CardTitle className="text-card-foreground">
						Website Information
					</CardTitle>
					<CardDescription className="text-muted-foreground">
						Details about this website
					</CardDescription>
				</CardHeader>
				<CardContent>
					<dl className="grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-2">
						<div>
							<dt className="text-sm font-medium text-muted-foreground">
								Name
							</dt>
							<dd className="mt-1 text-sm text-card-foreground">
								{website.name}
							</dd>
						</div>
						<div>
							<dt className="text-sm font-medium text-muted-foreground">URL</dt>
							<dd className="mt-1 text-sm text-card-foreground">
								<a
									href={website.url}
									target="_blank"
									rel="noopener noreferrer"
									className="text-primary hover:text-primary/80 flex items-center"
								>
									{website.url}
									<ArrowLeft className="ml-1 h-3 w-3 rotate-[135deg]" />
								</a>
							</dd>
						</div>
						<div>
							<dt className="text-sm font-medium text-muted-foreground">
								Created
							</dt>
							<dd className="mt-1 text-sm text-card-foreground">
								{new Date(website.createdAt).toLocaleDateString()}
							</dd>
						</div>
						<div>
							<dt className="text-sm font-medium text-muted-foreground">
								Last Updated
							</dt>
							<dd className="mt-1 text-sm text-card-foreground">
								{new Date(website.updatedAt).toLocaleDateString()}
							</dd>
						</div>
					</dl>
				</CardContent>
			</Card>

			{/* Crawl Status */}
			<CrawlStatusList websiteId={id} />

			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				{/* Chat Configuration Card */}
				<Card className="border-border/40 shadow-sm hover:shadow-md transition-shadow duration-200 ">
					<CardHeader>
						<CardTitle className="text-card-foreground">
							Chat Configuration
						</CardTitle>
						<CardDescription className="text-muted-foreground">
							Customize your chat widget
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<p className="text-muted-foreground">
							Configure your chat widget appearance and behavior, then get the
							embed code to add it to your website.
						</p>
						<Link href={`/dashboard/websites/${id}/chat-config`}>
							<Button className="w-full">Configure Chat Widget</Button>
						</Link>
					</CardContent>
				</Card>

				{/* Analytics Card */}
				<Card className="border-border/40 shadow-sm hover:shadow-md transition-shadow duration-200">
					<CardHeader>
						<CardTitle className="text-card-foreground">
							Chat Analytics
						</CardTitle>
						<CardDescription className="text-muted-foreground">
							View usage and performance metrics
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<p className="text-muted-foreground">
							Track chat usage, monitor performance, and analyze how users
							interact with your chat widget.
						</p>
						<Link href={`/dashboard/websites/${id}/analytics`}>
							<Button className="w-full" variant="outline">
								View Analytics
							</Button>
						</Link>
					</CardContent>
				</Card>
			</div>

			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				{/* Crawled Pages Card */}
				<Card className="border-border/40 shadow-sm hover:shadow-md transition-shadow duration-200">
					<CardHeader>
						<CardTitle className="text-card-foreground">
							Crawled Pages
						</CardTitle>
						<CardDescription className="text-muted-foreground">
							View all pages that have been crawled
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<p className="text-muted-foreground">
							Browse all the pages that have been crawled from your website and
							see when they were last updated.
						</p>
						<Link href={`/dashboard/websites/${id}/pages`}>
							<Button className="w-full" variant="outline">
								View Pages
							</Button>
						</Link>
					</CardContent>
				</Card>

				{/* Conversations Card */}
				<Card className="border-border/40 shadow-sm hover:shadow-md transition-shadow duration-200">
					<CardHeader>
						<CardTitle className="text-card-foreground">
							Conversations
						</CardTitle>
						<CardDescription className="text-muted-foreground">
							View chat conversations with visitors
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<p className="text-muted-foreground">
							Browse all conversations that visitors have had with your chat
							widget and see the full message history.
						</p>
						<Link href={`/dashboard/websites/${id}/conversations`}>
							<Button className="w-full" variant="outline">
								View Conversations
							</Button>
						</Link>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
