"use client";

import { WebsiteForm } from "@/components/WebsiteForm";
import { WebsiteList } from "@/components/WebsiteList";
import { ErrorState, LoadingState } from "@/components/ui";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { usePlanUsage, useUserPlan, useWebsites } from "@/hooks";
import type { Website } from "@/lib/api/types";
import { CreditCard, PlusIcon } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

interface WebsitesClientProps {
	initialData?: Website[];
}

export function WebsitesClient({ initialData }: WebsitesClientProps = {}) {
	const {
		data: websites,
		isLoading: isWebsitesLoading,
		isError: isWebsitesError,
		error: websitesError,
	} = useWebsites();
	const {
		data: userPlan,
		isLoading: isPlanLoading,
		isError: isPlanError,
		error: planError,
	} = useUserPlan();
	const {
		data: planUsage,
		isLoading: isUsageLoading,
		isError: isUsageError,
	} = usePlanUsage();
	const [showAddForm, setShowAddForm] = useState(false);

	// Check for hash fragment to show add form
	useEffect(() => {
		if (typeof window !== "undefined") {
			if (window.location.hash === "#add") {
				setShowAddForm(true);
				// Remove the hash to avoid showing the form again on refresh
				window.history.replaceState(null, "", window.location.pathname);
			}
		}
	}, []);

	// Combined loading and error states
	const isLoading = isWebsitesLoading || isPlanLoading || isUsageLoading;
	const isError = isWebsitesError || isPlanError || isUsageError;
	const error = websitesError || planError;

	// Check if the user has any websites
	const hasWebsites = websites && websites.length > 0;

	// Check if the user has reached their website limit
	const hasReachedLimit =
		planUsage && planUsage.websites.count >= planUsage.websites.limit;

	return (
		<div className="space-y-8">
			<div className="mb-4">
				<h1 className="text-3xl font-bold text-foreground mb-2">Websites</h1>
				<p className="text-muted-foreground">
					Manage your website integration with Bubl
				</p>
			</div>

			{isLoading ? (
				<LoadingState message="Loading website data..." />
			) : isError ? (
				<ErrorState
					message={error?.message || "Failed to load website data"}
					retry={() => window.location.reload()}
				/>
			) : (
				<>
					{/* Plan Information Card */}
					{userPlan && planUsage && (
						<Card className="border-border/40 shadow-sm hover:shadow-md transition-shadow duration-200 mb-6">
							<CardHeader className="pb-2">
								<CardTitle className="text-lg">Your Plan</CardTitle>
								<CardDescription>
									{userPlan.name} Plan - {planUsage.websites.count} of{" "}
									{planUsage.websites.limit} websites used
								</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
									<div>
										<p className="text-sm text-muted-foreground mb-2">
											{hasReachedLimit
												? "You've reached your website limit. Upgrade your plan to add more websites."
												: `You can add ${planUsage.websites.limit - planUsage.websites.count} more website${planUsage.websites.limit - planUsage.websites.count !== 1 ? "s" : ""}.`}
										</p>
									</div>
									{userPlan.name !== "Enterprise" && (
										<Link href="/dashboard/plans">
											<Button variant="outline" size="sm" className="text-xs">
												<CreditCard className="mr-2 h-4 w-4" />
												Upgrade Plan
											</Button>
										</Link>
									)}
								</div>
							</CardContent>
						</Card>
					)}

					{/* Add Website Card */}
					<Card className="border-border/40 shadow-sm hover:shadow-md transition-shadow duration-200">
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<div>
								<CardTitle className="text-card-foreground">
									Add Website
								</CardTitle>
								<CardDescription className="text-muted-foreground">
									{hasWebsites
										? "Add another website to your account"
										: "Add your website to get started with Bubl"}
								</CardDescription>
							</div>
							<Button
								onClick={() => setShowAddForm(!showAddForm)}
								variant={showAddForm ? "outline" : "ghost"}
								size="sm"
								disabled={hasReachedLimit}
								className={
									showAddForm
										? "rounded-full border-primary/20 bg-primary/5 text-primary hover:bg-primary/10"
										: "rounded-full"
								}
							>
								{showAddForm ? (
									"Cancel"
								) : hasReachedLimit ? (
									"Limit Reached"
								) : (
									<>
										<PlusIcon className="mr-2 h-4 w-4" />
										Add Website
									</>
								)}
							</Button>
						</CardHeader>
						{showAddForm && !hasReachedLimit && (
							<CardContent>
								<WebsiteForm
									onSuccess={() => setShowAddForm(false)}
									userPlan={userPlan}
									planUsage={planUsage}
								/>
							</CardContent>
						)}
						{hasReachedLimit && (
							<CardContent>
								<div className="p-4 bg-amber-50 dark:bg-amber-950/20 rounded-lg border border-amber-200 dark:border-amber-800/30">
									<p className="text-sm text-amber-800 dark:text-amber-300">
										You've reached your limit of {planUsage?.websites.limit}{" "}
										websites on your {userPlan?.name} plan.
									</p>
									<Link href="/dashboard/plans" className="mt-2 inline-block">
										<Button variant="outline" size="sm" className="text-xs">
											<CreditCard className="mr-2 h-4 w-4" />
											Upgrade to Add More
										</Button>
									</Link>
								</div>
							</CardContent>
						)}
					</Card>

					{/* Websites Card */}
					<Card className="border-border/40 shadow-sm hover:shadow-md transition-shadow duration-200">
						<CardHeader>
							<CardTitle className="text-card-foreground">
								Your Websites
							</CardTitle>
							<CardDescription className="text-muted-foreground">
								Manage and configure your websites
							</CardDescription>
						</CardHeader>
						<CardContent>
							<WebsiteList />
						</CardContent>
					</Card>
				</>
			)}
		</div>
	);
}
