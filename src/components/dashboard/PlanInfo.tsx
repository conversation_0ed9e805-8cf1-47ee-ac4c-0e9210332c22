"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useUserPlan, useWebsites } from "@/hooks";
import { ArrowUpRight } from "lucide-react";
import Link from "next/link";

export function PlanInfo() {
	const { data: plan, isLoading: isPlanLoading } = useUserPlan();
	const { data: websites, isLoading: isWebsitesLoading } = useWebsites();

	const isLoading = isPlanLoading || isWebsitesLoading;
	const websiteCount = websites?.length || 0;
	const websiteLimit = plan?.websiteLimit || 1;
	const websitePercentage = Math.min(
		Math.round((websiteCount / websiteLimit) * 100),
		100,
	);

	const planName = plan?.name || "Free";
	const isFreePlan = planName === "Free";

	return (
		<Card className="border-border/40 shadow-sm hover:shadow-md transition-shadow duration-200">
			<CardHeader className="pb-3">
				<CardTitle className="text-sm font-medium text-muted-foreground">
					Your Plan
				</CardTitle>
				<div className="text-xl font-bold text-card-foreground mt-1">
					{isLoading ? "..." : planName}
				</div>
			</CardHeader>
			<CardContent className="space-y-4">
				{!isLoading && (
					<>
						<div className="space-y-2">
							<div className="flex justify-between text-sm">
								<span className="text-muted-foreground">Websites</span>
								<span className="font-medium text-card-foreground">
									{websiteCount} / {websiteLimit}
								</span>
							</div>
							<Progress value={websitePercentage} className="h-2 bg-muted" />
							{websiteCount >= websiteLimit && (
								<p className="text-xs text-primary">
									You've reached your website limit. Upgrade to add more.
								</p>
							)}
						</div>

						{plan && (
							<div className="space-y-2 text-sm pt-1">
								<div className="flex justify-between">
									<span className="text-muted-foreground">
										Pages per website
									</span>
									<span className="font-medium text-card-foreground">
										{plan.pagesPerWebsiteLimit}
									</span>
								</div>
								<div className="flex justify-between">
									<span className="text-muted-foreground">
										Messages per day
									</span>
									<span className="font-medium text-card-foreground">
										{plan.messagesPerDayLimit}
									</span>
								</div>
							</div>
						)}
					</>
				)}
			</CardContent>
			<CardFooter>
				{isFreePlan ? (
					<Link href="/dashboard/plans" className="w-full">
						<Button
							className="w-full rounded-full bg-primary/10 text-primary hover:bg-primary/20 border-primary/20"
							variant="outline"
						>
							Upgrade Plan
							<ArrowUpRight className="ml-2 h-4 w-4" />
						</Button>
					</Link>
				) : (
					<Link href="/dashboard/plans" className="w-full">
						<Button
							className="w-full rounded-full border-primary/20 bg-primary/5 text-primary hover:bg-primary/10"
							variant="outline"
						>
							Manage Plan
							<ArrowUpRight className="ml-2 h-4 w-4" />
						</Button>
					</Link>
				)}
			</CardFooter>
		</Card>
	);
}
