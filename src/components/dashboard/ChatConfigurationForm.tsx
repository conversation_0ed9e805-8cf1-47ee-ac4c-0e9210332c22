"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { X } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface ChatConfigurationFormProps {
	websiteId: string;
	onConfigUpdate?: () => void; // Called when inputs change (for preview updates)
	onConfigSave?: () => void; // Called when form is submitted (for embed code updates)
	onConfigChange?: (config: ChatConfig) => void; // Called with the current config when inputs change
}

export interface ChatConfig {
	id?: string;
	name: string;
	primaryColor: string;
	secondaryColor: string;
	headerText: string;
	welcomeMessage: string;
	position: string;
	isActive: boolean;
	allowedDomains?: string[];
}

export function ChatConfigurationForm({
	websiteId,
	onConfigUpdate,
	onConfigSave,
	onConfigChange,
}: ChatConfigurationFormProps) {
	const [isLoading, setIsLoading] = useState(false);
	const [config, setConfig] = useState<ChatConfig>({
		name: "Default Configuration",
		primaryColor: "#4F46E5",
		secondaryColor: "#FFFFFF",
		headerText: "Chat Assistant",
		welcomeMessage: "Hi there! How can I help you today?",
		position: "BOTTOM_RIGHT",
		isActive: true,
		allowedDomains: [],
	});

	// Fetch the current configuration
	useEffect(() => {
		const fetchConfig = async () => {
			try {
				setIsLoading(true);
				console.log("Fetching chat config for website:", websiteId);
				const response = await fetch(
					`/api/dashboard/websites/${websiteId}/chat-config`,
				);

				if (!response.ok) {
					console.error(
						"Failed to fetch chat configuration:",
						response.status,
						response.statusText,
					);
					const errorText = await response.text();
					console.error("Error response:", errorText);
					throw new Error(
						`Failed to fetch chat configuration: ${response.status} ${response.statusText}`,
					);
				}

				const responseData = await response.json();
				console.log("Received chat config response:", responseData);

				// Check if the response has the expected format (success and data properties)
				if (responseData?.success && responseData.data) {
					const data = responseData.data;

					setConfig({
						name: data.name || "Default Configuration",
						primaryColor: data.primaryColor || "#4F46E5",
						secondaryColor: data.secondaryColor || "#FFFFFF",
						headerText: data.headerText || "Chat Assistant",
						welcomeMessage:
							data.welcomeMessage || "Hi there! How can I help you today?",
						position: data.position || "BOTTOM_RIGHT",
						isActive: data.isActive !== undefined ? data.isActive : true,
						allowedDomains: data.allowedDomains || [],
					});
				} else {
					console.error("Invalid response format:", responseData);
					toast.error(
						"Failed to load chat configuration: Invalid response format",
					);
				}
			} catch (error) {
				console.error("Error fetching chat configuration:", error);
				toast.error("Failed to load chat configuration");
			} finally {
				setIsLoading(false);
			}
		};

		if (websiteId) {
			fetchConfig();
		}
	}, [websiteId]);

	// Handle input changes without auto-saving
	const handleInputChange = (
		field: string,
		value: string | boolean | string[],
	) => {
		const updatedConfig = { ...config, [field]: value };
		setConfig(updatedConfig);

		// Only update the preview, don't save to the server
		if (onConfigUpdate) {
			onConfigUpdate();
		}

		// Pass the updated config to the parent component
		if (onConfigChange) {
			onConfigChange(updatedConfig);
		}
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		try {
			setIsLoading(true);
			console.log("Updating chat config for website:", websiteId);
			console.log("Config data to send:", config);

			const response = await fetch(
				`/api/dashboard/websites/${websiteId}/chat-config`,
				{
					method: "PUT",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify(config),
				},
			);

			// Clone the response before reading it
			const responseClone = response.clone();

			if (!response.ok) {
				console.error(
					"Failed to update chat configuration:",
					response.status,
					response.statusText,
				);
				let errorMessage = "Failed to update chat configuration";

				try {
					const errorData = await responseClone.json();
					errorMessage = errorData.error || errorMessage;
					console.error("Error response:", errorData);
				} catch (e) {
					console.error("Error parsing error response:", e);
				}

				throw new Error(errorMessage);
			}

			// Use the original response for success case
			// Define a more specific type for the response data
			let responseData: {
				success?: boolean;
				data?: ChatConfig;
				error?: string;
			};
			try {
				responseData = await response.json();
				console.log("Updated config response:", responseData);
			} catch (e) {
				console.error("Error parsing success response:", e);
				throw new Error("Failed to parse server response");
			}

			// Check if the response has the expected format
			if (!responseData || !responseData.success || !responseData.data) {
				console.error("Invalid response format:", responseData);
				throw new Error("Invalid response format");
			}

			// Update the local config with the server response
			const updatedConfig = responseData.data;
			setConfig(updatedConfig);

			toast.success("Chat configuration updated successfully");

			// Call the onConfigSave callback if provided
			if (onConfigSave) {
				onConfigSave();
			}
		} catch (error) {
			console.error("Error updating chat configuration:", error);

			// More detailed error message
			if (error instanceof Error) {
				toast.error(`Failed to update chat configuration: ${error.message}`);
			} else {
				toast.error("Failed to update chat configuration");
			}
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle>Chat Configuration</CardTitle>
			</CardHeader>
			<form onSubmit={handleSubmit}>
				<CardContent className="space-y-4">
					<div className="space-y-2">
						<Label htmlFor="headerText">Chat Header</Label>
						<Input
							id="headerText"
							value={config.headerText}
							onChange={(e) => handleInputChange("headerText", e.target.value)}
							placeholder="Chat Assistant"
							disabled={isLoading}
						/>
					</div>

					<div className="space-y-2">
						<Label htmlFor="welcomeMessage">Welcome Message</Label>
						<Textarea
							id="welcomeMessage"
							value={config.welcomeMessage}
							onChange={(e) =>
								handleInputChange("welcomeMessage", e.target.value)
							}
							placeholder="Hi there! How can I help you today?"
							disabled={isLoading}
						/>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="space-y-2">
							<Label htmlFor="primaryColor">Primary Color</Label>
							<div className="flex items-center gap-2">
								<Input
									id="primaryColor"
									type="color"
									value={config.primaryColor}
									onChange={(e) =>
										handleInputChange("primaryColor", e.target.value)
									}
									className="w-12 h-10 p-1"
									disabled={isLoading}
								/>
								<Input
									type="text"
									value={config.primaryColor}
									onChange={(e) =>
										handleInputChange("primaryColor", e.target.value)
									}
									className="flex-1"
									disabled={isLoading}
								/>
							</div>
						</div>

						<div className="space-y-2">
							<Label htmlFor="secondaryColor">Secondary Color</Label>
							<div className="flex items-center gap-2">
								<Input
									id="secondaryColor"
									type="color"
									value={config.secondaryColor}
									onChange={(e) =>
										handleInputChange("secondaryColor", e.target.value)
									}
									className="w-12 h-10 p-1"
									disabled={isLoading}
								/>
								<Input
									type="text"
									value={config.secondaryColor}
									onChange={(e) =>
										handleInputChange("secondaryColor", e.target.value)
									}
									className="flex-1"
									disabled={isLoading}
								/>
							</div>
						</div>
					</div>

					<div className="space-y-2">
						<Label>Widget Position</Label>
						<div className="flex space-x-4">
							<label className="flex items-center space-x-2">
								<input
									type="radio"
									name="position"
									checked={config.position === "BOTTOM_RIGHT"}
									onChange={() => handleInputChange("position", "BOTTOM_RIGHT")}
									disabled={isLoading}
									className="h-4 w-4 text-primary"
								/>
								<span>Bottom Right</span>
							</label>
							<label className="flex items-center space-x-2">
								<input
									type="radio"
									name="position"
									checked={config.position === "BOTTOM_LEFT"}
									onChange={() => handleInputChange("position", "BOTTOM_LEFT")}
									disabled={isLoading}
									className="h-4 w-4 text-primary"
								/>
								<span>Bottom Left</span>
							</label>
						</div>
					</div>

					<div className="space-y-2">
						<Label htmlFor="allowedDomains">Allowed Domains</Label>
						<p className="text-sm text-muted-foreground mb-2">
							Restrict your widget to specific domains. If empty, the widget can
							be embedded on any website.
						</p>

						{/* Display current allowed domains */}
						<div className="flex flex-wrap gap-2 mb-2">
							{config.allowedDomains?.map((domain) => (
								<div
									key={`domain-${domain}`}
									className="flex items-center bg-secondary text-secondary-foreground px-3 py-1 rounded-full text-sm"
								>
									<span className="mr-1">{domain}</span>
									<button
										type="button"
										onClick={() => {
											const newDomains = [...(config.allowedDomains || [])];
											const indexToRemove = newDomains.indexOf(domain);
											if (indexToRemove !== -1) {
												newDomains.splice(indexToRemove, 1);
												handleInputChange("allowedDomains", newDomains);
											}
										}}
										className="text-secondary-foreground/70 hover:text-secondary-foreground"
										aria-label={`Remove ${domain}`}
									>
										<X className="h-3 w-3" />
									</button>
								</div>
							))}
						</div>

						{/* Input for adding new domains */}
						<div className="flex gap-2">
							<Input
								id="newDomain"
								placeholder="https://example.com"
								disabled={isLoading}
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										e.preventDefault();
										const input = e.currentTarget;
										const value = input.value.trim();

										if (value) {
											try {
												// Basic validation - just check if it's not empty
												// We'll let the server handle more complex validation

												// Add to allowed domains
												const newDomains = [...(config.allowedDomains || [])];
												if (!newDomains.includes(value)) {
													newDomains.push(value);
													handleInputChange("allowedDomains", newDomains);
													input.value = "";
												} else {
													toast.error("Domain already added");
												}
											} catch (error) {
												toast.error("Please enter a valid URL");
											}
										}
									}
								}}
							/>
							<Button
								type="button"
								variant="outline"
								onClick={() => {
									const input = document.getElementById(
										"newDomain",
									) as HTMLInputElement;
									const value = input.value.trim();

									if (value) {
										try {
											// Basic validation - just check if it's not empty
											// We'll let the server handle more complex validation

											// Add to allowed domains
											const newDomains = [...(config.allowedDomains || [])];
											if (!newDomains.includes(value)) {
												newDomains.push(value);
												handleInputChange("allowedDomains", newDomains);
												input.value = "";
											} else {
												toast.error("Domain already added");
											}
										} catch (error) {
											toast.error("Please enter a valid URL");
										}
									}
								}}
								disabled={isLoading}
							>
								Add
							</Button>
						</div>
						<p className="text-xs text-muted-foreground mt-1">
							Press Enter or click Add to add a domain. Include the protocol
							(https://).
						</p>
					</div>
				</CardContent>
				<CardFooter>
					<Button type="submit" disabled={isLoading} className="w-full">
						{isLoading ? "Saving..." : "Save Configuration"}
					</Button>
				</CardFooter>
			</form>
		</Card>
	);
}
