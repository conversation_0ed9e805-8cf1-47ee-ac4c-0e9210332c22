"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { ragOptimizer } from "@/lib/api/rag-optimizer";
import { Loader2, Zap } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface OptimizeRagButtonProps {
	websiteId: string;
	variant?:
		| "default"
		| "outline"
		| "secondary"
		| "ghost"
		| "link"
		| "destructive";
	size?: "default" | "sm" | "lg" | "icon";
	className?: string;
}

export function OptimizeRagButton({
	websiteId,
	variant = "outline",
	size = "default",
	className,
}: OptimizeRagButtonProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [forceOptimization, setForceOptimization] = useState(false);

	const handleOptimize = async () => {
		setIsLoading(true);
		try {
			await ragOptimizer.optimizeWebsite(websiteId, {
				force: forceOptimization,
			});
			toast.success("RAG optimization triggered successfully");
			setIsOpen(false);
		} catch (error) {
			console.error("Error triggering RAG optimization:", error);
			toast.error("Failed to trigger RAG optimization");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<>
			<Button
				variant={variant}
				size={size}
				className={className}
				onClick={() => setIsOpen(true)}
			>
				<Zap className="mr-2 h-4 w-4" />
				Optimize RAG
			</Button>

			<Dialog open={isOpen} onOpenChange={setIsOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Optimize RAG</DialogTitle>
						<DialogDescription>
							This will optimize the Retrieval-Augmented Generation (RAG) system
							for this website. The process will improve search accuracy and
							performance by optimizing chunking, embeddings, and vector
							indexes.
						</DialogDescription>
					</DialogHeader>

					<div className="flex items-center space-x-2 py-4">
						<Switch
							id="force-optimization"
							checked={forceOptimization}
							onCheckedChange={setForceOptimization}
						/>
						<Label htmlFor="force-optimization">Force optimization</Label>
					</div>
					<div className="text-sm text-muted-foreground">
						{forceOptimization
							? "Force optimization will rebuild all embeddings and indexes regardless of whether they need it."
							: "Smart optimization will only rebuild embeddings and indexes if they need it."}
					</div>

					<DialogFooter>
						<Button variant="outline" onClick={() => setIsOpen(false)}>
							Cancel
						</Button>
						<Button onClick={handleOptimize} disabled={isLoading}>
							{isLoading ? (
								<>
									<Loader2 className="mr-2 h-4 w-4 animate-spin" />
									Optimizing...
								</>
							) : (
								<>
									<Zap className="mr-2 h-4 w-4" />
									Optimize
								</>
							)}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}

interface OptimizeAllRagButtonProps {
	variant?:
		| "default"
		| "outline"
		| "secondary"
		| "ghost"
		| "link"
		| "destructive";
	size?: "default" | "sm" | "lg" | "icon";
	className?: string;
}

export function OptimizeAllRagButton({
	variant = "outline",
	size = "default",
	className,
}: OptimizeAllRagButtonProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [forceOptimization, setForceOptimization] = useState(false);

	const handleOptimizeAll = async () => {
		setIsLoading(true);
		try {
			await ragOptimizer.optimizeAllWebsites({
				force: forceOptimization,
			});
			toast.success("RAG optimization for all websites triggered successfully");
			setIsOpen(false);
		} catch (error) {
			console.error(
				"Error triggering RAG optimization for all websites:",
				error,
			);
			toast.error("Failed to trigger RAG optimization for all websites");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<>
			<Button
				variant={variant}
				size={size}
				className={className}
				onClick={() => setIsOpen(true)}
			>
				<Zap className="mr-2 h-4 w-4" />
				Optimize All RAG
			</Button>

			<Dialog open={isOpen} onOpenChange={setIsOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Optimize RAG for All Websites</DialogTitle>
						<DialogDescription>
							This will optimize the Retrieval-Augmented Generation (RAG) system
							for all websites. This is a resource-intensive operation and may
							take some time to complete.
						</DialogDescription>
					</DialogHeader>

					<div className="flex items-center space-x-2 py-4">
						<Switch
							id="force-optimization-all"
							checked={forceOptimization}
							onCheckedChange={setForceOptimization}
						/>
						<Label htmlFor="force-optimization-all">Force optimization</Label>
					</div>
					<div className="text-sm text-muted-foreground">
						{forceOptimization
							? "Force optimization will rebuild all embeddings and indexes regardless of whether they need it."
							: "Smart optimization will only rebuild embeddings and indexes if they need it."}
					</div>

					<DialogFooter>
						<Button variant="outline" onClick={() => setIsOpen(false)}>
							Cancel
						</Button>
						<Button onClick={handleOptimizeAll} disabled={isLoading}>
							{isLoading ? (
								<>
									<Loader2 className="mr-2 h-4 w-4 animate-spin" />
									Optimizing...
								</>
							) : (
								<>
									<Zap className="mr-2 h-4 w-4" />
									Optimize All
								</>
							)}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}
