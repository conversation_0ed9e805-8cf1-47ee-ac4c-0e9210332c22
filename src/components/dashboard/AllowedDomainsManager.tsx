"use client";

import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { AlertCircle, CheckCircle2, Plus, X } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface AllowedDomainsManagerProps {
	websiteId: string;
	initialDomains?: string[];
	onDomainsChange?: (domains: string[]) => void;
	readOnly?: boolean;
}

export function AllowedDomainsManager({
	websiteId,
	initialDomains = [],
	onDomainsChange,
	readOnly = false,
}: AllowedDomainsManagerProps) {
	const [domains, setDomains] = useState<string[]>(initialDomains);
	const [newDomain, setNewDomain] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		// Update domains if initialDomains changes
		setDomains(initialDomains);
	}, [initialDomains]);

	const handleAddDomain = () => {
		if (!newDomain.trim()) {
			toast.error("Please enter a domain");
			return;
		}

		// Format the domain with https:// prefix if needed
		let formattedDomain = newDomain.trim();
		if (
			!formattedDomain.startsWith("http://") &&
			!formattedDomain.startsWith("https://")
		) {
			formattedDomain = `https://${formattedDomain}`;
		}

		// Validate the domain
		try {
			new URL(formattedDomain);
		} catch (error) {
			toast.error("Please enter a valid domain");
			return;
		}

		// Check if domain already exists
		if (domains.includes(formattedDomain)) {
			toast.error("Domain already added");
			return;
		}

		// Add the domain
		const newDomains = [...domains, formattedDomain];
		setDomains(newDomains);
		setNewDomain("");

		// Notify parent component
		if (onDomainsChange) {
			onDomainsChange(newDomains);
		}

		toast.success(`Added domain: ${formattedDomain}`);
	};

	const handleRemoveDomain = (domain: string) => {
		const newDomains = domains.filter((d) => d !== domain);
		setDomains(newDomains);

		// Notify parent component
		if (onDomainsChange) {
			onDomainsChange(newDomains);
		}

		toast.success(`Removed domain: ${domain}`);
	};

	const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter") {
			e.preventDefault();
			handleAddDomain();
		}
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle>Allowed Domains</CardTitle>
				<CardDescription>
					Control which domains can embed your chat widget. If no domains are
					added, the widget can be embedded on any website.
				</CardDescription>
			</CardHeader>
			<CardContent>
				{domains.length === 0 ? (
					<Alert>
						<AlertCircle className="h-4 w-4" />
						<AlertTitle>No domain restrictions</AlertTitle>
						<AlertDescription>
							Your chat widget can currently be embedded on any website. Add
							domains below to restrict access.
						</AlertDescription>
					</Alert>
				) : (
					<Alert variant="default" className="mb-4">
						<CheckCircle2 className="h-4 w-4" />
						<AlertTitle>Domain restrictions active</AlertTitle>
						<AlertDescription>
							Your chat widget can only be embedded on the domains listed below.
						</AlertDescription>
					</Alert>
				)}

				<div className="space-y-2 mt-4">
					<div className="flex flex-wrap gap-2 mb-4">
						{domains.map((domain) => (
							<div
								key={domain}
								className="flex items-center bg-secondary text-secondary-foreground px-3 py-1 rounded-full text-sm"
							>
								<span className="mr-1">{domain}</span>
								{!readOnly && (
									<button
										type="button"
										onClick={() => handleRemoveDomain(domain)}
										className="text-secondary-foreground/70 hover:text-secondary-foreground"
										aria-label={`Remove ${domain}`}
									>
										<X className="h-3 w-3" />
									</button>
								)}
							</div>
						))}
					</div>

					{!readOnly && (
						<div className="flex gap-2">
							<Input
								id="newDomain"
								placeholder="example.com"
								value={newDomain}
								onChange={(e) => setNewDomain(e.target.value)}
								onKeyDown={handleKeyDown}
								disabled={isLoading}
							/>
							<Button
								type="button"
								onClick={handleAddDomain}
								disabled={isLoading || !newDomain.trim()}
							>
								<Plus className="h-4 w-4 mr-1" />
								Add
							</Button>
						</div>
					)}

					{error && (
						<Alert variant="destructive" className="mt-2">
							<AlertCircle className="h-4 w-4" />
							<AlertTitle>Error</AlertTitle>
							<AlertDescription>{error}</AlertDescription>
						</Alert>
					)}
				</div>
			</CardContent>
			<CardFooter className="flex justify-between">
				<div className="text-xs text-muted-foreground">
					{domains.length === 0
						? "No domain restrictions - widget can be embedded anywhere"
						: `${domains.length} domain${domains.length === 1 ? "" : "s"} allowed`}
				</div>
			</CardFooter>
		</Card>
	);
}
