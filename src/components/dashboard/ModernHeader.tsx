"use client";

import { ThemeToggle } from "@/components/ThemeToggle";
import { But<PERSON> } from "@/components/ui/button";
import {
	CommandDialog,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@/components/ui/command";
import { Input } from "@/components/ui/input";
import { useSidebar } from "@/contexts/SidebarContext";
import { cn } from "@/lib/utils";
import { UserButton } from "@clerk/nextjs";
import { Menu, Search } from "lucide-react";
import { useState } from "react";

interface ModernHeaderProps extends React.HTMLAttributes<HTMLElement> {}

export function ModernHeader({ className, ...props }: ModernHeaderProps) {
	const [open, setOpen] = useState(false);
	const { isOpen, toggle } = useSidebar();

	return (
		<header className={cn("sticky top-0 z-50 w-full", className)} {...props}>
			<div className="flex h-16 items-center px-6">
				<Button
					variant="ghost"
					size="icon"
					className="mr-2 h-9 w-9 rounded-full md:hidden"
					onClick={toggle}
					aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
				>
					<Menu className="h-5 w-5" />
				</Button>
				<div className="mr-4 hidden md:flex">
					<div className="relative">
						<Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
						<Input
							placeholder="Search..."
							className="w-[240px] bg-background/50 border-border/50 pl-9 text-foreground placeholder:text-muted-foreground focus-visible:ring-primary/20 rounded-full h-9"
							onClick={() => setOpen(true)}
							readOnly
						/>
					</div>
				</div>
				<div className="flex flex-1 items-center justify-between space-x-4 md:justify-end">
					<div className="w-full flex-1 md:w-auto md:flex-none">
						<Button
							variant="outline"
							size="sm"
							className="inline-flex items-center md:hidden border-border/50 bg-background/50 text-foreground rounded-full"
							onClick={() => setOpen(true)}
						>
							<Search className="mr-2 h-3.5 w-3.5" />
							Search
						</Button>
					</div>
					<nav className="flex items-center gap-4">
						<ThemeToggle />
						<div className="h-8 w-px bg-border/50 mx-1" />
						<UserButton
							appearance={{
								elements: {
									avatarBox: "h-8 w-8",
									userButtonTrigger: "hover:bg-primary/5 rounded-full p-0.5",
								},
							}}
						/>
					</nav>
				</div>
			</div>
			<CommandDialog open={open} onOpenChange={setOpen}>
				<CommandInput placeholder="Type a command or search..." />
				<CommandList>
					<CommandEmpty>No results found.</CommandEmpty>
					<CommandGroup heading="Suggestions">
						<CommandItem>Dashboard</CommandItem>
						<CommandItem>Search Websites...</CommandItem>
						<CommandItem>View Analytics...</CommandItem>
						<CommandItem>Account Settings...</CommandItem>
					</CommandGroup>
				</CommandList>
			</CommandDialog>
		</header>
	);
}
