"use client";

import { ThemeToggle } from "@/components/ThemeToggle";
import { OrganizationSwitcher, UserButton } from "@clerk/nextjs";
import { PlusIcon } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export function DashboardHeader() {
	const [createOrgOpen, setCreateOrgOpen] = useState(false);

	return (
		<header className="flex justify-between items-center p-4 h-16 border-b bg-background">
			<div>
				<Link href="/" className="font-bold text-xl">
					Bubl
				</Link>
			</div>

			<div className="flex items-center gap-4">
				<ThemeToggle />
				<OrganizationSwitcher
					hidePersonal
					afterCreateOrganizationUrl="/setup?from=clerk"
					afterLeaveOrganizationUrl="/"
					afterSelectOrganizationUrl="/dashboard"
					appearance={{
						elements: {
							rootBox: "flex items-center gap-2",
							organizationSwitcherTrigger:
								"flex items-center gap-2 rounded-md p-2 hover:bg-gray-100",
						},
					}}
				/>
				<UserButton afterSignOutUrl="/" />
			</div>
		</header>
	);
}
