"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

const navigation = [
	{ name: "Overview", href: "/dashboard" },
	{ name: "Website", href: "/dashboard/websites" },
	{ name: "Test Chat", href: "/dashboard/test-chat" },
	{ name: "Analytics", href: "/dashboard/analytics" },
	{ name: "Settings", href: "/dashboard/settings" },
];

export function DashboardNavigation() {
	const pathname = usePathname();

	return (
		<nav className="mt-8 border-b border-gray-200">
			<div className="-mb-px flex space-x-8 overflow-x-auto">
				{navigation.map((item) => (
					<Link
						key={item.href}
						href={item.href}
						className={`${
							pathname === item.href
								? "border-primary text-primary"
								: "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
						} whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium`}
					>
						{item.name}
					</Link>
				))}
			</div>
		</nav>
	);
}
