"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState } from "react";

interface DeleteWebsiteDialogProps {
	isOpen: boolean;
	onClose: () => void;
	onConfirm: () => void;
	websiteName: string;
	websiteUrl: string;
	isDeleting: boolean;
}

export function DeleteWebsiteDialog({
	isOpen,
	onClose,
	onConfirm,
	websiteName,
	websiteUrl,
	isDeleting,
}: DeleteWebsiteDialogProps) {
	const [confirmUrl, setConfirmUrl] = useState("");
	const isConfirmDisabled = confirmUrl !== websiteUrl;

	return (
		<Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle className="text-destructive">Delete Website</DialogTitle>
					<DialogDescription>
						This action cannot be undone. This will permanently delete the
						website &quot;{websiteName}&quot; and all of its data.
					</DialogDescription>
				</DialogHeader>
				<div className="grid gap-4 py-4">
					<div className="grid gap-2">
						<Label htmlFor="confirmUrl" className="text-left">
							To confirm, type the website URL below:
						</Label>
						<div className="flex items-center gap-2">
							<Input
								id="confirmUrl"
								value={confirmUrl}
								onChange={(e) => setConfirmUrl(e.target.value)}
								placeholder={websiteUrl}
								className={
									isConfirmDisabled && confirmUrl ? "border-destructive" : ""
								}
								autoComplete="off"
							/>
						</div>
						{isConfirmDisabled && confirmUrl && (
							<p className="text-xs text-destructive mt-1">
								The URL doesn&apos;t match. Please enter the exact URL.
							</p>
						)}
					</div>
				</div>
				<DialogFooter>
					<Button variant="outline" onClick={onClose} disabled={isDeleting}>
						Cancel
					</Button>
					<Button
						variant="destructive"
						onClick={onConfirm}
						disabled={isConfirmDisabled || isDeleting}
						className="gap-2"
					>
						{isDeleting ? (
							<>
								<span className="animate-spin">⏳</span>
								Deleting...
							</>
						) : (
							"Delete Website"
						)}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
