"use client";

import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import type { LucideIcon } from "lucide-react";

interface StatsCardProps {
	title: string;
	value: string | number;
	description?: string;
	icon: LucideIcon;
	trend?: {
		value: number;
		isPositive: boolean;
	};
	className?: string;
}

export function StatsCard({
	title,
	value,
	description,
	icon: Icon,
	trend,
	className,
}: StatsCardProps) {
	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
		>
			<Card
				className={cn(
					"relative overflow-hidden border-border/40 bg-card p-6 shadow-sm hover:shadow-md transition-shadow duration-200",
					className,
				)}
			>
				<div className="flex items-center justify-between">
					<div>
						<p className="text-sm font-medium text-muted-foreground">{title}</p>
						<motion.div
							initial={{ opacity: 0, scale: 0.95 }}
							animate={{ opacity: 1, scale: 1 }}
							transition={{ delay: 0.1, duration: 0.3 }}
						>
							<h2 className="mt-2 text-3xl font-bold text-card-foreground">
								{value}
							</h2>
						</motion.div>
						{description && (
							<p className="mt-2 text-xs text-muted-foreground">
								{description}
							</p>
						)}
						{trend && (
							<div className="mt-2 flex items-center">
								<span
									className={cn(
										"inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium",
										trend.isPositive
											? "bg-primary/10 text-primary"
											: "bg-destructive/10 text-destructive",
									)}
								>
									{trend.isPositive ? "+" : ""}
									{trend.value}%
								</span>
							</div>
						)}
					</div>
					<div
						className={cn(
							"flex h-12 w-12 items-center justify-center rounded-lg",
							"bg-primary/10 text-primary",
						)}
					>
						<Icon className="h-5 w-5" />
					</div>
				</div>
				<div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-primary/5 via-primary/20 to-primary/5" />
			</Card>
		</motion.div>
	);
}
