"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Pagination,
	PaginationContent,
	PaginationEllipsis,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from "@/components/ui/pagination";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { useCsrfToken } from "@/hooks/useCsrfToken";
import { format } from "date-fns";
import { AlertCircle, CheckCircle, Loader2, RefreshCw } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "sonner";

interface CrawlOperation {
	id: string;
	status: string;
	startedAt?: string | null;
	completedAt?: string | null;
	pagesProcessed?: number | null;
	pagesSucceeded?: number | null;
	pagesFailed?: number | null;
	error?: string | null;
	createdAt: string;
	websiteId: string;
	configuration?: unknown;
	updatedAt: string;
}

interface PaginationData {
	page: number;
	pageSize: number;
	total: number;
	pageCount: number;
}

interface CrawlStatusListProps {
	websiteId: string;
}

export function CrawlStatusList({ websiteId }: CrawlStatusListProps) {
	const [operations, setOperations] = useState<CrawlOperation[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const { getRequestOptions } = useCsrfToken();
	const [hasActiveCrawl, setHasActiveCrawl] = useState(false);
	const [pagination, setPagination] = useState<PaginationData>({
		page: 1,
		pageSize: 10,
		total: 0,
		pageCount: 0,
	});

	// Use a ref to track loading state without causing re-renders
	const isLoadingRef = useRef(false);

	// Store the fetchOperations function in a ref to avoid stale closures in effects
	const fetchOperationsRef = useRef<() => Promise<void>>(async () => {});

	// Function to handle page change
	const handlePageChange = (newPage: number) => {
		setPagination((prev) => ({ ...prev, page: newPage }));
		// Use the ref to call the latest version of fetchOperations
		fetchOperationsRef.current();
	};

	// Function to fetch operations - defined once and stored in ref
	const fetchOperations = useCallback(async () => {
		// Prevent concurrent fetches
		if (isLoadingRef.current) return;

		isLoadingRef.current = true;
		setIsLoading(true);
		setError(null);

		try {
			// Add pagination parameters to the URL
			const url = new URL(
				`/api/dashboard/websites/${websiteId}/crawl/operations`,
				window.location.origin,
			);
			url.searchParams.append("page", pagination.page.toString());
			url.searchParams.append("pageSize", pagination.pageSize.toString());

			const response = await fetch(
				url.toString(),
				getRequestOptions({
					method: "GET",
				}),
			);

			if (!response.ok) {
				throw new Error("Failed to fetch crawl operations");
			}

			const data = await response.json();
			setOperations(data.operations || []);

			// Update pagination data if available
			if (data.pagination) {
				setPagination(data.pagination);
			}

			// Check if there are any active crawls
			const active = (data.operations || []).some(
				(op: CrawlOperation) =>
					op.status === "RUNNING" || op.status === "PENDING",
			);
			setHasActiveCrawl(active);
		} catch (err) {
			setError(err instanceof Error ? err.message : "An error occurred");
			toast.error("Failed to fetch crawl operations");
		} finally {
			setIsLoading(false);
			isLoadingRef.current = false;
		}
	}, [websiteId, getRequestOptions, pagination.page, pagination.pageSize]);

	// Update the ref whenever fetchOperations changes
	useEffect(() => {
		fetchOperationsRef.current = fetchOperations;
	}, [fetchOperations]);

	// Initial fetch - only run once when component mounts
	useEffect(() => {
		// Define a function to perform the initial fetch
		const performInitialFetch = async () => {
			// Use the ref to get the latest fetchOperations function
			await fetchOperationsRef.current();
		};

		// Call it immediately
		performInitialFetch();

		// No dependencies needed as we're using the ref
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	// Set up polling if there are active crawls
	// useEffect(() => {
	// 	// Only set up polling if there are active crawls
	// 	if (!hasActiveCrawl) return;

	// 	// Use the ref to always get the latest fetchOperations function
	// 	const intervalId = setInterval(() => {
	// 		// This will always use the latest version of fetchOperations
	// 		fetchOperationsRef.current();
	// 	}, 10000);

	// 	// Clean up the interval when the component unmounts or hasActiveCrawl changes
	// 	return () => clearInterval(intervalId);
	// }, [hasActiveCrawl]);

	const getStatusBadge = (status: string) => {
		switch (status) {
			case "PENDING":
				return <Badge variant="outline">Pending</Badge>;
			case "RUNNING":
				return <Badge variant="secondary">Running</Badge>;
			case "COMPLETED":
				return (
					<Badge className="bg-green-500 hover:bg-green-600">Completed</Badge>
				);
			case "FAILED":
				return <Badge variant="destructive">Failed</Badge>;
			default:
				return <Badge variant="outline">{status}</Badge>;
		}
	};

	const getStatusIcon = (status: string) => {
		switch (status) {
			case "PENDING":
				return <Loader2 className="h-4 w-4 text-muted-foreground" />;
			case "RUNNING":
				return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
			case "COMPLETED":
				return <CheckCircle className="h-4 w-4 text-green-500" />;
			case "FAILED":
				return <AlertCircle className="h-4 w-4 text-red-500" />;
			default:
				return null;
		}
	};

	return (
		<Card>
			<CardHeader>
				<div className="flex justify-between items-center">
					<div>
						<CardTitle>Crawl Operations</CardTitle>
						<CardDescription>
							View the status and history of crawl operations
							{hasActiveCrawl && " (Auto-refreshing)"}
						</CardDescription>
					</div>
					<Button
						variant="outline"
						size="sm"
						onClick={() => fetchOperations()}
						disabled={isLoading}
					>
						<RefreshCw
							className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
						/>
						Refresh
					</Button>
				</div>
			</CardHeader>
			<CardContent>
				{isLoading && operations.length === 0 ? (
					<div className="flex justify-center items-center py-8">
						<Loader2 className="h-8 w-8 animate-spin text-primary" />
					</div>
				) : error ? (
					<div className="text-center py-8 text-red-500">{error}</div>
				) : operations.length === 0 ? (
					<div className="text-center py-8 text-muted-foreground">
						No crawl operations found
					</div>
				) : (
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>Status</TableHead>
								<TableHead>Started</TableHead>
								<TableHead>Completed</TableHead>
								<TableHead>Pages</TableHead>
								<TableHead>Success</TableHead>
								<TableHead>Failed</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{operations.map((operation) => (
								<TableRow key={operation.id}>
									<TableCell>
										<div className="flex items-center gap-2">
											{getStatusIcon(operation.status)}
											{getStatusBadge(operation.status)}
										</div>
									</TableCell>
									<TableCell>
										{operation.startedAt &&
										!Number.isNaN(new Date(operation.startedAt).getTime())
											? format(
													new Date(operation.startedAt),
													"MMM d, yyyy HH:mm",
												)
											: "-"}
									</TableCell>
									<TableCell>
										{operation.completedAt &&
										!Number.isNaN(new Date(operation.completedAt).getTime())
											? format(
													new Date(operation.completedAt),
													"MMM d, yyyy HH:mm",
												)
											: "-"}
									</TableCell>
									<TableCell>{operation.pagesProcessed || 0}</TableCell>
									<TableCell>{operation.pagesSucceeded || 0}</TableCell>
									<TableCell>{operation.pagesFailed || 0}</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				)}

				{/* Pagination */}
				{operations.length > 0 && pagination.pageCount > 1 && (
					<div className="mt-4 flex justify-center">
						<Pagination>
							<PaginationContent>
								{pagination.page > 1 && (
									<PaginationItem>
										<PaginationPrevious
											href="#"
											onClick={(e) => {
												e.preventDefault();
												handlePageChange(pagination.page - 1);
											}}
										/>
									</PaginationItem>
								)}

								{/* First page */}
								{pagination.page > 2 && (
									<PaginationItem>
										<PaginationLink
											href="#"
											onClick={(e) => {
												e.preventDefault();
												handlePageChange(1);
											}}
										>
											1
										</PaginationLink>
									</PaginationItem>
								)}

								{/* Ellipsis if needed */}
								{pagination.page > 3 && (
									<PaginationItem>
										<PaginationEllipsis />
									</PaginationItem>
								)}

								{/* Previous page if not first */}
								{pagination.page > 1 && (
									<PaginationItem>
										<PaginationLink
											href="#"
											onClick={(e) => {
												e.preventDefault();
												handlePageChange(pagination.page - 1);
											}}
										>
											{pagination.page - 1}
										</PaginationLink>
									</PaginationItem>
								)}

								{/* Current page */}
								<PaginationItem>
									<PaginationLink href="#" isActive>
										{pagination.page}
									</PaginationLink>
								</PaginationItem>

								{/* Next page if not last */}
								{pagination.page < pagination.pageCount && (
									<PaginationItem>
										<PaginationLink
											href="#"
											onClick={(e) => {
												e.preventDefault();
												handlePageChange(pagination.page + 1);
											}}
										>
											{pagination.page + 1}
										</PaginationLink>
									</PaginationItem>
								)}

								{/* Ellipsis if needed */}
								{pagination.page < pagination.pageCount - 2 && (
									<PaginationItem>
										<PaginationEllipsis />
									</PaginationItem>
								)}

								{/* Last page if not current or next */}
								{pagination.page < pagination.pageCount - 1 && (
									<PaginationItem>
										<PaginationLink
											href="#"
											onClick={(e) => {
												e.preventDefault();
												handlePageChange(pagination.pageCount);
											}}
										>
											{pagination.pageCount}
										</PaginationLink>
									</PaginationItem>
								)}

								{pagination.page < pagination.pageCount && (
									<PaginationItem>
										<PaginationNext
											href="#"
											onClick={(e) => {
												e.preventDefault();
												handlePageChange(pagination.page + 1);
											}}
										/>
									</PaginationItem>
								)}
							</PaginationContent>
						</Pagination>
					</div>
				)}
			</CardContent>
		</Card>
	);
}
