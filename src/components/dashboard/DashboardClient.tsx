"use client";

import { PlanInfo } from "@/components/dashboard/PlanInfo";
import { PlanUsage } from "@/components/dashboard/PlanUsage";
import { ErrorState, LoadingState } from "@/components/ui";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { useWebsites } from "@/hooks";
import type { Website } from "@/lib/api/types";
import { PlusIcon, Settings } from "lucide-react";
import Link from "next/link";
import {
	Bar,
	BarChart,
	CartesianGrid,
	ResponsiveContainer,
	Tooltip,
	XAxis,
	YAxis,
} from "recharts";

interface DashboardClientProps {
	initialData?: Website[];
}

export function DashboardClient({ initialData }: DashboardClientProps = {}) {
	const { data: websites, isLoading, isError, error } = useWebsites();

	// Prepare data for the chart
	const chartData =
		websites?.map((website) => ({
			name: website.name,
			conversations: website.conversationCount || 0,
		})) || [];

	// Calculate totals
	const totalWebsites = websites?.length || 0;
	const totalConversations =
		websites?.reduce(
			(sum, website) => sum + (website.conversationCount || 0),
			0,
		) || 0;

	return (
		<div className="space-y-8">
			{isLoading ? (
				<LoadingState message="Loading dashboard data..." />
			) : isError ? (
				<ErrorState
					message={error?.message || "Failed to load dashboard data"}
				/>
			) : (
				<>
					<div className="mb-8">
						<h1 className="text-3xl font-bold text-foreground mb-2">
							Dashboard
						</h1>
						<p className="text-muted-foreground">
							Welcome to your Bubl dashboard
						</p>
					</div>

					<div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
						<Card className="border-border/40 shadow-sm hover:shadow-md transition-shadow duration-200">
							<CardHeader className="pb-2">
								<CardTitle className="text-sm font-medium text-muted-foreground">
									Website Status
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="text-3xl font-bold text-card-foreground">
									{totalWebsites > 0 ? "Active" : "Not Set Up"}
								</div>
								<div className="mt-4">
									<Link href="/dashboard/websites">
										<Button
											variant="outline"
											size="sm"
											className="rounded-full border-primary/20 bg-primary/5 text-primary hover:bg-primary/10"
										>
											{totalWebsites > 0 ? (
												<>
													<Settings className="mr-2 h-4 w-4" />
													Manage Website
												</>
											) : (
												<>
													<PlusIcon className="mr-2 h-4 w-4" />
													Add Website
												</>
											)}
										</Button>
									</Link>
								</div>
							</CardContent>
						</Card>

						<Card className="border-border/40 shadow-sm hover:shadow-md transition-shadow duration-200">
							<CardHeader className="pb-2">
								<CardTitle className="text-sm font-medium text-muted-foreground">
									Total Conversations
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="text-3xl font-bold text-card-foreground">
									{totalConversations}
								</div>
								<p className="mt-2 text-xs text-muted-foreground">
									Across all websites
								</p>
							</CardContent>
						</Card>

						<Card className="border-border/40 shadow-sm hover:shadow-md transition-shadow duration-200">
							<CardHeader className="pb-2">
								<CardTitle className="text-sm font-medium text-muted-foreground">
									Active Websites
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="text-3xl font-bold text-card-foreground">
									{websites?.filter((w) => w.status === "ACTIVE").length || 0}
								</div>
								<p className="mt-2 text-xs text-muted-foreground">
									Ready to receive visitors
								</p>
							</CardContent>
						</Card>

						{/* Plan Info Card */}
						<PlanInfo />
					</div>

					{/* Plan Usage Card */}
					<div className="mt-8 grid grid-cols-1 gap-6 md:grid-cols-2">
						<PlanUsage />

						{chartData.length > 0 ? (
							<Card className="border-border/40 shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden">
								<CardHeader>
									<CardTitle className="text-card-foreground">
										Conversations by Website
									</CardTitle>
									<CardDescription className="text-muted-foreground">
										Number of conversations for each of your websites
									</CardDescription>
								</CardHeader>
								<CardContent>
									<div className="h-80">
										<ResponsiveContainer width="100%" height="100%">
											<BarChart data={chartData}>
												<CartesianGrid
													strokeDasharray="3 3"
													stroke="var(--border)"
												/>
												<XAxis
													dataKey="name"
													stroke="var(--muted-foreground)"
												/>
												<YAxis stroke="var(--muted-foreground)" />
												<Tooltip
													contentStyle={{
														backgroundColor: "var(--card)",
														border: "1px solid var(--border)",
														borderRadius: "var(--radius)",
														color: "var(--card-foreground)",
													}}
												/>
												<Bar
													dataKey="conversations"
													fill="var(--primary)"
													radius={[6, 6, 0, 0]}
												/>
											</BarChart>
										</ResponsiveContainer>
									</div>
								</CardContent>
							</Card>
						) : (
							<Card className="border-border/40 shadow-sm hover:shadow-md transition-shadow duration-200">
								<CardHeader>
									<CardTitle className="text-card-foreground">
										No Data Available
									</CardTitle>
									<CardDescription className="text-muted-foreground">
										Add websites and generate conversations to see analytics
									</CardDescription>
								</CardHeader>
								<CardContent>
									<p className="text-muted-foreground mb-4">
										To get started, add your first website and set up the chat
										widget.
									</p>
									<Link href="/dashboard/websites">
										<Button className="rounded-full">
											<PlusIcon className="mr-2 h-4 w-4" />
											Add Your First Website
										</Button>
									</Link>
								</CardContent>
							</Card>
						)}
					</div>
				</>
			)}
		</div>
	);
}
