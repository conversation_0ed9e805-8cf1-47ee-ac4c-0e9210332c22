"use client";

import { ErrorState, LoadingState } from "@/components/ui";
import { useConversation } from "@/hooks/useWebsiteConversations";
import { formatDistanceToNow } from "date-fns";
import { <PERSON><PERSON>, User } from "lucide-react";

interface ConversationDetailProps {
	conversationId: string;
}

export function ConversationDetail({
	conversationId,
}: ConversationDetailProps) {
	const { data, isLoading, isError, error } = useConversation(conversationId);

	if (isLoading) {
		return <LoadingState message="Loading conversation..." />;
	}

	if (isError) {
		return (
			<ErrorState
				message={error?.message || "Failed to load conversation"}
				retry={() => window.location.reload()}
			/>
		);
	}

	if (!data || !data.conversation || !data.messages) {
		return <div>No conversation data found</div>;
	}

	const { conversation, messages } = data;

	return (
		<div className="space-y-4">
			<div className="bg-muted/50 p-4 rounded-lg">
				<div className="grid grid-cols-2 gap-4">
					<div>
						<p className="text-sm font-medium text-muted-foreground">
							Visitor ID
						</p>
						<p className="text-sm">{conversation.visitorId}</p>
					</div>
					<div>
						<p className="text-sm font-medium text-muted-foreground">Status</p>
						<p className="text-sm">{conversation.status}</p>
					</div>
					<div>
						<p className="text-sm font-medium text-muted-foreground">Started</p>
						<p className="text-sm">
							{formatDistanceToNow(new Date(conversation.createdAt), {
								addSuffix: true,
							})}
						</p>
					</div>
					<div>
						<p className="text-sm font-medium text-muted-foreground">
							Referring URL
						</p>
						<p className="text-sm">{conversation.referringUrl || "Direct"}</p>
					</div>
					{conversation.rating && (
						<div>
							<p className="text-sm font-medium text-muted-foreground">
								Rating
							</p>
							<p className="text-sm">{conversation.rating}/5</p>
						</div>
					)}
					{conversation.feedback && (
						<div className="col-span-2">
							<p className="text-sm font-medium text-muted-foreground">
								Feedback
							</p>
							<p className="text-sm">{conversation.feedback}</p>
						</div>
					)}
				</div>
			</div>

			<div className="space-y-4">
				<h3 className="text-lg font-medium">Conversation</h3>
				<div className="space-y-4">
					{messages.map((message) => (
						<div
							key={message.id}
							className={`flex ${
								message.role === "user" ? "justify-end" : "justify-start"
							}`}
						>
							<div
								className={`flex max-w-[80%] items-start gap-2 rounded-lg p-4 ${
									message.role === "user"
										? "bg-primary text-primary-foreground"
										: "bg-muted"
								}`}
							>
								<div className="mt-0.5">
									{message.role === "user" ? (
										<User className="h-5 w-5" />
									) : (
										<Bot className="h-5 w-5" />
									)}
								</div>
								<div className="space-y-1">
									<div className="text-sm">{message.content}</div>
									<div className="text-xs opacity-70">
										{formatDistanceToNow(new Date(message.createdAt), {
											addSuffix: true,
										})}
									</div>
									{message.sources &&
										Object.keys(message.sources).length > 0 && (
											<div className="mt-2 text-xs">
												<p className="font-medium">Sources:</p>
												<ul className="list-disc list-inside">
													{Array.isArray(message.sources)
														? message.sources.map(
																(
																	source: {
																		url?: string;
																		title?: string;
																		[key: string]: unknown;
																	},
																	sourceIndex: number,
																) => (
																	<li
																		key={`source-${message.id}-${sourceIndex}`}
																	>
																		{source.url ||
																			source.title ||
																			JSON.stringify(source)}
																	</li>
																),
															)
														: Object.entries(message.sources).map(
																(
																	[key, value]: [
																		string,
																		{
																			url?: string;
																			title?: string;
																			[key: string]: unknown;
																		},
																	],
																	entryIndex: number,
																) => (
																	<li
																		key={`source-${message.id}-${key}-${entryIndex}`}
																	>
																		{value.url ||
																			value.title ||
																			JSON.stringify(value)}
																	</li>
																),
															)}
												</ul>
											</div>
										)}
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
}
