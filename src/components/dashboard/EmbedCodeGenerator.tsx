"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Card<PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { CheckIcon, CopyIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface EmbedCodeGeneratorProps {
	websiteId: string;
}

export function EmbedCodeGenerator({ websiteId }: EmbedCodeGeneratorProps) {
	const [isLoading, setIsLoading] = useState(false);
	const [embedCode, setEmbedCode] = useState("");
	const [copied, setCopied] = useState(false);

	// Fetch the embed code
	useEffect(() => {
		const fetchEmbedCode = async () => {
			try {
				setIsLoading(true);
				console.log("Fetching embed code for website:", websiteId);
				const response = await fetch(
					`/api/dashboard/websites/${websiteId}/embed-code`,
				);

				// Clone the response before reading it
				const responseClone = response.clone();

				if (!response.ok) {
					console.error(
						"Failed to fetch embed code:",
						response.status,
						response.statusText,
					);
					try {
						const errorText = await responseClone.text();
						console.error("Error response:", errorText);
					} catch (e) {
						console.error("Error reading error response:", e);
					}
					throw new Error(
						`Failed to fetch embed code: ${response.status} ${response.statusText}`,
					);
				}

				// Define a more specific type for the response data
				let responseData: {
					success?: boolean;
					data?: { embedCode?: string };
					embedCode?: string;
				};
				try {
					responseData = await response.json();
					console.log("Received embed code response:", responseData);
				} catch (e) {
					console.error("Error parsing response:", e);
					throw new Error("Failed to parse server response");
				}

				// Log the full response for debugging
				console.log("Full embed code response:", JSON.stringify(responseData));

				// Handle both response formats
				if (responseData?.success === true && responseData.data) {
					// Format: { success: true, data: { embedCode: string } }
					const { embedCode } = responseData.data;
					if (embedCode) {
						setEmbedCode(embedCode);
					} else {
						console.error(
							"No embed code found in response data:",
							responseData.data,
						);
						toast.error(
							"Failed to load embed code: Missing embed code in response",
						);
					}
				} else if (responseData?.embedCode) {
					// Format: { embedCode: string }
					setEmbedCode(responseData.embedCode);
				} else {
					console.error("Invalid response format:", responseData);
					toast.error("Failed to load embed code: Invalid response format");
				}
			} catch (error) {
				console.error("Error fetching embed code:", error);
				toast.error("Failed to load embed code");
			} finally {
				setIsLoading(false);
			}
		};

		if (websiteId) {
			fetchEmbedCode();
		}
	}, [websiteId]);

	const handleCopyCode = async () => {
		try {
			await navigator.clipboard.writeText(embedCode);
			setCopied(true);
			toast.success("Embed code copied to clipboard");

			// Reset the copied state after 2 seconds
			setTimeout(() => {
				setCopied(false);
			}, 2000);
		} catch (error) {
			console.error("Error copying to clipboard:", error);
			toast.error("Failed to copy embed code");
		}
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle>Embed Code</CardTitle>
			</CardHeader>
			<CardContent>
				{isLoading ? (
					<div className="h-40 flex items-center justify-center">
						<p className="text-muted-foreground">Loading embed code...</p>
					</div>
				) : embedCode ? (
					<div className="relative">
						<pre className="p-4 bg-muted rounded-md overflow-x-auto text-sm font-mono whitespace-pre-wrap break-all">
							{embedCode}
						</pre>
					</div>
				) : (
					<div className="h-40 flex items-center justify-center flex-col gap-2">
						<p className="text-muted-foreground">
							No embed code available yet.
						</p>
						<p className="text-xs text-muted-foreground">
							Save your chat configuration first to generate the embed code.
						</p>
					</div>
				)}
			</CardContent>
			<CardFooter>
				<Button
					onClick={handleCopyCode}
					disabled={isLoading || !embedCode}
					className="w-full"
				>
					{copied ? (
						<>
							<CheckIcon className="mr-2 h-4 w-4" />
							Copied!
						</>
					) : (
						<>
							<CopyIcon className="mr-2 h-4 w-4" />
							Copy Embed Code
						</>
					)}
				</Button>
			</CardFooter>
		</Card>
	);
}
