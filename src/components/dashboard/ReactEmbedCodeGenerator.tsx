"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { CheckIcon, CopyIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface ReactEmbedCodeGeneratorProps {
	websiteId: string;
	chatConfig?: {
		primaryColor?: string;
		secondaryColor?: string;
		position?: string;
		welcomeMessage?: string;
		headerText?: string;
	};
}

export function ReactEmbedCodeGenerator({
	websiteId,
	chatConfig,
}: ReactEmbedCodeGeneratorProps) {
	const [isLoading, setIsLoading] = useState(false);
	const [copied, setCopied] = useState(false);
	const [activeTab, setActiveTab] = useState("npm");

	// Generate the React component usage example
	const generateReactComponentCode = () => {
		const position =
			chatConfig?.position?.toLowerCase().replace("_", "-") || "bottom-right";

		return `import { BublChat } from "@bubl/react";

function App() {
  return (
    <div>
      {/* Your website content */}
      
      <BublChat 
        websiteId="${websiteId}"
        primaryColor="${chatConfig?.primaryColor || "#4F46E5"}"
        secondaryColor="${chatConfig?.secondaryColor || "#FFFFFF"}"
        position="${position}"
        welcomeMessage="${(chatConfig?.welcomeMessage || "Hi there! How can I help you today?").replace(/"/g, '\\"')}"
        headerText="${(chatConfig?.headerText || "Chat Assistant").replace(/"/g, '\\"')}"
        initiallyOpen={false}
        onReady={() => console.log("Bubl widget is ready")}
      />
    </div>
  );
}`;
	};

	// Generate the installation instructions
	const generateInstallCode = (packageManager: string) => {
		switch (packageManager) {
			case "npm":
				return "npm install @bubl/react";
			case "yarn":
				return "yarn add @bubl/react";
			case "pnpm":
				return "pnpm add @bubl/react";
			default:
				return "npm install @bubl/react";
		}
	};

	const handleCopyCode = async () => {
		try {
			await navigator.clipboard.writeText(generateReactComponentCode());
			setCopied(true);
			toast.success("React component code copied to clipboard");

			// Reset the copied state after 2 seconds
			setTimeout(() => {
				setCopied(false);
			}, 2000);
		} catch (error) {
			console.error("Error copying to clipboard:", error);
			toast.error("Failed to copy code");
		}
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle>React Component</CardTitle>
			</CardHeader>
			<CardContent>
				<Tabs defaultValue="npm" value={activeTab} onValueChange={setActiveTab}>
					<TabsList className="mb-4">
						<TabsTrigger value="npm">npm</TabsTrigger>
						<TabsTrigger value="yarn">yarn</TabsTrigger>
						<TabsTrigger value="pnpm">pnpm</TabsTrigger>
						<TabsTrigger value="component">Component</TabsTrigger>
					</TabsList>

					<TabsContent value="npm" className="mt-0">
						<div className="relative">
							<pre className="p-4 bg-muted rounded-md overflow-x-auto text-sm font-mono">
								{generateInstallCode("npm")}
							</pre>
						</div>
					</TabsContent>

					<TabsContent value="yarn" className="mt-0">
						<div className="relative">
							<pre className="p-4 bg-muted rounded-md overflow-x-auto text-sm font-mono">
								{generateInstallCode("yarn")}
							</pre>
						</div>
					</TabsContent>

					<TabsContent value="pnpm" className="mt-0">
						<div className="relative">
							<pre className="p-4 bg-muted rounded-md overflow-x-auto text-sm font-mono">
								{generateInstallCode("pnpm")}
							</pre>
						</div>
					</TabsContent>

					<TabsContent value="component" className="mt-0">
						<div className="relative">
							<pre className="p-4 bg-muted rounded-md overflow-x-auto text-sm font-mono whitespace-pre-wrap break-all">
								{generateReactComponentCode()}
							</pre>
						</div>
					</TabsContent>
				</Tabs>
			</CardContent>
			<CardFooter>
				<Button
					onClick={handleCopyCode}
					disabled={isLoading || activeTab !== "component"}
					className="w-full"
				>
					{copied ? (
						<>
							<CheckIcon className="mr-2 h-4 w-4" />
							Copied!
						</>
					) : (
						<>
							<CopyIcon className="mr-2 h-4 w-4" />
							Copy Component Code
						</>
					)}
				</Button>
			</CardFooter>
		</Card>
	);
}
