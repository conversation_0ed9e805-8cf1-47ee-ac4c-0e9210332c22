"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON>,
	<PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Card<PERSON>itle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { RefreshCw } from "lucide-react";
import Link from "next/link";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";

import { useCsrfToken } from "@/hooks/useCsrfToken";
import type { Plan } from "@/hooks/useUserPlan";

interface CrawlConfigurationFormProps {
	websiteId: string;
	userPlan?: Plan;
	onCrawlStart?: () => void;
	onCrawlComplete?: () => void;
}

export interface CrawlConfig {
	maxPages: number;
	maxDepth: number;
	includePatterns: string[];
	excludePatterns: string[];
	generateEmbeddings: boolean;
	crawlFrequency: string;
	respectRobotsTxt: boolean;
	crawlDelay: number;
	incrementalCrawl: boolean; // Whether to only process new or changed content
}

// Default configuration
export const defaultConfig: CrawlConfig = {
	maxPages: 100, // This will be overridden in the component based on plan
	maxDepth: 3,
	includePatterns: [],
	excludePatterns: [
		"/wp-admin",
		"/wp-login",
		"/login",
		"/admin",
		"/logout",
		"/cart",
		"/checkout",
		".pdf",
		".jpg",
		".png",
		".gif",
		".zip",
		".doc",
		".xls",
	],
	generateEmbeddings: true,
	crawlFrequency: "WEEKLY",
	respectRobotsTxt: true,
	crawlDelay: 1000,
	incrementalCrawl: true, // Default to true for incremental crawling
};

export function CrawlConfigurationForm({
	websiteId,
	userPlan,
	onCrawlStart,
	onCrawlComplete,
}: CrawlConfigurationFormProps) {
	const [isLoading, setIsLoading] = useState(false);
	const { getRequestOptions } = useCsrfToken();

	// Get the page limit from the user's plan
	const pagesPerWebsiteLimit = userPlan?.pagesPerWebsiteLimit || 50;

	// Set default max pages based on the user's plan
	// Use the minimum of 100 or the user's plan limit
	const defaultMaxPages = Math.min(100, pagesPerWebsiteLimit);

	// Use defaultConfig as a base, but override maxPages
	const initialConfig = useMemo(
		() => ({
			...defaultConfig,
			maxPages: defaultMaxPages,
		}),
		[defaultMaxPages],
	);

	const [config, setConfig] = useState<CrawlConfig>(initialConfig);
	const [isLoadingConfig, setIsLoadingConfig] = useState(true);

	// Fetch saved configuration on component mount
	useEffect(() => {
		const fetchSavedConfig = async () => {
			try {
				setIsLoadingConfig(true);
				const response = await fetch(
					`/api/dashboard/websites/${websiteId}/config`,
				);

				if (response.ok) {
					const data = await response.json();
					if (data.crawlConfiguration) {
						// Merge saved configuration with default values
						setConfig({
							...initialConfig,
							...data.crawlConfiguration,
							// Ensure maxPages doesn't exceed the plan limit
							maxPages: Math.min(
								data.crawlConfiguration.maxPages || defaultMaxPages,
								pagesPerWebsiteLimit,
							),
						});
					}
				}
			} catch (error) {
				console.error("Error fetching saved configuration:", error);
				// If there's an error, we'll use the default configuration
			} finally {
				setIsLoadingConfig(false);
			}
		};

		fetchSavedConfig();
	}, [websiteId, defaultMaxPages, pagesPerWebsiteLimit, initialConfig]);

	// Handle input changes
	const handleInputChange = (field: keyof CrawlConfig, value: unknown) => {
		setConfig((prev) => ({ ...prev, [field]: value }));
	};

	// Handle pattern changes (include/exclude)
	const handlePatternChange = (
		field: "includePatterns" | "excludePatterns",
		value: string,
	) => {
		// Convert the textarea value to an array of patterns
		const patterns = value
			.split("\n")
			.map((pattern) => pattern.trim())
			.filter((pattern) => pattern.length > 0);

		setConfig((prev) => ({ ...prev, [field]: patterns }));
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		try {
			setIsLoading(true);

			// Notify parent component that crawl is starting
			if (onCrawlStart) {
				onCrawlStart();
			}

			const response = await fetch(
				`/api/dashboard/websites/${websiteId}/crawl`,
				getRequestOptions({
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify(config),
				}),
			);

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to start crawling");
			}

			toast.success("Website crawling started", {
				description: "This process may take a few minutes to complete.",
			});

			// Notify parent component that crawl is complete
			if (onCrawlComplete) {
				onCrawlComplete();
			}
		} catch (err) {
			toast.error("Failed to start crawling", {
				description:
					err instanceof Error ? err.message : "An unknown error occurred",
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle>Crawl Configuration</CardTitle>
			</CardHeader>
			<form onSubmit={handleSubmit}>
				<CardContent className="space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="space-y-2">
							<Label htmlFor="maxPages">Maximum Pages</Label>
							<div className="relative">
								<Input
									id="maxPages"
									type="number"
									min="1"
									max={pagesPerWebsiteLimit.toString()}
									value={config.maxPages}
									onChange={(e) => {
										const value = Number.parseInt(e.target.value);
										// Ensure the value doesn't exceed the plan limit
										const limitedValue = Math.min(value, pagesPerWebsiteLimit);
										handleInputChange("maxPages", limitedValue);
									}}
									disabled={isLoading}
									className={
										config.maxPages >= pagesPerWebsiteLimit
											? "border-amber-500 pr-10"
											: ""
									}
								/>
								{config.maxPages >= pagesPerWebsiteLimit && (
									<div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
										<span className="text-amber-500 text-sm">Limit</span>
									</div>
								)}
							</div>
							<div className="flex justify-between">
								<p className="text-xs text-muted-foreground">
									Maximum number of pages to crawl
								</p>
								<p className="text-xs text-muted-foreground">
									Plan limit: {pagesPerWebsiteLimit} pages
								</p>
							</div>
							{config.maxPages >= pagesPerWebsiteLimit && (
								<p className="text-xs text-amber-500">
									You've reached your plan's page limit.
									<Link href="/dashboard/plans" className="ml-1 underline">
										Upgrade your plan
									</Link>{" "}
									for more pages.
								</p>
							)}
						</div>

						<div className="space-y-2">
							<Label htmlFor="maxDepth">Maximum Depth</Label>
							<Input
								id="maxDepth"
								type="number"
								min="1"
								max="10"
								value={config.maxDepth}
								onChange={(e) =>
									handleInputChange("maxDepth", Number.parseInt(e.target.value))
								}
								disabled={isLoading}
							/>
							<p className="text-xs text-muted-foreground">
								Maximum link depth to crawl (1-10)
							</p>
						</div>
					</div>

					<div className="space-y-2">
						<Label htmlFor="crawlFrequency">Crawl Frequency</Label>
						<Select
							value={config.crawlFrequency}
							onValueChange={(value) =>
								handleInputChange("crawlFrequency", value)
							}
							disabled={isLoading}
						>
							<SelectTrigger id="crawlFrequency">
								<SelectValue placeholder="Select frequency" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="DAILY">Daily</SelectItem>
								<SelectItem value="WEEKLY">Weekly</SelectItem>
								<SelectItem value="MONTHLY">Monthly</SelectItem>
								<SelectItem value="MANUAL">Manual Only</SelectItem>
							</SelectContent>
						</Select>
						<p className="text-xs text-muted-foreground">
							How often the website should be automatically crawled
						</p>
					</div>

					<div className="space-y-2">
						<Label htmlFor="includePatterns">Include Patterns (Optional)</Label>
						<Textarea
							id="includePatterns"
							placeholder="/blog\n/products"
							value={config.includePatterns.join("\n")}
							onChange={(e) =>
								handlePatternChange("includePatterns", e.target.value)
							}
							disabled={isLoading}
							className="min-h-[80px]"
						/>
						<p className="text-xs text-muted-foreground">
							Only crawl URLs containing these patterns (one per line). Leave
							empty to include all.
						</p>
					</div>

					<div className="space-y-2">
						<Label htmlFor="excludePatterns">Exclude Patterns</Label>
						<Textarea
							id="excludePatterns"
							placeholder="/admin\n/login\n.pdf"
							value={config.excludePatterns.join("\n")}
							onChange={(e) =>
								handlePatternChange("excludePatterns", e.target.value)
							}
							disabled={isLoading}
							className="min-h-[80px]"
						/>
						<p className="text-xs text-muted-foreground">
							Skip URLs containing these patterns (one per line)
						</p>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="space-y-2">
							<Label htmlFor="crawlDelay">Crawl Delay (ms)</Label>
							<Input
								id="crawlDelay"
								type="number"
								min="0"
								max="10000"
								value={config.crawlDelay}
								onChange={(e) =>
									handleInputChange(
										"crawlDelay",
										Number.parseInt(e.target.value),
									)
								}
								disabled={isLoading}
							/>
							<p className="text-xs text-muted-foreground">
								Delay between requests in milliseconds (0-10000)
							</p>
						</div>
					</div>

					<div className="flex items-center space-x-2">
						<Switch
							id="generateEmbeddings"
							checked={config.generateEmbeddings}
							onCheckedChange={(checked) =>
								handleInputChange("generateEmbeddings", checked)
							}
							disabled={isLoading}
						/>
						<Label htmlFor="generateEmbeddings">Generate Embeddings</Label>
						<p className="text-xs text-muted-foreground ml-2">
							Create vector embeddings for search capabilities
						</p>
					</div>

					<div className="flex items-center space-x-2">
						<Switch
							id="respectRobotsTxt"
							checked={config.respectRobotsTxt}
							onCheckedChange={(checked) =>
								handleInputChange("respectRobotsTxt", checked)
							}
							disabled={isLoading}
						/>
						<Label htmlFor="respectRobotsTxt">Respect robots.txt</Label>
						<p className="text-xs text-muted-foreground ml-2">
							Follow robots.txt rules when crawling
						</p>
					</div>

					<div className="flex items-center space-x-2">
						<Switch
							id="incrementalCrawl"
							checked={config.incrementalCrawl}
							onCheckedChange={(checked) =>
								handleInputChange("incrementalCrawl", checked)
							}
							disabled={isLoading}
						/>
						<Label htmlFor="incrementalCrawl">Incremental Crawling</Label>
						<p className="text-xs text-muted-foreground ml-2">
							Only process new or changed content (faster crawling)
						</p>
					</div>
				</CardContent>
				<CardFooter>
					<Button
						type="submit"
						disabled={isLoading || isLoadingConfig}
						className="w-full"
					>
						{isLoading ? (
							<>
								<RefreshCw className="mr-2 h-4 w-4 animate-spin" />
								Starting Crawl...
							</>
						) : isLoadingConfig ? (
							<>
								<RefreshCw className="mr-2 h-4 w-4 animate-spin" />
								Loading Configuration...
							</>
						) : (
							"Start Crawling"
						)}
					</Button>
				</CardFooter>
			</form>
		</Card>
	);
}
