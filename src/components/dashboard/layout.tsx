import { DashboardNavigation } from "./navigation";

interface DashboardLayoutProps {
	children: React.ReactNode;
	title: string;
	description?: string;
}

export function DashboardLayout({
	children,
	title,
	description,
}: DashboardLayoutProps) {
	return (
		<div className="min-h-screen bg-gray-50">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="py-6">
					<h1 className="text-2xl font-semibold text-gray-900">{title}</h1>
					{description && (
						<p className="mt-2 text-sm text-gray-600">{description}</p>
					)}
					<DashboardNavigation />
					<div className="mt-8">
						<div className="bg-white rounded-lg shadow">{children}</div>
					</div>
				</div>
			</div>
		</div>
	);
}
