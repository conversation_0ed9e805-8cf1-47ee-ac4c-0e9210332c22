"use client";

import { ModernHeader } from "@/components/dashboard/ModernHeader";
import { ModernSidebar } from "@/components/dashboard/ModernSidebar";
import { useSidebar } from "@/contexts/SidebarContext";
import { cn } from "@/lib/utils";

export const DashboardSidebar = () => {
	const { isOpen, close } = useSidebar();

	return (
		<>
			{/* Overlay for mobile - only visible when sidebar is open */}
			{isOpen && (
				<div
					className="fixed inset-0 bg-background/80 backdrop-blur-sm z-20 md:hidden"
					onClick={close}
					onKeyDown={(e) => {
						// Close on Escape key
						if (e.key === "Escape") {
							close();
						}
					}}
					tabIndex={0} // Make div focusable for keyboard navigation
					role="button"
					aria-label="Close sidebar"
				/>
			)}
			<ModernSidebar
				className={cn(
					"w-64 border-r border-border/40 fixed h-full shadow-sm z-30 transition-all duration-300 ease-in-out",
					isOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0",
				)}
			/>
		</>
	);
};

export const DashboardContent = ({
	children,
}: {
	children: React.ReactNode;
}) => {
	const { isOpen } = useSidebar();

	return (
		<div
			className={cn(
				"flex-1 transition-all duration-300 ease-in-out",
				isOpen ? "md:ml-64" : "ml-0",
			)}
		>
			<ModernHeader className="sticky top-0 z-20 backdrop-blur-md bg-background/80 border-b border-border/40" />
			<main className="p-4 md:p-8 max-w-7xl mx-auto">{children}</main>
		</div>
	);
};
