"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useUserPlan } from "@/hooks";
import { usePlanUsage } from "@/hooks/usePlanUsage";
import { Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";

export function PlanUsage() {
	const router = useRouter();
	const { data: userPlan, isLoading: isPlanLoading } = useUserPlan();
	const {
		data: usage,
		isLoading: isUsageLoading,
		isError: isUsageError,
	} = usePlanUsage();

	const isLoading = isPlanLoading || isUsageLoading;

	const handleUpgrade = () => {
		router.push("/dashboard/plans");
	};

	if (isLoading) {
		return (
			<Card>
				<CardHeader className="pb-2">
					<CardTitle className="text-lg">Plan Usage</CardTitle>
					<CardDescription>Loading your usage data...</CardDescription>
				</CardHeader>
				<CardContent className="flex justify-center py-4">
					<Loader2 className="h-8 w-8 animate-spin text-primary" />
				</CardContent>
			</Card>
		);
	}

	if (!userPlan || !usage || isUsageError) {
		return (
			<Card>
				<CardHeader className="pb-2">
					<CardTitle className="text-lg">Plan Usage</CardTitle>
					<CardDescription>Unable to load usage data</CardDescription>
				</CardHeader>
				<CardContent>
					<p className="text-sm text-muted-foreground mb-4">
						There was an error loading your plan usage data. Please try again
						later.
					</p>
					<Button
						variant="outline"
						size="sm"
						onClick={() => window.location.reload()}
						className="text-xs"
					>
						Refresh
					</Button>
				</CardContent>
			</Card>
		);
	}

	const calculatePercentage = (current: number, limit: number) => {
		return Math.min(Math.round((current / limit) * 100), 100);
	};

	const websitePercentage = calculatePercentage(
		usage.websites.count,
		usage.websites.limit,
	);
	const pagesPercentage = calculatePercentage(
		usage.pages.count,
		usage.pages.limit,
	);
	const messagesPercentage = calculatePercentage(
		usage.messages.count,
		usage.messages.limit,
	);

	return (
		<Card>
			<CardHeader className="pb-2">
				<div className="flex items-center justify-between">
					<div>
						<CardTitle className="text-lg">Plan Usage</CardTitle>
						<CardDescription>{userPlan.name} Plan</CardDescription>
					</div>
					<Button
						variant="outline"
						size="sm"
						onClick={handleUpgrade}
						className="text-xs"
					>
						{userPlan.name === "Free" ? "Upgrade" : "Manage Plan"}
					</Button>
				</div>
			</CardHeader>
			<CardContent className="space-y-4">
				<div className="space-y-2">
					<div className="flex justify-between text-sm">
						<span className="text-muted-foreground">Websites</span>
						<span className="font-medium">
							{usage.websites.count} / {usage.websites.limit}
						</span>
					</div>
					<Progress
						value={websitePercentage}
						className={`h-2 ${websitePercentage > 90 ? "bg-amber-100" : ""}`}
					/>
					{websitePercentage >= 100 && (
						<p className="text-xs text-amber-600">
							You've reached your website limit. Upgrade to add more websites.
						</p>
					)}
				</div>

				<div className="space-y-2">
					<div className="flex justify-between text-sm">
						<span className="text-muted-foreground">Pages per website</span>
						<span className="font-medium">
							{usage.pages.count} / {usage.pages.limit}
						</span>
					</div>
					<Progress
						value={pagesPercentage}
						className={`h-2 ${pagesPercentage > 90 ? "bg-amber-100" : ""}`}
					/>
					{usage.pages.count === 0 && (
						<p className="text-xs text-muted-foreground">
							No pages crawled yet. Crawl a website to see page usage.
						</p>
					)}
				</div>

				<div className="space-y-2">
					<div className="flex justify-between text-sm">
						<span className="text-muted-foreground">Messages today</span>
						<span className="font-medium">
							{usage.messages.count} / {usage.messages.limit}
						</span>
					</div>
					<Progress
						value={messagesPercentage}
						className={`h-2 ${messagesPercentage > 90 ? "bg-amber-100" : ""}`}
					/>
					{messagesPercentage >= 100 && (
						<p className="text-xs text-amber-600">
							You've reached your daily message limit. Upgrade for more
							messages.
						</p>
					)}
					{messagesPercentage === 0 && (
						<p className="text-xs text-muted-foreground">
							No messages sent today. Messages reset daily.
						</p>
					)}
				</div>
			</CardContent>
		</Card>
	);
}
