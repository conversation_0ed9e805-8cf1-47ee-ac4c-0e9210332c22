"use client";

import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

export function MarketingFooter() {
	const currentYear = new Date().getFullYear();

	return (
		<footer className="border-t bg-muted/40">
			<div className="container py-12 md:py-16 max-w-screen-xl mx-auto">
				<div className="grid grid-cols-1 md:grid-cols-4 gap-8">
					<div className="md:col-span-1">
						<Link href="/" className="font-bold text-xl">
							Bubl
						</Link>
						<p className="mt-2 text-sm text-muted-foreground">
							Add intelligent chat to your website powered by AI
						</p>
						<div className="mt-4 flex space-x-3">
							<Button variant="ghost" size="icon" asChild>
								<a
									href="https://twitter.com/bublchat"
									target="_blank"
									rel="noopener noreferrer"
									aria-label="Twitter"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										strokeWidth="2"
										strokeLinecap="round"
										strokeLinejoin="round"
										className="h-5 w-5"
									>
										<title>Twitter icon</title>
										<path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
									</svg>
								</a>
							</Button>
							<Button variant="ghost" size="icon" asChild>
								<a
									href="https://github.com/bublchat"
									target="_blank"
									rel="noopener noreferrer"
									aria-label="GitHub"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										strokeWidth="2"
										strokeLinecap="round"
										strokeLinejoin="round"
										className="h-5 w-5"
									>
										<title>GitHub icon</title>
										<path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4" />
										<path d="M9 18c-4.51 2-5-2-7-2" />
									</svg>
								</a>
							</Button>
							<Button variant="ghost" size="icon" asChild>
								<a
									href="https://linkedin.com/company/bublchat"
									target="_blank"
									rel="noopener noreferrer"
									aria-label="LinkedIn"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										strokeWidth="2"
										strokeLinecap="round"
										strokeLinejoin="round"
										className="h-5 w-5"
									>
										<title>LinkedIn icon</title>
										<path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
										<rect width="4" height="12" x="2" y="9" />
										<circle cx="4" cy="4" r="2" />
									</svg>
								</a>
							</Button>
						</div>
					</div>

					<div>
						<h3 className="font-medium mb-3">Product</h3>
						<ul className="space-y-2">
							<li>
								<Link
									href="/features"
									className="text-sm text-muted-foreground hover:text-foreground transition-colors"
								>
									Features
								</Link>
							</li>
							<li>
								<Link
									href="/pricing"
									className="text-sm text-muted-foreground hover:text-foreground transition-colors"
								>
									Pricing
								</Link>
							</li>
							<li>
								<Link
									href="/blog"
									className="text-sm text-muted-foreground hover:text-foreground transition-colors"
								>
									Blog
								</Link>
							</li>
							<li>
								<Link
									href="/changelog"
									className="text-sm text-muted-foreground hover:text-foreground transition-colors"
								>
									Changelog
								</Link>
							</li>
						</ul>
					</div>

					<div>
						<h3 className="font-medium mb-3">Resources</h3>
						<ul className="space-y-2">
							<li>
								<Link
									href="/docs"
									className="text-sm text-muted-foreground hover:text-foreground transition-colors"
								>
									Documentation
								</Link>
							</li>
							<li>
								<Link
									href="/api"
									className="text-sm text-muted-foreground hover:text-foreground transition-colors"
								>
									API Reference
								</Link>
							</li>
							<li>
								<Link
									href="/support"
									className="text-sm text-muted-foreground hover:text-foreground transition-colors"
								>
									Support
								</Link>
							</li>
						</ul>
					</div>

					<div>
						<h3 className="font-medium mb-3">Company</h3>
						<ul className="space-y-2">
							<li>
								<Link
									href="/about"
									className="text-sm text-muted-foreground hover:text-foreground transition-colors"
								>
									About
								</Link>
							</li>
							<li>
								<Link
									href="/privacy"
									className="text-sm text-muted-foreground hover:text-foreground transition-colors"
								>
									Privacy Policy
								</Link>
							</li>
							<li>
								<Link
									href="/terms"
									className="text-sm text-muted-foreground hover:text-foreground transition-colors"
								>
									Terms of Service
								</Link>
							</li>
						</ul>
					</div>
				</div>

				<div className="mt-12 pt-8 border-t text-center">
					<p className="text-sm text-muted-foreground">
						© {currentYear} Bubl. All rights reserved.
					</p>
				</div>
			</div>
		</footer>
	);
}
