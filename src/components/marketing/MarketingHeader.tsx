"use client";

import { ThemeToggle } from "@/components/ThemeToggle";
import { But<PERSON> } from "@/components/ui/button";
import {
	SignInButton,
	SignUpButton,
	SignedIn,
	SignedOut,
	UserButton,
} from "@clerk/nextjs";
import { LayoutDashboard, Menu, X } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export function MarketingHeader() {
	const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

	const toggleMobileMenu = () => {
		setMobileMenuOpen(!mobileMenuOpen);
	};

	return (
		<header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
			<div className="container flex h-16 items-center justify-between  max-w-screen-xl mx-auto">
				<div className="flex items-center gap-2">
					<Link href="/" className="flex items-center gap-2">
						<span className="font-bold text-xl">Bubl</span>
					</Link>
				</div>

				{/* Desktop Navigation */}
				<nav className="hidden md:flex items-center gap-6">
					<Link
						href="/features"
						className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
					>
						Features
					</Link>
					<Link
						href="/pricing"
						className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
					>
						Pricing
					</Link>
					<Link
						href="/blog"
						className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
					>
						Blog
					</Link>
				</nav>

				{/* Desktop Actions */}
				<div className="hidden md:flex items-center gap-4">
					<ThemeToggle />

					<SignedIn>
						<Link href="/dashboard">
							<Button variant="outline" size="sm">
								<LayoutDashboard className="mr-2 h-4 w-4" />
								Dashboard
							</Button>
						</Link>
						<UserButton afterSignOutUrl="/" />
					</SignedIn>

					<SignedOut>
						<SignInButton mode="modal">
							<Button variant="ghost" size="sm">
								Sign In
							</Button>
						</SignInButton>
						<SignUpButton mode="modal">
							<Button size="sm">Sign Up</Button>
						</SignUpButton>
					</SignedOut>
				</div>

				{/* Mobile Menu Button */}
				<div className="flex md:hidden items-center gap-4">
					<ThemeToggle />
					<Button
						variant="ghost"
						size="icon"
						onClick={toggleMobileMenu}
						aria-label="Toggle Menu"
					>
						{mobileMenuOpen ? (
							<X className="h-5 w-5" />
						) : (
							<Menu className="h-5 w-5" />
						)}
					</Button>
				</div>
			</div>

			{/* Mobile Menu */}
			{mobileMenuOpen && (
				<div className="md:hidden border-t">
					<div className="container py-4 space-y-4">
						<nav className="flex flex-col space-y-4">
							<Link
								href="/features"
								className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
								onClick={() => setMobileMenuOpen(false)}
							>
								Features
							</Link>
							<Link
								href="/pricing"
								className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
								onClick={() => setMobileMenuOpen(false)}
							>
								Pricing
							</Link>
							<Link
								href="/blog"
								className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
								onClick={() => setMobileMenuOpen(false)}
							>
								Blog
							</Link>
						</nav>

						<div className="pt-4 border-t flex flex-col gap-2">
							<SignedIn>
								<Link
									href="/dashboard"
									onClick={() => setMobileMenuOpen(false)}
								>
									<Button variant="outline" className="w-full justify-start">
										<LayoutDashboard className="mr-2 h-4 w-4" />
										Dashboard
									</Button>
								</Link>
							</SignedIn>

							<SignedOut>
								<SignInButton mode="modal">
									<Button variant="outline" className="w-full">
										Sign In
									</Button>
								</SignInButton>
								<SignUpButton mode="modal">
									<Button className="w-full">Sign Up</Button>
								</SignUpButton>
							</SignedOut>
						</div>
					</div>
				</div>
			)}
		</header>
	);
}
