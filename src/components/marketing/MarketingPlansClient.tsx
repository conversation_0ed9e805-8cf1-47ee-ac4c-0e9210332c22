"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON>,
	CardContent,
	CardDescription,
	Card<PERSON>oot<PERSON>,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { useMarketingPlans } from "@/hooks/useMarketingPlans";
import { ArrowRight, Check, Loader2 } from "lucide-react";
import Link from "next/link";

export function MarketingPlansClient() {
	const { data: plans, isLoading } = useMarketingPlans();

	const formatPrice = (price: number) => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: "USD",
			minimumFractionDigits: 0,
		}).format(price / 100);
	};

	return (
		<>
			{/* Hero Section */}
			<section className="py-20 px-4 md:px-6 lg:py-32 bg-gradient-to-b from-background to-muted/30">
				<div className="container mx-auto max-w-5xl">
					<div className="flex flex-col items-center text-center space-y-8">
						<h1 className="text-4xl md:text-6xl font-bold tracking-tight">
							Simple, Transparent <span className="text-primary">Pricing</span>
						</h1>
						<p className="text-xl text-muted-foreground max-w-2xl">
							Choose the plan that best fits your needs. All plans include our
							core features.
						</p>
					</div>
				</div>
			</section>

			{/* Plans Section */}
			<section className="py-20 px-4 md:px-6">
				<div className="container mx-auto max-w-5xl">
					{isLoading ? (
						<div className="flex justify-center items-center h-64">
							<Loader2 className="h-8 w-8 animate-spin text-primary" />
						</div>
					) : (
						<div className="grid grid-cols-1 gap-6 md:grid-cols-3">
							{plans?.map((plan) => {
								const features = plan.features
									? plan.features.split(",").map((f) => f.trim())
									: [];

								return (
									<Card
										key={plan.id}
										className={`border-border/40 shadow-sm hover:shadow-md transition-shadow duration-200 ${
											plan.name === "Pro"
												? "border-primary/50 md:scale-105 z-10"
												: ""
										}`}
									>
										<CardHeader>
											<CardTitle className="text-xl font-bold text-card-foreground">
												{plan.name}
											</CardTitle>
											<CardDescription className="text-muted-foreground">
												{plan.description}
											</CardDescription>
											<div className="mt-2">
												<span className="text-3xl font-bold text-card-foreground">
													{formatPrice(plan.price)}
												</span>
												{plan.price > 0 && (
													<span className="text-muted-foreground ml-1">
														/month
													</span>
												)}
											</div>
										</CardHeader>
										<CardContent className="space-y-4">
											<div className="space-y-2">
												<div className="flex justify-between text-sm">
													<span className="text-muted-foreground">
														Websites
													</span>
													<span className="font-medium text-card-foreground">
														{plan.websiteLimit}
													</span>
												</div>
												<div className="flex justify-between text-sm">
													<span className="text-muted-foreground">
														Pages per website
													</span>
													<span className="font-medium text-card-foreground">
														{plan.pagesPerWebsiteLimit}
													</span>
												</div>
												<div className="flex justify-between text-sm">
													<span className="text-muted-foreground">
														Messages per day
													</span>
													<span className="font-medium text-card-foreground">
														{plan.messagesPerDayLimit}
													</span>
												</div>
											</div>

											<div className="pt-4 space-y-2">
												{features.map((feature) => (
													<div
														key={feature}
														className="flex items-center gap-2"
													>
														<Check className="h-4 w-4 text-primary" />
														<span className="text-sm">{feature}</span>
													</div>
												))}
											</div>
										</CardContent>
										<CardFooter>
											<Link href="/dashboard" className="w-full">
												<Button className="w-full rounded-full">
													Get Started
													<ArrowRight className="ml-2 h-4 w-4" />
												</Button>
											</Link>
										</CardFooter>
									</Card>
								);
							})}
						</div>
					)}
				</div>
			</section>

			{/* FAQ Section */}
			<section className="py-20 px-4 md:px-6 bg-muted/30">
				<div className="container mx-auto max-w-5xl">
					<h2 className="text-3xl font-bold text-center mb-12">
						Frequently Asked Questions
					</h2>
					<div className="grid md:grid-cols-2 gap-8">
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-sm">
							<h3 className="text-xl font-semibold mb-3">
								Can I upgrade or downgrade my plan?
							</h3>
							<p className="text-muted-foreground">
								Yes, you can change your plan at any time. When you upgrade,
								you'll get immediate access to the new features. When you
								downgrade, the changes will take effect at the end of your
								current billing cycle.
							</p>
						</div>
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-sm">
							<h3 className="text-xl font-semibold mb-3">
								Is there a free trial?
							</h3>
							<p className="text-muted-foreground">
								Yes! You can start with our Free plan which includes basic
								features to get you started. No credit card required.
							</p>
						</div>
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-sm">
							<h3 className="text-xl font-semibold mb-3">
								What payment methods do you accept?
							</h3>
							<p className="text-muted-foreground">
								We accept all major credit cards including Visa, Mastercard, and
								American Express. We also support payment via PayPal.
							</p>
						</div>
						<div className="bg-card border border-border/40 rounded-lg p-6 shadow-sm">
							<h3 className="text-xl font-semibold mb-3">
								Do you offer custom plans?
							</h3>
							<p className="text-muted-foreground">
								Yes, if you have specific needs that aren't covered by our
								standard plans, please contact us to discuss a custom solution
								for your business.
							</p>
						</div>
					</div>
				</div>
			</section>
		</>
	);
}
