"use client";

import { DeleteWebsiteDialog } from "@/components/dashboard/DeleteWebsiteDialog";
import { ErrorState, LoadingState } from "@/components/ui";
import { Button } from "@/components/ui/button";
import { useDeleteWebsite, useWebsites } from "@/hooks";
import { ExternalLink, Settings, Trash2 } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { toast } from "sonner";

export function WebsiteList() {
	const { data: websites, isLoading, isError, error, refetch } = useWebsites();
	const { mutate: deleteWebsite, isPending: isDeleting } = useDeleteWebsite();

	// State for the delete dialog
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [websiteToDelete, setWebsiteToDelete] = useState<{
		id: string;
		name: string;
		url: string;
	} | null>(null);

	const openDeleteDialog = (id: string, name: string, url: string) => {
		setWebsiteToDelete({ id, name, url });
		setDeleteDialogOpen(true);
	};

	const closeDeleteDialog = () => {
		setDeleteDialogOpen(false);
	};

	const confirmDelete = () => {
		if (!websiteToDelete) return;

		deleteWebsite(websiteToDelete.id, {
			onSuccess: () => {
				toast.success("Website deleted", {
					description: `"${websiteToDelete.name}" has been removed from your account.`,
				});
				closeDeleteDialog();
				// Refetch the websites list to update the UI
				refetch();
			},
			onError: (err) => {
				toast.error("Failed to delete website", {
					description:
						err.message || "An error occurred while deleting the website.",
				});
			},
		});
	};

	if (isLoading) {
		return <LoadingState message="Loading websites..." />;
	}

	if (isError) {
		return (
			<ErrorState
				message={error?.message || "Failed to load websites"}
				retry={() => refetch()}
			/>
		);
	}

	if (!websites?.length) {
		return (
			<div className="text-center p-6 bg-muted/20 rounded-lg border border-border/40">
				<p className="text-card-foreground">No website found</p>
				<p className="text-sm text-muted-foreground mt-2">
					Add a website to get started with Bubl
				</p>
			</div>
		);
	}

	return (
		<>
			<div className="space-y-4">
				{websites.map((website) => (
					<div
						key={website.id}
						className="flex flex-col sm:flex-row sm:items-center justify-between p-5 bg-card rounded-lg border border-border/40 shadow-sm"
					>
						<div className="mb-4 sm:mb-0">
							<h3 className="font-medium text-card-foreground">
								{website.name}
							</h3>
							<div className="flex items-center mt-1.5">
								<a
									href={website.url}
									target="_blank"
									rel="noopener noreferrer"
									className="text-sm text-primary hover:text-primary/80 flex items-center"
								>
									{website.url}
									<ExternalLink className="ml-1 h-3 w-3" />
								</a>
							</div>
						</div>
						<div className="flex items-center gap-3">
							<Link href={`/dashboard/websites/${website.id}`} passHref>
								<Button
									variant="outline"
									size="sm"
									className="rounded-full border-primary/20 bg-primary/5 text-primary hover:bg-primary/10"
								>
									<Settings className="h-4 w-4 mr-2" />
									Manage
								</Button>
							</Link>
							<Button
								variant="outline"
								size="sm"
								onClick={() =>
									openDeleteDialog(website.id, website.name, website.url)
								}
								disabled={isDeleting}
								className="rounded-full border-destructive/20 bg-destructive/5 text-destructive hover:bg-destructive/10"
							>
								<Trash2 className="h-4 w-4" />
							</Button>
						</div>
					</div>
				))}
			</div>

			{/* Delete Website Dialog */}
			{websiteToDelete && (
				<DeleteWebsiteDialog
					isOpen={deleteDialogOpen}
					onClose={closeDeleteDialog}
					onConfirm={confirmDelete}
					websiteName={websiteToDelete.name}
					websiteUrl={websiteToDelete.url}
					isDeleting={isDeleting}
				/>
			)}
		</>
	);
}
