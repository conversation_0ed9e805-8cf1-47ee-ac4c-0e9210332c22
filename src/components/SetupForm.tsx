"use client";

import { WebsiteForm } from "@/components/WebsiteForm";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { useOrganization } from "@clerk/nextjs";
import { useState } from "react";

export function SetupForm() {
	const { organization } = useOrganization();
	const [step, setStep] = useState<"organization" | "website">(
		organization ? "website" : "organization",
	);
	const [organizationId, setOrganizationId] = useState<string | null>(
		organization?.id || null,
	);

	const handleOrganizationCreated = (newOrgId: string) => {
		setOrganizationId(newOrgId);
		setStep("website");
	};

	const handleWebsiteCreated = () => {
		// Redirect to dashboard or show success message
		window.location.href = "/dashboard";
	};

	return (
		<div className="space-y-6">
			{step === "organization" && (
				<Card>
					<CardHeader>
						<CardTitle>Create Organization</CardTitle>
						<CardDescription>
							First, let's create an organization for your website
						</CardDescription>
					</CardHeader>
				</Card>
			)}

			{step === "website" && organizationId && (
				<Card>
					<CardHeader>
						<CardTitle>Add Your Website</CardTitle>
						<CardDescription>
							Now, let's add your website to get started
						</CardDescription>
					</CardHeader>
					<CardContent>
						<WebsiteForm onSuccess={handleWebsiteCreated} />
					</CardContent>
				</Card>
			)}
		</div>
	);
}
