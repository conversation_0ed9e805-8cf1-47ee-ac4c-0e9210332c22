"use client";

import { ThemeToggle } from "@/components/ThemeToggle";
import { Button } from "@/components/ui/button";
import {
	SignInButton,
	SignUpButton,
	SignedIn,
	SignedOut,
	UserButton,
} from "@clerk/nextjs";
import { LayoutDashboard } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";

export function Header() {
	const pathname = usePathname();
	const isDashboard = pathname?.startsWith("/dashboard");

	// Don't render this header on dashboard pages as we use DashboardHeader instead
	if (isDashboard) {
		return null;
	}

	return (
		<header className="flex justify-between items-center p-4 h-16 border-b">
			<div>
				<Link href="/" className="font-bold text-xl">
					Bubl
				</Link>
			</div>

			<div className="flex items-center gap-4">
				<ThemeToggle />

				<SignedIn>
					<Link href="/dashboard">
						<Button variant="outline" size="sm">
							<LayoutDashboard className="mr-2 h-4 w-4" />
							Dashboard
						</Button>
					</Link>
					<UserButton afterSignOutUrl="/" />
				</SignedIn>

				<SignedOut>
					<SignInButton mode="modal">
						<Button variant="outline" size="sm">
							Sign In
						</Button>
					</SignInButton>
					<SignUpButton mode="modal">
						<Button size="sm">Sign Up</Button>
					</SignUpButton>
				</SignedOut>
			</div>
		</header>
	);
}
