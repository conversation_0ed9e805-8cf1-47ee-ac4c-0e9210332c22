import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { AlertCircle } from "lucide-react";

interface ErrorStateProps {
	message?: string;
	className?: string;
	retry?: () => void;
}

export function ErrorState({
	message = "Something went wrong",
	className = "",
	retry,
}: ErrorStateProps) {
	return (
		<div
			className={cn(
				"flex flex-col items-center justify-center p-6 rounded-lg border border-destructive/20 bg-destructive/5",
				className,
			)}
		>
			<AlertCircle className="h-8 w-8 text-destructive" />
			<p className="mt-3 text-sm text-card-foreground text-center">{message}</p>
			{retry && (
				<Button
					type="button"
					onClick={retry}
					variant="outline"
					size="sm"
					className="mt-4 rounded-full border-destructive/20 bg-destructive/5 text-destructive hover:bg-destructive/10"
				>
					Try again
				</Button>
			)}
		</div>
	);
}
