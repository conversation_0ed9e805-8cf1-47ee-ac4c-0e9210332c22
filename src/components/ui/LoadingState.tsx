import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";

interface LoadingStateProps {
	message?: string;
	className?: string;
}

export function LoadingState({
	message = "Loading...",
	className = "",
}: LoadingStateProps) {
	return (
		<div
			className={cn(
				"flex flex-col items-center justify-center p-6 rounded-lg border border-border/40 bg-muted/10",
				className,
			)}
		>
			<Loader2 className="h-8 w-8 animate-spin text-primary" />
			<p className="mt-3 text-sm text-muted-foreground text-center">
				{message}
			</p>
		</div>
	);
}
