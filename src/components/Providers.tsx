"use client";

import { Toaster } from "@/components/ui/sonner";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { ReactQueryStreamedHydration } from "@tanstack/react-query-next-experimental";
import { ThemeProvider } from "next-themes";
import { useState } from "react";

export function Providers({ children }: { children: React.ReactNode }) {
	// Create a new QueryClient instance for each client component
	const [queryClient] = useState(
		() =>
			new QueryClient({
				defaultOptions: {
					queries: {
						staleTime: 60 * 1000, // 1 minute
						retry: 1,
						// This is important for SSR to work properly
						refetchOnWindowFocus: false,
					},
				},
			}),
	);

	return (
		<ClerkProvider
			appearance={{
				elements: {
					organizationSwitcherTrigger: "py-2 px-4",
					userButtonTrigger: "py-2 px-4",
				},
			}}
		>
			<QueryClientProvider client={queryClient}>
				{/* Enable streaming hydration */}
				<ReactQueryStreamedHydration>
					<ThemeProvider attribute="class" defaultTheme="light" enableSystem>
						{children}
						<Toaster />
					</ThemeProvider>
				</ReactQueryStreamedHydration>
				<ReactQueryDevtools initialIsOpen={false} />
			</QueryClientProvider>
		</ClerkProvider>
	);
}
