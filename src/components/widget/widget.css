/* Reset styles for the widget */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

:root {
	--chat-primary-color: #4f46e5;
}

body {
	font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>,
		sans-serif;
	line-height: 1.5;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	background-color: transparent !important; /* Force transparent background */
	overflow: hidden;
}

/* Hide scrollbar for Chrome, Safari and Opera */
::-webkit-scrollbar {
	display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
* {
	-ms-overflow-style: none; /* IE and Edge */
	scrollbar-width: none; /* Firefox */
}
