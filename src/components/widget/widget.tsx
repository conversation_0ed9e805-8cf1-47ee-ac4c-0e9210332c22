"use client";

import { useSearchParams } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import { SimpleChatWidget } from "../chat-ui/SimpleChatWidget";

// Import web-vitals for performance tracking
import * as webVitals from "web-vitals";

// Define interfaces for non-standard browser APIs
interface MemoryInfo {
	totalJSHeapSize: number;
	usedJSHeapSize: number;
	jsHeapSizeLimit: number;
}

interface PerformanceWithMemory extends Performance {
	memory: MemoryInfo;
}

interface NetworkInformation {
	effectiveType: string;
	downlink: number;
	rtt: number;
	saveData: boolean;
}

const widgetScriptStart =
	typeof window !== "undefined"
		? (window.performance?.now?.() ?? Date.now())
		: Date.now();

export default function WidgetClientComponent() {
	const searchParams = useSearchParams();
	const [mounted, setMounted] = useState(false);

	// Get parameters from the URL
	const websiteId = searchParams?.get("websiteId") || "";
	const primaryColor = searchParams?.get("primaryColor") || "#4F46E5";
	const secondaryColor = searchParams?.get("secondaryColor") || "#FFFFFF";
	const position = (searchParams?.get("position") || "bottom-right") as
		| "bottom-right"
		| "bottom-left";
	const welcomeMessage =
		searchParams?.get("welcomeMessage") ||
		"Hi there! How can I help you today?";
	const headerText = searchParams?.get("headerText") || "Chat Assistant";
	const initiallyOpen = searchParams?.get("initiallyOpen") === "true";

	// Custom styles for the iframe content
	const customStyles = {
		"--chat-primary-color": primaryColor,
		"--chat-secondary-color": secondaryColor,
	} as React.CSSProperties;

	// Get visitor ID from URL parameters or generate a new one
	const visitorIdFromUrl = searchParams?.get("visitorId");
	const visitorIdRef = useRef<string>(
		visitorIdFromUrl ||
			`visitor-${Math.random().toString(36).substring(2, 15)}`,
	);

	// Generate a UUID for the conversation
	const generateUUID = (): string => {
		// Use crypto.randomUUID if available (modern browsers)
		if (crypto.randomUUID) {
			return crypto.randomUUID();
		}

		// Fallback UUID v4 implementation for older browsers
		// This creates a valid UUID v4 format string
		return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
			const r = (Math.random() * 16) | 0;
			const v = c === "x" ? r : (r & 0x3) | 0x8;
			return v.toString(16);
		});
	};

	// Always generate a new conversation ID for each session (must be a valid UUID)
	const conversationIdRef = useRef<string>(generateUUID());

	// Function to track performance metrics
	const trackMetric = useCallback(
		async (metricName: string, value: number, additionalData = {}) => {
			if (!websiteId) return;

			try {
				await fetch("/api/analytics/track-event", {
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({
						websiteId,
						visitorId: visitorIdRef.current,
						conversationId: conversationIdRef.current,
						eventType: metricName,
						metadata: {
							value,
							isWidget: true,
							source: "embedded",
							...additionalData,
						},
					}),
				});
			} catch (error) {
				console.error(`Error tracking ${metricName}:`, error);
			}
		},
		[websiteId],
	);

	// Handle component mounting and tracking
	useEffect(() => {
		setMounted(true);

		if (websiteId) {
			const widgetReadyTime = window.performance?.now?.() ?? Date.now();
			const loadDuration = widgetReadyTime - widgetScriptStart;

			// Track widget load event with load duration
			trackMetric("widget_loaded", loadDuration, {
				position,
				initiallyOpen,
				referrer: document.referrer || "unknown",
				url: window.location.href,
			});

			// Track web vitals
			try {
				// Core Web Vitals
				webVitals.onCLS((metric) => trackMetric("widget_cls", metric.value));
				webVitals.onFID((metric) => trackMetric("widget_fid", metric.value));
				webVitals.onLCP((metric) => trackMetric("widget_lcp", metric.value));
				webVitals.onTTFB((metric) => trackMetric("widget_ttfb", metric.value));
				webVitals.onFCP((metric) => trackMetric("widget_fcp", metric.value));

				// Approximate TTI (Time to Interactive)
				const tti = performance.now() - widgetScriptStart;
				trackMetric("widget_tti", tti);

				// Track memory usage if available
				if ("memory" in performance) {
					const perfMemory = performance as PerformanceWithMemory;
					trackMetric("widget_memory", perfMemory.memory.usedJSHeapSize, {
						totalJSHeapSize: perfMemory.memory.totalJSHeapSize,
						jsHeapSizeLimit: perfMemory.memory.jsHeapSizeLimit,
					});
				}

				// Track network information if available
				if ("connection" in navigator) {
					const navConnection = navigator as unknown as {
						connection: NetworkInformation;
					};
					trackMetric("widget_network", 0, {
						effectiveType: navConnection.connection.effectiveType,
						downlink: navConnection.connection.downlink,
						rtt: navConnection.connection.rtt,
						saveData: navConnection.connection.saveData,
					});
				}
			} catch (error) {
				console.error("Error tracking web vitals:", error);
			}
		}
	}, [websiteId, position, initiallyOpen, trackMetric]);

	// Only render the content after the component has mounted on the client
	if (!mounted) {
		return (
			<div className="h-screen w-full flex items-center justify-center bg-background text-foreground">
				{/* <div className="animate-pulse">Loading...</div> */}
			</div>
		);
	}

	return (
		<div
			className="h-screen w-full bg-transparent text-foreground"
			style={{
				...customStyles,
				backgroundColor: "transparent",
			}}
		>
			<SimpleChatWidget
				websiteId={websiteId}
				position={position}
				title={headerText}
				primaryColor={primaryColor}
				secondaryColor={secondaryColor}
				welcomeMessage={welcomeMessage}
				initiallyOpen={initiallyOpen}
				// Pass visitor ID to ensure conversation continuity
				visitorId={visitorIdRef.current}
				// Pass isWidget flag to identify this as an embedded widget in analytics
				isWidget={true}
			/>
		</div>
	);
}
