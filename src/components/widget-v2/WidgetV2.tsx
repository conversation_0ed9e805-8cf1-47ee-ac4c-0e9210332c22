'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { createRoot } from 'react-dom/client'
import { marked } from 'marked'

// Types
interface WidgetConfig {
  websiteId: string
  primaryColor?: string
  secondaryColor?: string
  position?: 'bottom-right' | 'bottom-left'
  welcomeMessage?: string
  headerText?: string
  initiallyOpen?: boolean
  baseUrl?: string
  visitorId?: string
}

interface WidgetProps {
  config: WidgetConfig
  shadowRoot: ShadowRoot | HTMLElement
}

interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
}

// Styles as JavaScript objects for complete isolation
const styles = {
  container: {
    position: 'fixed' as const,
    bottom: '20px',
    right: '20px',
    zIndex: 2147483647,
    fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    fontSize: '14px',
    lineHeight: '1.5',
    color: '#333',
    pointerEvents: 'auto' as const,
  },

  containerLeft: {
    left: '20px',
    right: 'auto',
  },

  bubble: {
    width: '60px',
    height: '60px',
    borderRadius: '50%',
    border: 'none',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    transition: 'all 0.3s ease',
    outline: 'none',
    position: 'relative' as const,
  },

  bubbleHover: {
    transform: 'scale(1.05)',
    boxShadow: '0 6px 16px rgba(0, 0, 0, 0.2)',
  },

  chatWindow: {
    position: 'absolute' as const,
    bottom: '70px',
    right: '0',
    width: '360px',
    height: '600px',
    maxHeight: 'calc(100vh - 100px)',
    backgroundColor: '#ffffff',
    borderRadius: '12px',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
    border: '1px solid #e5e7eb',
    display: 'flex',
    flexDirection: 'column' as const,
    overflow: 'hidden',
    transform: 'translateY(10px)',
    opacity: 0,
    transition: 'all 0.3s ease',
    pointerEvents: 'auto' as const,
  },

  chatWindowLeft: {
    left: '0',
    right: 'auto',
  },

  chatWindowOpen: {
    transform: 'translateY(0)',
    opacity: 1,
  },

  header: {
    padding: '16px 20px',
    borderBottom: '1px solid #e5e7eb',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#ffffff',
  },

  headerTitle: {
    margin: 0,
    fontSize: '16px',
    fontWeight: '600',
    color: '#111827',
  },

  closeButton: {
    background: 'none',
    border: 'none',
    cursor: 'pointer',
    padding: '4px',
    borderRadius: '4px',
    color: '#6b7280',
    fontSize: '18px',
    lineHeight: 1,
    outline: 'none',
  },

  messagesContainer: {
    flex: 1,
    padding: '16px',
    overflowY: 'auto' as const,
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '12px',
  },

  message: {
    maxWidth: '80%',
    padding: '8px 12px',
    borderRadius: '12px',
    fontSize: '14px',
    lineHeight: '1.4',
  },

  userMessage: {
    alignSelf: 'flex-end' as const,
    backgroundColor: '#3b82f6',
    color: '#ffffff',
  },

  assistantMessage: {
    alignSelf: 'flex-start' as const,
    backgroundColor: '#f3f4f6',
    color: '#111827',
  },

  inputContainer: {
    padding: '16px',
    borderTop: '1px solid #e5e7eb',
    display: 'flex',
    gap: '8px',
    backgroundColor: '#ffffff',
  },

  input: {
    flex: 1,
    padding: '8px 12px',
    border: '1px solid #d1d5db',
    borderRadius: '8px',
    fontSize: '14px',
    outline: 'none',
    resize: 'none' as const,
    minHeight: '36px',
    maxHeight: '100px',
  },

  sendButton: {
    padding: '8px 16px',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: '500',
    outline: 'none',
    transition: 'all 0.2s ease',
  },

  sendButtonDisabled: {
    opacity: 0.5,
    cursor: 'not-allowed',
  },

  // Markdown styles
  markdownContent: {
    lineHeight: '1.6',
  },

  markdownH1: {
    fontSize: '18px',
    fontWeight: '700',
    margin: '16px 0 8px 0',
    color: 'inherit',
  },

  markdownH2: {
    fontSize: '16px',
    fontWeight: '600',
    margin: '14px 0 6px 0',
    color: 'inherit',
  },

  markdownH3: {
    fontSize: '15px',
    fontWeight: '600',
    margin: '12px 0 4px 0',
    color: 'inherit',
  },

  markdownP: {
    margin: '8px 0',
    color: 'inherit',
  },

  markdownUl: {
    margin: '8px 0',
    paddingLeft: '20px',
    color: 'inherit',
  },

  markdownOl: {
    margin: '8px 0',
    paddingLeft: '20px',
    color: 'inherit',
  },

  markdownLi: {
    margin: '4px 0',
    color: 'inherit',
  },

  markdownCode: {
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    padding: '2px 4px',
    borderRadius: '3px',
    fontSize: '13px',
    fontFamily: 'Monaco, Consolas, "Courier New", monospace',
    color: 'inherit',
  },

  markdownPre: {
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    padding: '12px',
    borderRadius: '6px',
    overflow: 'auto',
    margin: '8px 0',
    fontSize: '13px',
    fontFamily: 'Monaco, Consolas, "Courier New", monospace',
    color: 'inherit',
  },

  markdownBlockquote: {
    borderLeft: '3px solid #ddd',
    paddingLeft: '12px',
    margin: '8px 0',
    fontStyle: 'italic',
    color: 'inherit',
  },

  markdownA: {
    color: '#3b82f6',
    textDecoration: 'underline',
  },

  markdownStrong: {
    fontWeight: '600',
    color: 'inherit',
  },

  markdownEm: {
    fontStyle: 'italic',
    color: 'inherit',
  },
}

// Markdown Renderer Component
function MarkdownRenderer({ content, primaryColor }: { content: string; primaryColor: string }) {
  const createMarkup = useCallback(() => {
    try {
      // Configure marked options
      marked.setOptions({
        breaks: true,
        gfm: true,
      })

      // Parse markdown to HTML (marked.parse is synchronous)
      const html = marked.parse(content) as string

      // Apply custom styles by wrapping elements
      const styledHtml = html
        .replace(/<h1>/g, `<h1 style="font-size: 18px; font-weight: 700; margin: 16px 0 8px 0; color: inherit;">`)
        .replace(/<h2>/g, `<h2 style="font-size: 16px; font-weight: 600; margin: 14px 0 6px 0; color: inherit;">`)
        .replace(/<h3>/g, `<h3 style="font-size: 15px; font-weight: 600; margin: 12px 0 4px 0; color: inherit;">`)
        .replace(/<p>/g, `<p style="margin: 8px 0; color: inherit;">`)
        .replace(/<ul>/g, `<ul style="margin: 8px 0; padding-left: 20px; color: inherit;">`)
        .replace(/<ol>/g, `<ol style="margin: 8px 0; padding-left: 20px; color: inherit;">`)
        .replace(/<li>/g, `<li style="margin: 4px 0; color: inherit;">`)
        .replace(/<code>/g, `<code style="background-color: rgba(0, 0, 0, 0.1); padding: 2px 4px; border-radius: 3px; font-size: 13px; font-family: Monaco, Consolas, 'Courier New', monospace; color: inherit;">`)
        .replace(/<pre>/g, `<pre style="background-color: rgba(0, 0, 0, 0.1); padding: 12px; border-radius: 6px; overflow: auto; margin: 8px 0; font-size: 13px; font-family: Monaco, Consolas, 'Courier New', monospace; color: inherit;">`)
        .replace(/<blockquote>/g, `<blockquote style="border-left: 3px solid #ddd; padding-left: 12px; margin: 8px 0; font-style: italic; color: inherit;">`)
        .replace(/<a /g, `<a style="color: ${primaryColor}; text-decoration: underline;" `)
        .replace(/<strong>/g, `<strong style="font-weight: 600; color: inherit;">`)
        .replace(/<em>/g, `<em style="font-style: italic; color: inherit;">`)

      return { __html: styledHtml }
    } catch (error) {
      // Fallback to plain text if markdown parsing fails
      return { __html: content.replace(/\n/g, '<br>') }
    }
  }, [content, primaryColor])

  return (
    <div
      style={styles.markdownContent}
      dangerouslySetInnerHTML={createMarkup()}
    />
  )
}

// Widget Component
function WidgetV2({ config }: WidgetProps) {
  const [isOpen, setIsOpen] = useState(config.initiallyOpen || false)
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isHovered, setIsHovered] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const primaryColor = config.primaryColor || '#3b82f6'
  const position = config.position || 'bottom-right'

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [messages])

  // Add welcome message on first open
  useEffect(() => {
    if (isOpen && messages.length === 0 && config.welcomeMessage) {
      setMessages([{
        id: 'welcome',
        content: config.welcomeMessage,
        role: 'assistant',
        timestamp: new Date(),
      }])
    }
  }, [isOpen, messages.length, config.welcomeMessage])

  const handleSendMessage = useCallback(async () => {
    if (!inputValue.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      role: 'user',
      timestamp: new Date(),
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsLoading(true)

    // Create a placeholder assistant message that will be updated
    const assistantMessageId = (Date.now() + 1).toString()
    const assistantMessage: Message = {
      id: assistantMessageId,
      content: '',
      role: 'assistant',
      timestamp: new Date(),
    }

    setMessages(prev => [...prev, assistantMessage])

    try {
      const response = await fetch(`${config.baseUrl}/api/chat/${config.websiteId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages: [...messages, userMessage].map(m => ({
            role: m.role,
            content: m.content,
          })),
          websiteId: config.websiteId,
          visitorId: config.visitorId,
        }),
      })

      if (!response.ok) throw new Error('Failed to send message')

      // Handle Mastra streaming response
      const reader = response.body?.getReader()
      const decoder = new TextDecoder()
      let fullContent = ''

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value, { stream: true })
          const lines = chunk.split('\n')

          for (const line of lines) {
            if (line.startsWith('0:')) {
              // Text content from Mastra stream
              const content = line.slice(2)
              fullContent += content

              // Hide "Thinking..." as soon as we get the first content
              if (isLoading && content.trim()) {
                setIsLoading(false)
              }

              // Debug: Log the raw content to help identify quote issues
              if (fullContent.includes('""')) {
                console.log('Raw content with quotes detected:', fullContent)
              }

              // Clean the content to remove extra quotes and formatting issues
              const cleanContent = fullContent
                // First, handle the specific pattern you mentioned: "Hello""! I""'""m"" here to assist"" you."
                .replace(/([a-zA-Z])""/g, '$1')          // Remove double quotes after letters
                .replace(/""([a-zA-Z])/g, '$1')          // Remove double quotes before letters
                .replace(/""([!?.,;:])/g, '$1')          // Remove double quotes before punctuation
                .replace(/([!?.,;:])""/g, '$1')          // Remove double quotes after punctuation
                .replace(/""'/g, "'")                    // Fix quote-apostrophe patterns
                .replace(/'""/g, "'")                    // Fix apostrophe-quote patterns
                // Remove various quote patterns
                .replace(/"""/g, '"')                    // Triple quotes to single
                .replace(/""/g, '')                      // Remove double quotes entirely
                .replace(/\\"/g, '"')                    // Escaped quotes
                .replace(/\\""/g, '"')                   // Escaped double quotes
                .replace(/"{2,}/g, '"')                  // Multiple consecutive quotes
                .replace(/^"(.*)"$/g, '$1')              // Remove surrounding quotes
                .replace(/^"(.*)"$/gm, '$1')             // Remove surrounding quotes per line
                // Handle JSON-like quote patterns
                .replace(/"([^"]*)""/g, '"$1"')          // Fix quote-quote patterns
                .replace(/""([^"]*)""/g, '"$1"')         // Fix double-quote wrapping
                .replace(/\\\\"/g, '"')                  // Double-escaped quotes
                // Handle specific patterns that might come from JSON stringification
                .replace(/"\s*"/g, '"')                  // Quotes with spaces
                .replace(/"\n"/g, '"\n')                 // Quotes around newlines
                .replace(/"\r"/g, '"\r')                 // Quotes around carriage returns
                // Clean up any remaining quote artifacts
                .replace(/([a-zA-Z])"([a-zA-Z])/g, '$1\'$2')  // Replace quotes between letters with apostrophes
                .replace(/\s+"/g, ' "')                  // Fix spacing before quotes
                .replace(/"\s+/g, '" ')                  // Fix spacing after quotes
                .trim()

              // Update the assistant message with cleaned accumulated content
              setMessages(prev => prev.map(msg =>
                msg.id === assistantMessageId
                  ? { ...msg, content: cleanContent }
                  : msg
              ))
            }
          }
        }
      }

      // If no content was received, show error
      if (!fullContent) {
        setMessages(prev => prev.map(msg =>
          msg.id === assistantMessageId
            ? { ...msg, content: 'Sorry, I encountered an error.' }
            : msg
        ))
      }

    } catch (error) {
      console.error('Error sending message:', error)

      // Update the assistant message with error content
      setMessages(prev => prev.map(msg =>
        msg.id === assistantMessageId
          ? { ...msg, content: 'Sorry, I encountered an error. Please try again.' }
          : msg
      ))
    } finally {
      // Always ensure loading is false when done
      setIsLoading(false)
    }
  }, [inputValue, isLoading, messages, config])

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const toggleChat = () => {
    setIsOpen(!isOpen)
  }

  const closeChat = () => {
    setIsOpen(false)
  }

  return (
    <div
      style={{
        ...styles.container,
        ...(position === 'bottom-left' ? styles.containerLeft : {}),
      }}
    >
      {/* Chat Window */}
      {isOpen && (
        <div
          style={{
            ...styles.chatWindow,
            ...(position === 'bottom-left' ? styles.chatWindowLeft : {}),
            ...styles.chatWindowOpen,
          }}
        >
          {/* Header */}
          <div style={styles.header}>
            <h3 style={styles.headerTitle}>
              {config.headerText || 'Chat Assistant'}
            </h3>
            <button
              style={styles.closeButton}
              onClick={closeChat}
              aria-label="Close chat"
            >
              ×
            </button>
          </div>

          {/* Messages */}
          <div style={styles.messagesContainer}>
            {messages.map((message) => (
              <div
                key={message.id}
                style={{
                  ...styles.message,
                  ...(message.role === 'user' ? styles.userMessage : styles.assistantMessage),
                  ...(message.role === 'user' ? { backgroundColor: primaryColor } : {}),
                }}
              >
                {message.role === 'assistant' ? (
                  <MarkdownRenderer content={message.content} primaryColor={primaryColor} />
                ) : (
                  message.content
                )}
              </div>
            ))}
            {isLoading && (
              <div style={{ ...styles.message, ...styles.assistantMessage }}>
                Thinking...
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div style={styles.inputContainer}>
            <textarea
              style={styles.input}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Type your message..."
              rows={1}
            />
            <button
              style={{
                ...styles.sendButton,
                backgroundColor: primaryColor,
                color: '#ffffff',
                ...((!inputValue.trim() || isLoading) ? styles.sendButtonDisabled : {}),
              }}
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
            >
              Send
            </button>
          </div>
        </div>
      )}

      {/* Bubble Button */}
      <button
        style={{
          ...styles.bubble,
          backgroundColor: primaryColor,
          ...(isHovered ? styles.bubbleHover : {}),
        }}
        onClick={toggleChat}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        aria-label={isOpen ? 'Close chat' : 'Open chat'}
      >
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          style={{ color: '#ffffff' }}
        >
          {isOpen ? (
            <path d="M18 6L6 18M6 6l12 12" />
          ) : (
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
          )}
        </svg>
      </button>
    </div>
  )
}

// Widget initialization function
export function initWidget({ shadowRoot, config }: { shadowRoot: ShadowRoot | HTMLElement, config: WidgetConfig }) {
  // Create container in shadow root
  const container = document.createElement('div')
  shadowRoot.appendChild(container)

  // Create React root and render
  const root = createRoot(container)
  root.render(<WidgetV2 config={config} shadowRoot={shadowRoot} />)

  // Return widget instance with API methods
  return {
    open() {
      // Implementation would trigger state change
    },
    close() {
      // Implementation would trigger state change
    },
    toggle() {
      // Implementation would trigger state change
    },
    destroy() {
      root.unmount()
      if (container.parentNode) {
        container.parentNode.removeChild(container)
      }
    }
  }
}
