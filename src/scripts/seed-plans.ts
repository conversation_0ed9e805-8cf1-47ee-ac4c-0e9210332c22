import { plansTable } from "@/lib/db/schema";
import * as dotenv from "dotenv";
import { eq } from "drizzle-orm";
import { drizzle } from "drizzle-orm/node-postgres";
import { Pool } from "pg";

// Load environment variables
dotenv.config();

/**
 * <PERSON><PERSON>t to seed the plans table with default plans
 */
async function seedPlans() {
	console.log("🌱 Seeding plans...");

	try {
		// Get the database connection string from environment variables
		const dbUrl = process.env.DATABASE_URL;
		if (!dbUrl) {
			console.error("Error: DATABASE_URL environment variable is not set");
			process.exit(1);
		}

		// Create a database connection
		const pool = new Pool({
			connectionString: dbUrl,
		});

		// Create the database instance
		const db = drizzle(pool, { schema: { plansTable } });

		console.log("Connected to database");

		// First, check if plans already exist
		const existingPlans = await db.select().from(plansTable);

		if (existingPlans.length > 0) {
			console.log(`Found ${existingPlans.length} existing plans.`);

			// Ask for confirmation to overwrite
			const readline = require("node:readline").createInterface({
				input: process.stdin,
				output: process.stdout,
			});

			const answer = await new Promise<string>((resolve) => {
				readline.question("Do you want to reset all plans? (y/N): ", resolve);
			});

			readline.close();

			if (answer.toLowerCase() !== "y") {
				console.log("Aborting. No changes made.");
				return;
			}

			// Delete all existing plans
			console.log("Deleting existing plans...");
			await db.delete(plansTable);
		}

		// Define default plans
		const defaultPlans = [
			{
				name: "Free",
				description: "Basic plan for getting started",
				price: 0, // Price in cents
				websiteLimit: 1,
				pagesPerWebsiteLimit: 50,
				messagesPerDayLimit: 100,
				features: "Basic chat widget, Website crawling, Standard response time",
				isActive: "ACTIVE",
			},
			{
				name: "Pro",
				description: "For growing businesses",
				price: 2900, // $29/month
				websiteLimit: 5,
				pagesPerWebsiteLimit: 200,
				messagesPerDayLimit: 1000,
				features:
					"Advanced chat widget, Priority crawling, Faster response time, Custom branding, Analytics",
				isActive: "ACTIVE",
			},
			{
				name: "Enterprise",
				description: "For large organizations",
				price: 9900, // $99/month
				websiteLimit: 20,
				pagesPerWebsiteLimit: 500,
				messagesPerDayLimit: 5000,
				features:
					"Premium chat widget, Unlimited crawling, Fastest response time, White labeling, Advanced analytics, Priority support",
				isActive: "ACTIVE",
			},
		];

		// Insert default plans
		console.log("Inserting default plans...");
		for (const plan of defaultPlans) {
			await db.insert(plansTable).values({
				...plan,
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			});
		}

		console.log("✅ Plans seeded successfully!");

		// Close the database connection
		await pool.end();
		console.log("Database connection closed");
	} catch (error) {
		console.error("Error seeding plans:", error);
		process.exit(1);
	}
}

// Run the seed function
seedPlans()
	.then(() => process.exit(0))
	.catch((error) => {
		console.error("Unhandled error:", error);
		process.exit(1);
	});
