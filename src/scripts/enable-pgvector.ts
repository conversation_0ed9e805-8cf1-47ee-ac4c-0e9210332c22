/**
 * <PERSON><PERSON><PERSON> to enable the pgvector extension in PostgreSQL
 *
 * This script connects to the PostgreSQL database and enables the pgvector extension,
 * which is required for vector search functionality.
 *
 * Usage:
 * pnpm db:pgvector
 */

import * as dotenv from "dotenv";
import { Client } from "pg";

// Load environment variables
dotenv.config();

async function enablePgVector() {
	console.log("Enabling pgvector extension...");

	// Get the database connection string from environment variables
	const dbUrl = process.env.DATABASE_URL;
	if (!dbUrl) {
		console.error("Error: DATABASE_URL environment variable is not set");
		process.exit(1);
	}

	// Create a PostgreSQL client
	const client = new Client({
		connectionString: dbUrl,
	});

	try {
		// Connect to the database
		await client.connect();
		console.log("Connected to database");

		// Check if pgvector extension is already installed
		const checkResult = await client.query(`
      SELECT * FROM pg_extension WHERE extname = 'vector';
    `);

		if (checkResult?.rowCount && checkResult.rowCount > 0) {
			console.log("pgvector extension is already enabled");
		} else {
			// Enable the pgvector extension
			await client.query("CREATE EXTENSION IF NOT EXISTS vector;");
			console.log("Successfully enabled pgvector extension");
		}

		// Create IVFFLAT indexes for efficient similarity search if they don't exist
		// First check if the tables exist
		const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('embeddings', 'website_embeddings');
    `);

		const tables = tablesResult.rows.map((row) => row.table_name);

		if (tables.includes("embeddings")) {
			// Check if index exists
			const embeddingsIndexResult = await client.query(`
        SELECT indexname FROM pg_indexes 
        WHERE tablename = 'embeddings' AND indexname = 'embeddings_embedding_idx';
      `);

			if (embeddingsIndexResult.rowCount === 0) {
				console.log("Creating index on embeddings table...");
				await client.query(`
          CREATE INDEX IF NOT EXISTS embeddings_embedding_idx 
          ON embeddings 
          USING ivfflat (embedding vector_cosine_ops) 
          WITH (lists = 100);
        `);
				console.log("Created index on embeddings table");
			} else {
				console.log("Index on embeddings table already exists");
			}
		}

		if (tables.includes("website_embeddings")) {
			// Check if index exists
			const websiteEmbeddingsIndexResult = await client.query(`
        SELECT indexname FROM pg_indexes 
        WHERE tablename = 'website_embeddings' AND indexname = 'website_embeddings_embedding_idx';
      `);

			if (websiteEmbeddingsIndexResult.rowCount === 0) {
				console.log("Creating index on website_embeddings table...");
				await client.query(`
          CREATE INDEX IF NOT EXISTS website_embeddings_embedding_idx 
          ON website_embeddings 
          USING ivfflat (embedding vector_cosine_ops) 
          WITH (lists = 100);
        `);
				console.log("Created index on website_embeddings table");
			} else {
				console.log("Index on website_embeddings table already exists");
			}
		}

		console.log("pgvector setup completed successfully");
	} catch (error) {
		console.error("Error enabling pgvector extension:", error);
		process.exit(1);
	} finally {
		// Close the database connection
		await client.end();
	}
}

// Run the script
enablePgVector();
