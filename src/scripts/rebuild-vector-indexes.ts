/**
 * <PERSON><PERSON><PERSON> to rebuild all vector indexes for optimal performance
 *
 * This script:
 * 1. Counts the number of vectors in each table
 * 2. Calculates optimal index parameters based on dataset size
 * 3. Drops and recreates indexes with optimized parameters
 *
 * Usage:
 * pnpm tsx src/scripts/rebuild-vector-indexes.ts
 */

import * as dotenv from "dotenv";
import { Client } from "pg";

// Load environment variables
dotenv.config();

async function rebuildVectorIndexes() {
	console.log("Starting vector index rebuild...");

	// Get the database connection string from environment variables
	const dbUrl = process.env.DATABASE_URL;
	if (!dbUrl) {
		console.error("Error: DATABASE_URL environment variable is not set");
		process.exit(1);
	}

	// Create a PostgreSQL client
	const client = new Client({
		connectionString: dbUrl,
	});

	try {
		// Connect to the database
		await client.connect();
		console.log("Connected to PostgreSQL database");

		// Check if pgvector extension is installed
		const extensionResult = await client.query(
			"SELECT * FROM pg_extension WHERE extname = 'vector'",
		);

		if (extensionResult.rowCount === 0) {
			console.error("Error: pgvector extension is not installed");
			process.exit(1);
		}

		// Get a list of tables with vector columns
		const tablesResult = await client.query(`
      SELECT table_name, column_name
      FROM information_schema.columns
      WHERE data_type = 'vector'
    `);

		if (tablesResult.rowCount === 0) {
			console.log("No tables with vector columns found");
			process.exit(0);
		}

		// Process each table with vector columns
		for (const row of tablesResult.rows) {
			const tableName = row.table_name;
			const columnName = row.column_name;

			console.log(`Processing table: ${tableName}, column: ${columnName}`);

			// Count the number of vectors in the table
			const countResult = await client.query(
				`SELECT COUNT(*) as count FROM ${tableName}`,
			);

			const count = Number.parseInt(countResult.rows[0].count, 10);
			console.log(`Found ${count} vectors in ${tableName} table`);

			// Calculate optimal number of lists based on dataset size
			let lists = 100; // Default

			if (count < 1000) {
				lists = 10; // Small dataset
			} else if (count < 10000) {
				lists = Math.ceil(Math.sqrt(count)); // Medium dataset
			} else if (count < 100000) {
				lists = Math.ceil(count / 100); // Large dataset
			} else {
				lists = Math.ceil(count / 200); // Very large dataset
			}

			console.log(`Calculated optimal lists parameter: ${lists}`);

			// Get the index name
			const indexResult = await client.query(`
        SELECT indexname
        FROM pg_indexes
        WHERE tablename = '${tableName}'
        AND indexdef LIKE '%USING ivfflat%'
      `);

			if (indexResult.rowCount && indexResult.rowCount > 0) {
				const indexName = indexResult.rows[0].indexname;

				// Drop the existing index
				console.log(`Dropping existing index: ${indexName}`);
				await client.query(`DROP INDEX IF EXISTS ${indexName}`);
			}

			// Create a new index with optimized parameters
			const newIndexName = `${tableName}_${columnName}_idx`;
			console.log(`Creating new index: ${newIndexName} with lists=${lists}`);

			await client.query(`
        CREATE INDEX ${newIndexName}
        ON ${tableName}
        USING ivfflat (${columnName} vector_cosine_ops)
        WITH (lists = ${lists})
      `);

			// Analyze the table to update statistics
			console.log(`Analyzing table: ${tableName}`);
			await client.query(`ANALYZE ${tableName}`);

			console.log(`Successfully rebuilt index for ${tableName}.${columnName}`);
		}

		console.log("Vector index rebuild completed successfully");
	} catch (error) {
		console.error("Error rebuilding vector indexes:", error);
		process.exit(1);
	} finally {
		// Close the database connection
		await client.end();
	}
}

// Run the script
rebuildVectorIndexes();
