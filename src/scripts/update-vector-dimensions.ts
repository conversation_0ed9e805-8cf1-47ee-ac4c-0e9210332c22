import { Client } from "pg";
import "dotenv/config";

async function updateVectorDimensions() {
	const client = new Client({
		connectionString: process.env.DATABASE_URL,
	});

	try {
		await client.connect();
		console.log("Connected to database");

		// Update embeddings table
		await client.query(`
      ALTER TABLE embeddings 
      ALTER COLUMN embedding 
      SET DATA TYPE vector(1024)
    `);
		console.log("Updated embeddings table");

		// Update website_embeddings table
		await client.query(`
      ALTER TABLE website_embeddings 
      ALTER COLUMN embedding 
      SET DATA TYPE vector(1024)
    `);
		console.log("Updated website_embeddings table");

		console.log("Successfully updated vector dimensions");
	} catch (error) {
		console.error("Error updating vector dimensions:", error);
	} finally {
		await client.end();
	}
}

updateVectorDimensions();
