import { count, desc, eq } from "drizzle-orm";
import { db } from "../lib/db";
import { conversationsTable, messagesTable } from "../lib/db/schema";

async function main() {
	try {
		// Count conversations
		const conversationCount = await db
			.select({ count: count() })
			.from(conversationsTable);

		console.log(`Total conversations: ${conversationCount[0]?.count || 0}`);

		// If there are conversations, get some details
		if (conversationCount[0]?.count > 0) {
			// Get the most recent conversations
			const conversations = await db
				.select()
				.from(conversationsTable)
				.orderBy(desc(conversationsTable.createdAt))
				.limit(5);

			console.log("\nMost recent conversations:");
			for (const conversation of conversations) {
				console.log(
					`- ID: ${conversation.id}, Website: ${conversation.websiteId}, Visitor: ${conversation.visitorId}, Status: ${conversation.status}`,
				);

				// Count messages for this conversation
				const messageCount = await db
					.select({ count: count() })
					.from(messagesTable)
					.where(eq(messagesTable.conversationId, conversation.id));

				console.log(`  Message count: ${messageCount[0]?.count || 0}`);

				// Get a sample of messages
				if (messageCount[0]?.count > 0) {
					const messages = await db
						.select()
						.from(messagesTable)
						.where(eq(messagesTable.conversationId, conversation.id))
						.orderBy(desc(messagesTable.createdAt))
						.limit(3);

					console.log("  Recent messages:");
					for (const message of messages) {
						console.log(
							`    - Role: ${
								message.role
							}, Content: ${message.content.substring(0, 50)}${
								message.content.length > 50 ? "..." : ""
							}`,
						);
					}
				}
			}

			// Check for specific website ID
			const websiteId = "8c6ced22-b93d-4813-9129-f2cb14917dfa";
			console.log(`\nChecking conversations for website ID: ${websiteId}`);

			const websiteConversations = await db
				.select()
				.from(conversationsTable)
				.where(eq(conversationsTable.websiteId, websiteId))
				.orderBy(desc(conversationsTable.createdAt))
				.limit(5);

			console.log(
				`Found ${websiteConversations.length} conversations for this website`,
			);

			for (const conversation of websiteConversations) {
				console.log(
					`- ID: ${conversation.id}, Visitor: ${conversation.visitorId}, Status: ${conversation.status}`,
				);

				// Count messages for this conversation
				const messageCount = await db
					.select({ count: count() })
					.from(messagesTable)
					.where(eq(messagesTable.conversationId, conversation.id));

				console.log(`  Message count: ${messageCount[0]?.count || 0}`);
			}
		}
	} catch (error) {
		console.error("Error checking conversations:", error);
		if (error instanceof Error) {
			console.error(`Error name: ${error.name}, message: ${error.message}`);
			console.error(`Stack trace: ${error.stack}`);
		}
	} finally {
		process.exit(0);
	}
}

main();
