/**
 * <PERSON>ript to test vector search functionality
 *
 * Usage:
 * pnpm test:vector-search:direct --websiteId=<websiteId> --query="<search query>"
 */

import { websitesTable } from "@/lib/db/schema";
import { WebsiteRagService } from "@/lib/services/rag/website-rag-service";
import * as dotenv from "dotenv";
import { eq } from "drizzle-orm";
import { drizzle } from "drizzle-orm/node-postgres";
import { Pool } from "pg";

// Load environment variables
dotenv.config();

async function main() {
	try {
		// Parse command line arguments
		const args = process.argv.slice(2);
		const websiteIdArg = args.find((arg) => arg.startsWith("--websiteId="));
		const queryArg = args.find((arg) => arg.startsWith("--query="));

		if (!websiteIdArg) {
			console.error("Error: --websiteId is required");
			console.log(
				'Usage: pnpm test:vector-search:direct --websiteId=<websiteId> --query="<search query>"',
			);
			process.exit(1);
		}

		const websiteId = websiteIdArg.split("=")[1];
		const query = queryArg
			? queryArg.split("=")[1]
			: "What is this website about?";

		// Create a database connection
		const pool = new Pool({
			connectionString: process.env.DATABASE_URL,
		});

		// Create a Drizzle instance
		const db = drizzle(pool);

		// Get the website
		const [website] = await db
			.select()
			.from(websitesTable)
			.where(eq(websitesTable.id, websiteId));

		if (!website) {
			console.error(`Error: Website with ID ${websiteId} not found`);
			process.exit(1);
		}

		console.log(
			`Testing vector search for website: ${website.name} (${website.url})`,
		);
		console.log(`Query: "${query}"`);

		// Create a RAG service instance
		const ragService = new WebsiteRagService();

		// Search for similar content
		console.log("Searching for similar content...");
		const similarContent = await ragService.searchSimilarContent(
			websiteId,
			query,
			5, // Limit to 5 results
		);

		// Display the results
		console.log("\nSearch Results:");
		console.log("==============");

		if (similarContent.length === 0) {
			console.log(
				"No results found. Make sure the website has been crawled and embeddings have been generated.",
			);
		} else {
			similarContent.forEach((result, index) => {
				console.log(`\nResult ${index + 1}:`);
				console.log(`Title: ${result.title}`);
				console.log(`URL: ${result.url}`);
				console.log(`Similarity: ${result.similarity.toFixed(4)}`);
				console.log(`Text: ${result.text.substring(0, 200)}...`);
			});
		}

		// Close the database connection
		await pool.end();
	} catch (error) {
		console.error("Error testing vector search:", error);
		process.exit(1);
	}
}

// Run the script
main();
