# Inngest Integration

This directory contains the Inngest integration for background job processing in the Bubl application.

## Overview

Inngest is used to offload CPU-intensive tasks like web crawling to background processes, ensuring the main application remains responsive. The integration includes:

1. **Client Configuration**: Setup for connecting to Inngest
2. **Functions**: Background job definitions
3. **API Route**: Endpoint for Inngest to communicate with the application

## Directory Structure

```
src/inngest/
├── client.ts             # Inngest client configuration
├── functions/            # Background job definitions
│   ├── crawler.ts        # Website crawler functions
│   └── index.ts          # Function exports
└── README.md             # Documentation
```

## Functions

### Crawler Functions

- `crawlWebsite`: Crawls a website based on its configuration
- `scheduledCrawl`: Runs daily to check for websites that need to be crawled based on their crawl frequency
- `triggerScheduledCrawl`: Manually triggers the scheduled crawl check

## Environment Variables

The following environment variables are used for Inngest:

- `INNGEST_EVENT_KEY`: Your Inngest event key (required in production)
- `INNGEST_SIGNING_KEY`: Your Inngest signing key (required in production)

## Development

To run the Inngest dev server locally:

```bash
pnpm inngest:dev
```

This will start the Inngest dev server and connect it to your local Next.js application.

## Events

The following events are used:

- `crawler/website.crawl`: Triggered when a website needs to be crawled
- `crawler/schedule.trigger`: Manually triggers the scheduled crawl check

## Usage

### Triggering a Crawl

To trigger a crawl for a website:

```typescript
import { inngest } from "@/inngest/client";

await inngest.send({
  name: "crawler/website.crawl",
  data: {
    websiteId: "website-id",
    configuration: {
      // Optional configuration overrides
    },
  },
});
```

### Manually Triggering Scheduled Crawls

To manually trigger the scheduled crawl check:

```typescript
import { inngest } from "@/inngest/client";

await inngest.send({
  name: "crawler/schedule.trigger",
  data: {},
});
```
