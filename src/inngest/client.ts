import { env } from "@/env";
import { Inngest } from "inngest";

/**
 * Create a client to send and receive events
 *
 * In production, you'll need to set the following environment variables:
 * - INNGEST_EVENT_KEY: Your Inngest event key
 * - INNGEST_SIGNING_KEY: Your Inngest signing key
 */
export const inngest = new Inngest({
	id: "bubl",
	// Optional: Add authentication for production environments
	...(env.NODE_ENV === "production" && {
		eventKey: env.INNGEST_EVENT_KEY,
		signingKey: env.INNGEST_SIGNING_KEY,
	}),
});
