import {
	crawlOperationsRepository,
	websitesRepository,
} from "@/lib/db/repositories";
import { CrawlStatus } from "@/lib/db/schema/crawlOperations";
import { WebsiteStatus } from "@/lib/db/schema/websites";
import { WebsiteCrawler } from "@/lib/services/crawler/website-crawler";
import { isFirstDayOfMonth, isSunday } from "date-fns";
import { inngest } from "../client";

/**
 * Crawl a website
 *
 * This function is triggered by the crawler/website.crawl event
 * It handles the entire crawling process:
 * 1. Gets the website from the database
 * 2. Creates or gets a crawl operation
 * 3. Updates the website status to CRAWLING
 * 4. Performs the actual crawl using the WebsiteCrawler
 * 5. Updates the website and operation status based on the result
 */
// Define the type for the crawler event data
interface CrawlWebsiteEventData {
	websiteId: string;
	configuration?: Record<string, unknown>;
	operationId?: string;
}

export const crawlWebsite = inngest.createFunction(
	{ id: "crawl-website" },
	{ event: "crawler/website.crawl" },
	async ({ event, step, logger }) => {
		const { websiteId, configuration, operationId } =
			event.data as CrawlWebsiteEventData;

		logger.info(`Starting crawl for website ${websiteId}`);

		try {
			// Get the website
			const website = await step.run("get-website", async () => {
				return await websitesRepository.getById(websiteId);
			});

			if (!website) {
				throw new Error(`Website with ID ${websiteId} not found`);
			}

			// Get or create a crawl operation
			const operation = await step.run(
				"get-or-create-crawl-operation",
				async () => {
					// Check if an operation ID was provided
					if (event.data.operationId) {
						// Get the existing operation
						const existingOperation = await crawlOperationsRepository.getById(
							event.data.operationId,
						);
						if (existingOperation) {
							return existingOperation;
						}
					}

					// Create a new operation if none was provided or if the provided one doesn't exist
					return await crawlOperationsRepository.create({
						websiteId: website.id,
						status: CrawlStatus.PENDING,
						startedAt: new Date().toISOString(),
						pagesSucceeded: 0,
						pagesFailed: 0,
					});
				},
			);

			// Update website status to CRAWLING
			await step.run("update-website-status-to-crawling", async () => {
				await websitesRepository.updateStatus(
					website.id,
					WebsiteStatus.CRAWLING,
				);
			});

			// Perform the actual crawl
			await step.run("perform-crawl", async () => {
				// Merge the website's stored configuration with the provided configuration
				const completeConfig = {
					...((website.crawlConfiguration as Record<string, unknown>) || {}),
					...(configuration || {}),
					crawlOperationId: operation.id,
				};

				// Create a new crawler instance
				const crawler = new WebsiteCrawler(website, completeConfig);

				// Start the crawl
				await crawler.crawl();
			});

			// Get the updated operation to get the counts
			const updatedOperation = await step.run(
				"get-updated-operation",
				async () => {
					return await crawlOperationsRepository.getById(operation.id);
				},
			);

			// Update the website and operation status based on the result
			await step.run("update-statuses", async () => {
				// The operation should already be updated by the crawler, but we'll update it again to be sure
				await crawlOperationsRepository.update(operation.id, {
					status: CrawlStatus.COMPLETED,
					completedAt: new Date().toISOString(),
				});

				// Update the website
				await websitesRepository.update(website.id, {
					status: WebsiteStatus.ACTIVE,
					lastCrawledAt: new Date().toISOString(),
				});
			});

			return {
				success: true,
				websiteId: website.id,
				operationId: operation.id,
				pagesSucceeded: updatedOperation?.pagesSucceeded ?? 0,
				pagesFailed: updatedOperation?.pagesFailed ?? 0,
			};
		} catch (error) {
			logger.error(`Error crawling website ${websiteId}:`, error);

			// Update the website status to ERROR if possible
			try {
				await websitesRepository.updateStatus(websiteId, WebsiteStatus.ERROR);
			} catch (updateError) {
				logger.error("Error updating website status:", updateError);
			}

			throw error;
		}
	},
);

/**
 * Scheduled crawl function
 *
 * This function runs daily at midnight and checks for websites that need to be crawled
 * based on their crawl frequency:
 * - DAILY: Crawled every day
 * - WEEKLY: Crawled on Sundays
 * - MONTHLY: Crawled on the 1st of each month
 * - MANUAL: Only crawled when manually triggered
 */
export const scheduledCrawl = inngest.createFunction(
	{ id: "scheduled-crawl" },
	{ cron: "0 0 * * *" }, // Run once per day at midnight
	async ({ step, logger }) => {
		logger.info("Running scheduled crawl check");

		const today = new Date();
		const isWeeklyCrawlDay = isSunday(today);
		const isMonthlyCrawlDay = isFirstDayOfMonth(today);

		// Get websites by crawl frequency
		const dailyWebsites = await step.run("get-daily-websites", async () => {
			return await websitesRepository.getWebsitesByCrawlFrequency("DAILY");
		});

		// Only get weekly websites on Sundays
		const weeklyWebsites = isWeeklyCrawlDay
			? await step.run("get-weekly-websites", async () => {
					return await websitesRepository.getWebsitesByCrawlFrequency("WEEKLY");
				})
			: [];

		// Only get monthly websites on the 1st of the month
		const monthlyWebsites = isMonthlyCrawlDay
			? await step.run("get-monthly-websites", async () => {
					return await websitesRepository.getWebsitesByCrawlFrequency(
						"MONTHLY",
					);
				})
			: [];

		// Combine all websites to crawl
		const websitesToCrawl = [
			...dailyWebsites,
			...weeklyWebsites,
			...monthlyWebsites,
		];

		logger.info(
			`Found ${websitesToCrawl.length} websites to crawl (${dailyWebsites.length} daily, ${weeklyWebsites.length} weekly, ${monthlyWebsites.length} monthly)`,
		);

		// Skip websites that are already being crawled
		const filteredWebsites = await step.run(
			"filter-already-crawling",
			async () => {
				return websitesToCrawl.filter(
					(website) => website.status !== WebsiteStatus.CRAWLING,
				);
			},
		);

		// Trigger crawl for each website
		const results = [];
		for (const website of filteredWebsites) {
			try {
				// Create a crawl operation first
				const operation = await step.run(
					`create-operation-${website.id}`,
					async () => {
						return await crawlOperationsRepository.create({
							websiteId: website.id,
							status: CrawlStatus.PENDING,
							startedAt: new Date().toISOString(),
							pagesSucceeded: 0,
							pagesFailed: 0,
						});
					},
				);

				// Send event to trigger crawl
				await step.run(`trigger-crawl-${website.id}`, async () => {
					await inngest.send({
						name: "crawler/website.crawl",
						data: {
							websiteId: website.id,
							configuration: website.crawlConfiguration || {},
							operationId: operation.id, // Pass the operation ID
						},
					});
				});

				results.push({
					websiteId: website.id,
					status: "triggered",
				});
			} catch (error) {
				logger.error(
					`Error triggering crawl for website ${website.id}:`,
					error,
				);
				results.push({
					websiteId: website.id,
					status: "failed",
					error: error instanceof Error ? error.message : String(error),
				});
			}
		}

		return {
			success: true,
			crawlsTriggered: results.filter((r) => r.status === "triggered").length,
			crawlsFailed: results.filter((r) => r.status === "failed").length,
			crawlsSkipped: websitesToCrawl.length - filteredWebsites.length,
			websites: results,
		};
	},
);

/**
 * Trigger scheduled crawl manually
 *
 * This function allows manually triggering the scheduled crawl check
 * It's useful for testing and for manually running the scheduled crawl
 * outside of its normal schedule
 */
export const triggerScheduledCrawl = inngest.createFunction(
	{ id: "trigger-scheduled-crawl" },
	{ event: "crawler/schedule.trigger" },
	async ({ step, logger }) => {
		logger.info("Manually triggering scheduled crawl check");

		// Get websites by crawl frequency
		const dailyWebsites = await step.run("get-daily-websites", async () => {
			return await websitesRepository.getWebsitesByCrawlFrequency("DAILY");
		});

		const weeklyWebsites = await step.run("get-weekly-websites", async () => {
			return await websitesRepository.getWebsitesByCrawlFrequency("WEEKLY");
		});

		const monthlyWebsites = await step.run("get-monthly-websites", async () => {
			return await websitesRepository.getWebsitesByCrawlFrequency("MONTHLY");
		});

		// Combine all websites to crawl
		const websitesToCrawl = [
			...dailyWebsites,
			...weeklyWebsites,
			...monthlyWebsites,
		];

		// Trigger crawl for each website
		const results = [];
		for (const website of websitesToCrawl) {
			try {
				// Create a crawl operation first
				const operation = await step.run(
					`create-operation-${website.id}`,
					async () => {
						return await crawlOperationsRepository.create({
							websiteId: website.id,
							status: CrawlStatus.PENDING,
							startedAt: new Date().toISOString(),
							pagesSucceeded: 0,
							pagesFailed: 0,
						});
					},
				);

				// Send event to trigger crawl
				await step.run(`trigger-crawl-${website.id}`, async () => {
					await inngest.send({
						name: "crawler/website.crawl",
						data: {
							websiteId: website.id,
							configuration: website.crawlConfiguration || {},
							operationId: operation.id, // Pass the operation ID
						},
					});
				});

				results.push({
					websiteId: website.id,
					status: "triggered",
				});
			} catch (error) {
				logger.error(
					`Error triggering crawl for website ${website.id}:`,
					error,
				);
				results.push({
					websiteId: website.id,
					status: "failed",
					error: error instanceof Error ? error.message : String(error),
				});
			}
		}

		return {
			success: true,
			crawlsTriggered: results.filter((r) => r.status === "triggered").length,
			websites: results,
		};
	},
);
