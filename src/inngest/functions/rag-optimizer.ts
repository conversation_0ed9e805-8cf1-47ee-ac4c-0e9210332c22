import { websitesRepository } from "@/lib/db/repositories";
import { WebsiteStatus } from "@/lib/db/schema/websites";
import {
	rebuildVectorIndex,
	shouldRebuildIndex,
} from "@/lib/services/rag/rebuild-index";
import { WebsiteRagService } from "@/lib/services/rag/website-rag-service";
import { inngest } from "../client";

/**
 * Event data for optimizing RAG for a specific website
 */
interface OptimizeRagEventData {
	websiteId?: string; // Optional - if not provided, optimize all websites
	force?: boolean; // Force optimization even if not needed
}

/**
 * Event data for optimizing RAG for all websites
 */
interface OptimizeAllRagEventData {
	force?: boolean; // Force optimization even if not needed
}

/**
 * Optimize RAG for a specific website
 *
 * This function:
 * 1. Processes all pages for the website using optimal chunking strategies
 * 2. Rebuilds the vector index with optimal parameters
 * 3. Updates the website status
 */
export const optimizeRag = inngest.createFunction(
	{ id: "optimize-rag" },
	{ event: "rag/website.optimize" },
	async ({ event, step, logger }) => {
		const { websiteId, force = false } = event.data as OptimizeRagEventData;

		if (!websiteId) {
			logger.error("Missing websiteId in event data");
			return { error: "Missing websiteId" };
		}

		logger.info(`Starting RAG optimization for website ${websiteId}`);

		try {
			// Get the website
			const website = await step.run("get-website", async () => {
				return await websitesRepository.getById(websiteId);
			});

			if (!website) {
				throw new Error(`Website with ID ${websiteId} not found`);
			}

			// Update website status to CRAWLING (using CRAWLING as a proxy for processing)
			await step.run("update-website-status-to-processing", async () => {
				await websitesRepository.update(website.id, {
					status: WebsiteStatus.CRAWLING,
				});
			});

			// Create a RAG service instance
			const ragService = new WebsiteRagService();

			// Process all pages for the website
			await step.run("process-website-pages", async () => {
				await ragService.processWebsite(website);
			});

			// Check if we should rebuild the vector index
			const shouldRebuild =
				force ||
				(await step.run("check-if-rebuild-needed", async () => {
					return await shouldRebuildIndex();
				}));

			if (shouldRebuild) {
				// Rebuild the vector index
				await step.run("rebuild-vector-index", async () => {
					logger.info("Rebuilding vector index with optimal parameters");
					await rebuildVectorIndex();
				});
			}

			// Update website status to ACTIVE
			await step.run("update-website-status-to-active", async () => {
				await websitesRepository.update(website.id, {
					status: WebsiteStatus.ACTIVE,
				});
			});

			logger.info(`RAG optimization completed for website ${websiteId}`);
			return { success: true, websiteId };
		} catch (error) {
			logger.error(`Error optimizing RAG for website ${websiteId}:`, error);

			// Update website status to ERROR
			await step.run("update-website-status-to-error", async () => {
				await websitesRepository.update(websiteId, {
					status: WebsiteStatus.ERROR,
				});
			});

			return {
				error: `Failed to optimize RAG: ${
					error instanceof Error ? error.message : String(error)
				}`,
			};
		}
	},
);

/**
 * Optimize RAG for all websites
 *
 * This function:
 * 1. Gets all active websites
 * 2. Triggers optimization for each website
 */
export const optimizeAllRag = inngest.createFunction(
	{ id: "optimize-all-rag" },
	{ event: "rag/all.optimize" },
	async ({ event, step, logger }) => {
		const { force = false } = event.data as OptimizeAllRagEventData;

		logger.info("Starting RAG optimization for all websites");

		try {
			// Get all active websites
			const websites = await step.run("get-all-active-websites", async () => {
				try {
					return await websitesRepository.getAllActive();
				} catch (error) {
					logger.error("Error getting active websites:", error);
					return [];
				}
			});

			logger.info(`Found ${websites.length} active websites to optimize`);

			// Trigger optimization for each website
			const results = [];
			for (const website of websites) {
				try {
					await step.run(`trigger-optimize-${website.id}`, async () => {
						await inngest.send({
							name: "rag/website.optimize",
							data: {
								websiteId: website.id,
								force,
							},
						});
					});

					results.push({
						websiteId: website.id,
						status: "triggered",
					});
				} catch (error) {
					logger.error(
						`Error triggering optimization for website ${website.id}:`,
						error,
					);
					results.push({
						websiteId: website.id,
						status: "failed",
						error: error instanceof Error ? error.message : String(error),
					});
				}
			}

			logger.info(`Triggered RAG optimization for ${results.length} websites`);
			return { success: true, results };
		} catch (error) {
			logger.error("Error optimizing RAG for all websites:", error);
			return {
				error: `Failed to optimize RAG: ${
					error instanceof Error ? error.message : String(error)
				}`,
			};
		}
	},
);

/**
 * Scheduled RAG optimization
 *
 * This function runs weekly to optimize RAG for all websites
 */
export const scheduledRagOptimization = inngest.createFunction(
	{ id: "scheduled-rag-optimization" },
	{ cron: "0 0 * * 0" }, // Run once per week on Sunday at midnight
	async ({ step, logger }) => {
		logger.info("Running scheduled RAG optimization");

		try {
			// Trigger optimization for all websites
			await step.run("trigger-optimize-all", async () => {
				await inngest.send({
					name: "rag/all.optimize",
					data: {
						force: false, // Only optimize if needed
					},
				});
			});

			logger.info("Scheduled RAG optimization triggered successfully");
			return { success: true };
		} catch (error) {
			logger.error("Error triggering scheduled RAG optimization:", error);
			return {
				error: `Failed to trigger optimization: ${
					error instanceof Error ? error.message : String(error)
				}`,
			};
		}
	},
);
