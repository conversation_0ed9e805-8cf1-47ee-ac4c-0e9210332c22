import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { env } from "./env";

// Define public routes that don't require authentication
const isPublicRoute = createRouteMatcher([
	// Auth routes
	"/sign-in(.*)",
	"/sign-up(.*)",
	"/api/webhooks/clerk(.*)",

	// Widget and API routes
	"/widget(.*)",
	"/api/chat(.*)", // Make chat API routes public
	"/api/widget(.*)", // Make widget API routes public
	"/api/analytics(.*)", // Make analytics API routes public
	"/api/dashboard/websites/(.*)/conversations", // Make conversations API route public for debugging
	"/api/inngest(.*)", // Make Inngest API route public

	// Marketing pages
	"/", // Homepage
	"/about(.*)", // About page
	"/features(.*)", // Features page
	"/pricing(.*)", // Pricing page
	"/blog(.*)", // Blog pages
	"/contact(.*)", // Contact page
	"/privacy(.*)", // Privacy policy
	"/terms(.*)", // Terms of service
	"/changelog(.*)", // Changelog
	"/docs(.*)", // Documentation
	"/api-reference(.*)", // API reference
	"/support(.*)", // Support page
]);

const isDashboardRoute = createRouteMatcher(["/dashboard(.*)"]);

export default clerkMiddleware(async (auth, request) => {
	// Check if route requires authentication
	if (!isPublicRoute(request)) {
		await auth.protect();
	}

	if (isDashboardRoute(request)) {
		const user = await auth();
		if (!user.userId) {
			return NextResponse.redirect(new URL("/sign-in", request.url));
		}
	}

	// Check if this is an API route for CSRF protection
	const isApiRoute = request.nextUrl.pathname.startsWith("/api/");

	if (isApiRoute) {
		// Simple CSRF header presence check for state-changing requests
		const method = request.method.toUpperCase();
		const stateChangingMethods = ["POST", "PUT", "DELETE", "PATCH"];

		// Skip CSRF check for specific endpoints that don't need it
		const skipCsrfPaths = [
			"/api/webhooks/",
			"/api/chat/",
			"/api/widget/",
			"/api/analytics/",
			"/api/csrf",
			"/api/inngest", // Skip CSRF for Inngest API route
			"/api/dashboard/websites", // Skip CSRF for dashboard websites API (includes all nested routes)
			"/api/dashboard/metrics", // Skip CSRF for dashboard metrics API
			"/api/dashboard/plans", // Skip CSRF for dashboard plans API
		];

		// Check if the path should skip CSRF protection
		const shouldSkipCsrf = skipCsrfPaths.some((path) =>
			request.nextUrl.pathname.startsWith(path),
		);

		// Enhanced logging for all API requests
		console.log(
			`[API Request] Path: ${request.nextUrl.pathname}, Method: ${method}`,
		);
		console.log(`[API Request] Skip CSRF: ${shouldSkipCsrf ? "Yes" : "No"}`);

		if (stateChangingMethods.includes(method) && !shouldSkipCsrf) {
			const csrfToken = request.headers.get("X-CSRF-Token");

			// Enhanced logging for debugging
			// console.log(
			// 	`[CSRF Check] Path: ${request.nextUrl.pathname}, Method: ${method}`,
			// );
			// console.log(`[CSRF Check] Token present: ${csrfToken ? "Yes" : "No"}`);
			// console.log(
			// 	"[CSRF Check] Headers:",
			// 	Object.fromEntries([...request.headers.entries()]),
			// );
			// console.log("[CSRF Check] Cookies:", request.headers.get("cookie"));

			// TEMPORARY FIX: Allow requests without CSRF token in all environments
			// This is a temporary fix until we resolve the cookie issues
			if (!csrfToken) {
				// Log the issue but allow the request to proceed
				console.warn(
					`[CSRF Warning] Token missing for ${request.nextUrl.pathname} (temporarily allowed in all environments)`,
				);
				console.warn(`[CSRF Debug] Cookies: ${request.headers.get("cookie")}`);

				// Continue with the request instead of blocking it
				// We'll rely on other security measures like authentication in the meantime
			}
		}

		// Continue with the request if all checks pass
		return NextResponse.next();
	}

	// For non-API routes, continue with Clerk's default behavior
	return NextResponse.next();
});

export const config = {
	matcher: [
		// Clerk's default matcher, but exclude the widget route group and public assets
		"/((?!.+\\.[\\w]+$|_next|widget).*)",
		// Only run for protected API routes (exclude widget and chat API routes)
		"/(api(?!/widget|/chat))(.*)",
	],
};
