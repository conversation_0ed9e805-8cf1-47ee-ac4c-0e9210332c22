export type ChatRole = "user" | "assistant";
export type ChatStatus = "idle" | "thinking" | "error";
export type ChatPosition = "bottom-right" | "bottom-left";

export interface ChatMessage {
	id: string;
	content: string;
	role: ChatRole;
	timestamp: Date;
}

export interface ChatSession {
	id: string;
	messages: ChatMessage[];
	status: ChatStatus;
}

export interface ChatTheme {
	primaryColor?: string;
	fontFamily?: string;
	borderRadius?: string;
	backgroundColor?: string;
	textColor?: string;
}

export interface ChatWidgetProps {
	websiteId: string;
	theme?: ChatTheme;
	position?: ChatPosition;
	initialMessage?: string;
	title?: string;
	placeholder?: string;
}

export interface ChatHeaderProps {
	title: string;
	status: ChatStatus;
	onMinimize: () => void;
	isMinimized: boolean;
}

export interface ChatMessagesProps {
	messages: ChatMessage[];
	status: ChatStatus;
}

export interface ChatInputProps {
	onSend: (message: string) => void;
	disabled?: boolean;
	placeholder?: string;
}

export interface ChatBubbleProps {
	message: ChatMessage;
}
