"use client";

import { createContext, useContext, useEffect, useState } from "react";

interface SidebarContextType {
	isOpen: boolean;
	toggle: () => void;
	close: () => void;
	open: () => void;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export function SidebarProvider({ children }: { children: React.ReactNode }) {
	// Default to open on larger screens, closed on mobile
	const [isOpen, setIsOpen] = useState(true);

	// Check screen size on mount and when window resizes
	useEffect(() => {
		const checkScreenSize = () => {
			if (typeof window !== "undefined") {
				setIsOpen(window.innerWidth >= 768); // 768px is the md breakpoint in Tailwind
			}
		};

		// Set initial state
		checkScreenSize();

		// Add event listener for window resize
		window.addEventListener("resize", checkScreenSize);

		// Clean up
		return () => window.removeEventListener("resize", checkScreenSize);
	}, []);

	const toggle = () => setIsOpen((prev) => !prev);
	const close = () => setIsOpen(false);
	const open = () => setIsOpen(true);

	return (
		<SidebarContext.Provider value={{ isOpen, toggle, close, open }}>
			{children}
		</SidebarContext.Provider>
	);
}

export function useSidebar() {
	const context = useContext(SidebarContext);
	if (context === undefined) {
		throw new Error("useSidebar must be used within a SidebarProvider");
	}
	return context;
}
