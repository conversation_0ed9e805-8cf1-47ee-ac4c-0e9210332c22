import { api } from "@/lib/api/client";
import { useQuery } from "@tanstack/react-query";

// Define the plan usage type
export interface PlanUsage {
	websites: {
		count: number;
		limit: number;
	};
	pages: {
		count: number;
		limit: number;
	};
	messages: {
		count: number;
		limit: number;
	};
}

// Query key factory
const planUsageKeys = {
	all: ["planUsage"] as const,
};

// API functions
const planUsageApi = {
	getPlanUsage: () => api.get<PlanUsage>("/dashboard/user/plan/usage"),
};

// Hook to fetch the user's plan usage
export function usePlanUsage() {
	return useQuery({
		queryKey: planUsageKeys.all,
		queryFn: () => planUsageApi.getPlanUsage(),
		refetchInterval: 60000, // Refetch every minute
	});
}
