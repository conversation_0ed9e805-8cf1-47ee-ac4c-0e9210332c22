import { api } from "@/lib/api/client";
import { useQuery } from "@tanstack/react-query";
import type { Plan } from "./useUserPlan";

// Query key factory
const plansKeys = {
	all: ["plans"] as const,
};

// API functions
const plansApi = {
	getPlans: () => api.get<Plan[]>("/dashboard/plans"),
};

// Hook to fetch all available plans
export function usePlans() {
	return useQuery({
		queryKey: plansKeys.all,
		queryFn: () => plansApi.getPlans(),
	});
}
