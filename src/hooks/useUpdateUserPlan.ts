import { api } from "@/lib/api/client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

// Mutation input type
interface UpdateUserPlanInput {
	planId: string;
}

// API functions
const planApi = {
	updateUserPlan: (data: UpdateUserPlanInput) =>
		api.patch<void>("/dashboard/user/plan", data),
};

// Hook to update a user's plan
export function useUpdateUserPlan() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (data: UpdateUserPlanInput) => planApi.updateUserPlan(data),
		onSuccess: () => {
			// Invalidate relevant queries
			queryClient.invalidateQueries({ queryKey: ["userPlan"] });
		},
	});
}
