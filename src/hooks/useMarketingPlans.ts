import { useQuery } from "@tanstack/react-query";
import type { Plan } from "./useUserPlan";

// Query key factory
const marketingPlansKeys = {
	all: ["marketingPlans"] as const,
};

// API functions
const marketingPlansApi = {
	getPlans: async (): Promise<Plan[]> => {
		const response = await fetch("/api/plans");
		if (!response.ok) {
			throw new Error("Failed to fetch plans");
		}
		return response.json();
	},
};

// Hook to fetch all available plans for marketing pages
export function useMarketingPlans() {
	return useQuery({
		queryKey: marketingPlansKeys.all,
		queryFn: () => marketingPlansApi.getPlans(),
	});
}
