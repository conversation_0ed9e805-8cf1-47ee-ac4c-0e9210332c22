import { useQuery } from "@tanstack/react-query";

// Define the Message type
export interface Message {
	id: string;
	conversationId: string;
	content: string;
	role: string;
	sources?: Record<
		string,
		{
			url?: string;
			title?: string;
			content?: string;
			[key: string]: unknown;
		}
	>;
	createdAt: string;
}

// Define the Conversation type
export interface Conversation {
	id: string;
	websiteId: string;
	visitorId: string;
	status: string;
	endedAt?: string;
	referringUrl?: string;
	deviceInfo?: Record<string, unknown>;
	rating?: number;
	feedback?: string;
	createdAt: string;
	updatedAt: string;
	messages?: Message[];
}

// Define the pagination response type
export interface PaginatedResponse<T> {
	conversations: T[];
	pagination: {
		page: number;
		pageSize: number;
		total: number;
		pageCount: number;
	};
}

// Query key factory
export const conversationKeys = {
	all: ["conversations"] as const,
	lists: () => [...conversationKeys.all, "list"] as const,
	list: (filters: {
		websiteId: string;
		page: number;
		pageSize: number;
		includeMessages: boolean;
	}) => [...conversationKeys.lists(), filters] as const,
	details: () => [...conversationKeys.all, "detail"] as const,
	detail: (id: string) => [...conversationKeys.details(), id] as const,
};

// Fetch website conversations with pagination
export function useWebsiteConversations(
	websiteId: string,
	page = 1,
	pageSize = 10,
	includeMessages = false,
) {
	return useQuery<PaginatedResponse<Conversation>>({
		queryKey: conversationKeys.list({
			websiteId,
			page,
			pageSize,
			includeMessages,
		}),
		queryFn: async () => {
			const response = await fetch(
				`/api/dashboard/websites/${websiteId}/conversations?page=${page}&pageSize=${pageSize}&includeMessages=${includeMessages}`,
			);

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to fetch website conversations");
			}

			return response.json();
		},
	});
}

// Fetch a single conversation with messages
export function useConversation(conversationId: string) {
	return useQuery<{ conversation: Conversation; messages: Message[] }>({
		queryKey: conversationKeys.detail(conversationId),
		queryFn: async () => {
			const response = await fetch(`/api/chat/history/${conversationId}`);

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to fetch conversation");
			}

			return response.json();
		},
		enabled: !!conversationId,
	});
}
