import { useQuery } from "@tanstack/react-query";

// Define the WebsitePage type
export interface WebsitePage {
	id: string;
	websiteId: string;
	url: string;
	title: string;
	content: string;
	metadata: Record<string, unknown>;
	lastCrawledAt: string;
	createdAt: string;
	updatedAt: string;
}

// Define the pagination response type
export interface PaginatedResponse<T> {
	pages: T[];
	pagination: {
		page: number;
		pageSize: number;
		total: number;
		pageCount: number;
	};
}

// Query key factory
export const websitePageKeys = {
	all: ["websitePages"] as const,
	lists: () => [...websitePageKeys.all, "list"] as const,
	list: (filters: { websiteId: string; page: number; pageSize: number }) =>
		[...websitePageKeys.lists(), filters] as const,
	details: () => [...websitePageKeys.all, "detail"] as const,
	detail: (id: string) => [...websitePageKeys.details(), id] as const,
};

// Fetch website pages with pagination
export function useWebsitePages(websiteId: string, page = 1, pageSize = 10) {
	return useQuery<PaginatedResponse<WebsitePage>>({
		queryKey: websitePageKeys.list({ websiteId, page, pageSize }),
		queryFn: async () => {
			const response = await fetch(
				`/api/dashboard/websites/${websiteId}/pages?page=${page}&pageSize=${pageSize}`,
			);

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to fetch website pages");
			}

			return response.json();
		},
	});
}
