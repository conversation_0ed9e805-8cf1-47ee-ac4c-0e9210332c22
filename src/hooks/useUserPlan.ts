import { api } from "@/lib/api/client";
import { useQuery } from "@tanstack/react-query";

// Plan type
export interface Plan {
	id: string;
	name: string;
	description: string;
	price: number;
	websiteLimit: number;
	pagesPerWebsiteLimit: number;
	messagesPerDayLimit: number;
	features: string;
	isActive: string;
	subscriptionStatus?: string;
	subscriptionExpiresAt?: string;
}

// Query key factory
const planKeys = {
	userPlan: ["userPlan"] as const,
};

// API functions
const planApi = {
	getUserPlan: () => api.get<Plan>("/dashboard/user/plan"),
};

// Hook to fetch the user's plan
export function useUserPlan() {
	return useQuery({
		queryKey: planKeys.userPlan,
		queryFn: () => planApi.getUserPlan(),
	});
}
