"use client";

import { useCallback, useEffect, useState } from "react";

// CSRF token header name
const CSRF_HEADER_NAME = "X-CSRF-Token";
// Local storage key for caching the token
const CSRF_STORAGE_KEY = "csrf_token";
// Token expiration time (55 minutes to be safe, since the cookie is 1 hour)
const TOKEN_EXPIRATION = 55 * 60 * 1000;

/**
 * Hook to fetch and use CSRF tokens in forms
 * @returns CSRF token and header information
 */
export function useCsrfToken() {
	const [token, setToken] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<Error | null>(null);

	// Function to fetch a new token
	const fetchToken = useCallback(async (force = false) => {
		try {
			setIsLoading(true);

			// Check if we have a cached token and it's not expired
			if (!force) {
				const cachedToken = localStorage.getItem(CSRF_STORAGE_KEY);
				const tokenTimestamp = localStorage.getItem(
					`${CSRF_STORAGE_KEY}_timestamp`,
				);

				if (cachedToken && tokenTimestamp) {
					const timestamp = Number.parseInt(tokenTimestamp, 10);
					const now = Date.now();

					// If token is still valid (less than 55 minutes old)
					if (now - timestamp < TOKEN_EXPIRATION) {
						console.log("[CSRF] Using cached token");
						setToken(cachedToken);
						setIsLoading(false);
						return;
					}
					console.log("[CSRF] Cached token expired, fetching new one");
				}
			}

			// Fetch a new token
			console.log("[CSRF] Fetching new token");
			const response = await fetch("/api/csrf", {
				// Include credentials to ensure cookies are sent and received
				credentials: "include",
				// Cache control to prevent caching
				cache: "no-store",
				headers: {
					"Cache-Control": "no-cache, no-store, must-revalidate",
					Pragma: "no-cache",
					Expires: "0",
				},
			});

			if (!response.ok) {
				throw new Error(`Failed to fetch CSRF token: ${response.status}`);
			}

			const data = await response.json();

			// Store the token in state and localStorage with timestamp
			setToken(data.token);
			localStorage.setItem(CSRF_STORAGE_KEY, data.token);
			localStorage.setItem(
				`${CSRF_STORAGE_KEY}_timestamp`,
				Date.now().toString(),
			);

			console.log("[CSRF] New token fetched and stored");
		} catch (err) {
			console.error("[CSRF] Error fetching token:", err);
			setError(err instanceof Error ? err : new Error("Unknown error"));
		} finally {
			setIsLoading(false);
		}
	}, []);

	// Fetch token on component mount
	useEffect(() => {
		fetchToken();

		// Set up a refresh interval (every 50 minutes)
		const refreshInterval = setInterval(
			() => {
				console.log("[CSRF] Refreshing token on interval");
				fetchToken(true);
			},
			50 * 60 * 1000,
		);

		return () => clearInterval(refreshInterval);
	}, [fetchToken]);

	// Helper function to add CSRF token to fetch options
	const getRequestOptions = useCallback(
		(options: RequestInit = {}): RequestInit => {
			if (!token) {
				console.warn("[CSRF] No token available for request");
				return {
					...options,
					// Always include credentials
					credentials: "include",
				};
			}

			console.log("[CSRF] Adding token to request");

			// Get existing headers or create new ones
			const existingHeaders = options.headers || {};

			// Create a new headers object with all the properties
			const newHeaders = {
				...existingHeaders,
				// Add the CSRF token header
				[CSRF_HEADER_NAME]: token,
				// Add a custom header to help with debugging
				"X-CSRF-Debug": "true",
			};

			return {
				...options,
				// Always include credentials
				credentials: "include",
				headers: newHeaders,
			};
		},
		[token],
	);

	// Return the token, loading state, error, and header information
	return {
		token,
		isLoading,
		error,
		headerName: CSRF_HEADER_NAME,
		getRequestOptions,
		refreshToken: () => fetchToken(true),
	};
}
