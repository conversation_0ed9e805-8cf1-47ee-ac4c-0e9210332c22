import { websitesApi } from "@/lib/api";
import type {
	CreateWebsiteInput,
	UpdateWebsiteInput,
	Website,
} from "@/lib/api/types";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

// Query key factory
const websiteKeys = {
	all: ["websites"] as const,
	detail: (id: string) => [...websiteKeys.all, id] as const,
};

// Hook to fetch all websites
export function useWebsites(options?: { initialData?: Website[] }) {
	return useQuery({
		queryKey: websiteKeys.all,
		queryFn: () => websitesApi.getAll(),
		initialData: options?.initialData,
	});
}

// Hook to fetch a single website
export function useWebsite(id: string, options?: { initialData?: Website }) {
	return useQuery({
		queryKey: websiteKeys.detail(id),
		queryFn: () => websitesApi.getById(id),
		initialData: options?.initialData,
		enabled: !!id,
	});
}

// Hook to create a website
export function useCreateWebsite() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (data: CreateWebsiteInput) => websitesApi.create(data),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: websiteKeys.all });
		},
	});
}

// Hook to update a website
export function useUpdateWebsite(id: string) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (data: UpdateWebsiteInput) => websitesApi.update(id, data),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: websiteKeys.detail(id) });
			queryClient.invalidateQueries({ queryKey: websiteKeys.all });
		},
	});
}

// Hook to delete a website
export function useDeleteWebsite() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (id: string) => websitesApi.delete(id),
		onSuccess: (_: unknown, id: string) => {
			// Remove the specific website from the cache
			queryClient.removeQueries({ queryKey: websiteKeys.detail(id) });

			// Force refetch the websites list
			queryClient.invalidateQueries({
				queryKey: websiteKeys.all,
				refetchType: "active",
				exact: false,
			});

			// Update the websites list in the cache by removing the deleted website
			queryClient.setQueryData<Website[]>(websiteKeys.all, (oldData) => {
				if (!oldData) return [];
				return oldData.filter((website) => website.id !== id);
			});
		},
	});
}
