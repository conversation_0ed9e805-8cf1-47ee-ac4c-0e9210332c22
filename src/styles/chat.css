/* Chat UI Styles */

/* Chat Widget Container */
.chat-widget {
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px
		rgba(0, 0, 0, 0.04);
	border: 1px solid hsl(var(--border));
}

.chat-widget:focus-within {
	box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px
		rgba(0, 0, 0, 0.04);
}

/* Chat Messages Container */
.chat-messages-container {
	scroll-behavior: smooth;
	padding: 1rem;
	overflow-y: auto;
	overflow-x: hidden;
}

/* Message Animations */
@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.chat-message {
	animation: fadeIn 0.3s ease-out forwards;
	position: relative;
	margin-bottom: 1rem;
}

.chat-message:last-child {
	margin-bottom: 0;
}

/* Typing Indicator */
.typing-indicator {
	display: flex;
	align-items: center;
	gap: 3px;
}

.typing-indicator span {
	display: block;
	width: 8px;
	height: 8px;
	background-color: currentColor;
	border-radius: 50%;
	opacity: 0.6;
}

.typing-indicator span:nth-child(1) {
	animation: typing 1.4s infinite;
}

.typing-indicator span:nth-child(2) {
	animation: typing 1.4s 0.2s infinite;
}

.typing-indicator span:nth-child(3) {
	animation: typing 1.4s 0.4s infinite;
}

@keyframes typing {
	0% {
		transform: translateY(0px);
		opacity: 0.6;
	}
	50% {
		transform: translateY(-5px);
		opacity: 1;
	}
	100% {
		transform: translateY(0px);
		opacity: 0.6;
	}
}

/* Message Reactions */
.message-reactions {
	display: flex;
	gap: 4px;
	margin-top: 4px;
}

.reaction-button {
	padding: 2px 6px;
	border-radius: 12px;
	font-size: 12px;
	display: flex;
	align-items: center;
	gap: 4px;
	cursor: pointer;
	transition: all 0.2s ease;
	background-color: transparent;
	border: 1px solid var(--border);
}

.reaction-button:hover {
	background-color: var(--muted);
}

.reaction-button.active {
	background-color: var(--primary);
	color: var(--primary-foreground);
}

/* File Attachments */
.file-attachment {
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 8px;
	border-radius: 8px;
	background-color: var(--muted);
	margin-top: 8px;
}

.file-attachment-icon {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 32px;
	height: 32px;
	border-radius: 4px;
	background-color: var(--primary);
	color: var(--primary-foreground);
}

.file-attachment-info {
	flex: 1;
	overflow: hidden;
}

.file-attachment-name {
	font-size: 14px;
	font-weight: 500;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.file-attachment-size {
	font-size: 12px;
	color: var(--muted-foreground);
}

.file-attachment-download {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 24px;
	height: 24px;
	border-radius: 4px;
	background-color: var(--muted);
	color: var(--muted-foreground);
	cursor: pointer;
	transition: all 0.2s ease;
}

.file-attachment-download:hover {
	background-color: var(--primary);
	color: var(--primary-foreground);
}

/* Code Blocks */
.chat-code-block {
	position: relative;
	margin: 1rem 0;
	border-radius: 6px;
	overflow: hidden;
}

.chat-code-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0.5rem 1rem;
	background-color: hsl(var(--muted));
	color: hsl(var(--muted-foreground));
	font-size: 0.875rem;
	font-family: var(--font-mono);
}

.chat-code-copy-button {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 24px;
	height: 24px;
	border-radius: 4px;
	background-color: transparent;
	color: hsl(var(--muted-foreground));
	cursor: pointer;
	transition: all 0.2s ease;
}

.chat-code-copy-button:hover {
	background-color: hsl(var(--muted));
	color: hsl(var(--foreground));
}

.chat-code-content {
	padding: 1rem;
	background-color: hsl(var(--card));
	color: hsl(var(--card-foreground));
	font-family: var(--font-mono);
	font-size: 0.875rem;
	line-height: 1.5;
	overflow-x: auto;
}

/* Chat Header */
.chat-header {
	padding: 0.75rem 1rem;
	border-bottom: 1px solid hsl(var(--border));
	background-color: hsl(var(--background));
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
	z-index: 10;
}

.chat-header-title {
	font-weight: 600;
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.chat-status-indicator {
	width: 8px;
	height: 8px;
	border-radius: 50%;
	background-color: hsl(var(--muted-foreground));
}

.chat-status-indicator.online {
	background-color: hsl(var(--success, 142 76% 36%));
}

.chat-status-indicator.thinking {
	background-color: hsl(var(--warning, 48 96% 53%));
	animation: pulse 2s infinite;
}

.chat-status-indicator.error {
	background-color: hsl(var(--destructive, 0 84% 60%));
}

@keyframes pulse {
	0% {
		opacity: 0.6;
		transform: scale(0.8);
	}
	50% {
		opacity: 1;
		transform: scale(1.2);
	}
	100% {
		opacity: 0.6;
		transform: scale(0.8);
	}
}

/* Chat Input */
.chat-input-container {
	border-top: 1px solid hsl(var(--border));
	padding: 0.75rem;
	background-color: hsl(var(--background));
	position: sticky;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 10;
	box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
	backdrop-filter: blur(8px);
}

.chat-input-floating {
	position: absolute;
	bottom: 1rem;
	left: 1rem;
	right: 1rem;
	background-color: hsl(var(--background));
	border-radius: 1.5rem;
	padding: 0.5rem;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	border: 1px solid hsl(var(--border));
	z-index: 20;
	transition: all 0.3s ease;
}

.chat-input-floating:focus-within {
	box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
	transform: translateY(-2px);
}

.chat-input-inline {
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.chat-input-textarea {
	resize: none;
	min-height: 48px;
	max-height: 120px;
	padding-right: 40px;
	transition: all 0.2s ease;
	border-radius: 1.5rem;
	padding-left: 1rem;
	padding-right: 1rem;
}

.chat-input-textarea:focus {
	box-shadow: 0 0 0 2px hsl(var(--primary) / 0.3);
}

.chat-input-actions {
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.chat-input-button {
	border-radius: 50%;
	width: 36px;
	height: 36px;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
}

.chat-input-button:hover {
	background-color: hsl(var(--primary) / 0.1);
}

.chat-input-button:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

.chat-send-button {
	border-radius: 50%;
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: hsl(var(--primary));
	color: hsl(var(--primary-foreground));
	transition: all 0.2s ease;
	flex-shrink: 0;
}

.chat-send-button:hover {
	transform: scale(1.05);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.chat-send-button:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

/* Chat Toggle Button */
.chat-toggle-button {
	position: fixed;
	bottom: 1.5rem;
	width: 60px;
	height: 60px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	z-index: 50;
	border: none;
	cursor: pointer;
}

.chat-toggle-button:hover {
	transform: scale(1.05);
	box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.chat-toggle-button:active {
	transform: scale(0.95);
}

.chat-toggle-button.bottom-right {
	right: 1.5rem;
}

.chat-toggle-button.bottom-left {
	left: 1.5rem;
}

/* Keyboard Shortcuts */
.chat-keyboard-shortcuts {
	position: absolute;
	bottom: 0.5rem;
	right: 0.5rem;
	font-size: 0.75rem;
	color: hsl(var(--muted-foreground));
	opacity: 0.7;
}

/* Mobile Optimizations */
@media (max-width: 640px) {
	.chat-fullscreen {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		width: 100% !important;
		height: 100% !important;
		max-height: 100% !important;
		border-radius: 0 !important;
		z-index: 100;
	}

	.chat-toggle-button {
		width: 50px;
		height: 50px;
		bottom: 1rem;
	}

	.chat-toggle-button.bottom-right {
		right: 1rem;
	}

	.chat-toggle-button.bottom-left {
		left: 1rem;
	}

	.chat-input-container {
		padding: 0.5rem;
	}

	.chat-input-textarea {
		min-height: 50px;
	}
}
