#!/bin/sh

# Colors
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo "${YELLOW}🔍 Running build:check before committing...${NC}"

# Run the build check
if pnpm build:check; then
  echo "${GREEN}✅ Build check passed! Proceeding with commit.${NC}"
  exit 0
else
  echo "${RED}❌ Build check failed!${NC}"
  echo "${YELLOW}To bypass this check and commit anyway, use:${NC}"
  echo "  ${GREEN}git commit --no-verify${NC}"
  exit 1
fi
